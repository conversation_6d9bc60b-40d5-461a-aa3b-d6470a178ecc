# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

SnapInspect 3 is a professional inspection management iOS app with a hybrid Objective-C/Swift architecture supporting multi-user accounts with isolated data storage.

## Build Commands

### Workspace & Project Structure
- **Main workspace:** `SnapInspect3.xcworkspace` (use this for builds)
- **Main app scheme:** `SnapInspect3`
- **Swift Package targets:** `AppFeatures`, `Extensions`

### Building
```bash

# Build for iOS Simulator
xcodebuild -workspace SnapInspect3.xcworkspace -scheme SnapInspect3 -destination 'platform=iOS Simulator,name=iPhone 16 Pro'

# Build for iOS Device
xcodebuild -workspace SnapInspect3.xcworkspace -scheme SnapInspect3 -destination generic/platform=iOS
```

### Testing
```bash
# Run all tests
xcodebuild test -workspace SnapInspect3.xcworkspace -scheme SnapInspect3 -destination 'platform=iOS Simulator,name=iPhone 16 Pro'

# Run specific test suites
xcodebuild test -workspace SnapInspect3.xcworkspace -scheme AppFeaturesTests -destination 'platform=iOS Simulator,name=iPhone 16 Pro'
xcodebuild test -workspace SnapInspect3.xcworkspace -scheme ExtensionsTests -destination 'platform=iOS Simulator,name=iPhone 16 Pro'
```

### Dependencies
```bash
# Update CocoaPods dependencies
pod install

# Resolve Swift Package Manager dependencies
xcodebuild -resolvePackageDependencies -workspace SnapInspect3.xcworkspace
```

## Architecture Overview

### Hybrid Code Organization
- **Legacy Layer (Objective-C):** `TopInspect/` - Traditional MVC with inspection logic, database operations, UI controllers
- **Modern Layer (Swift):** `SnapInspect/` - Swift Package modules using MVVM + Composable Architecture (TCA)

### Multi-User Account System
The app supports multiple isolated user accounts:

- **Main Accounts:** Use UserDefaults for preferences and shared `IF_Data.db` database
- **Sub Accounts:** Each gets isolated directory with PropertyList storage and separate database at `user_{customerID}/IF_Data.db`

**Key Components:**
- `AccountManager` - Manages account switching and data isolation
- `PrefsManager` - Handles account-specific preference storage
- `DatabaseContextManager` - Manages database connections per user

### Database Architecture
- **FMDB (SQLite)** for data persistence
- **Entity Pattern:** Core models use `O_` prefix (`O_Asset`, `O_Inspection`, `O_Task`, etc.)
- **Data Access:** `db_*` classes handle operations (`db_Asset`, `db_Inspection`)
- **Thread Safety:** Database operations centralized through `AppEnvironment.runSQL`

### Preference Management
```objective-c
// Legacy preference methods (current user context)
[CommonHelper IFGetPref:@"key"]
[CommonHelper IFSavePref:@"key" sValue:@"value"]

// Main user preference methods (always main account)
[CommonHelper getMainUserPref:@"key"]
[CommonHelper saveMainUserPref:@"key" sValue:@"value"]
```

**Critical Rule:** Login screens (`if_ViewController`) MUST use main user preference methods to ensure consistent behavior regardless of active account.

### Key Data Models Relationships
- **Assets** → **Inspections** → **Tasks/Photos/Videos**
- **Users** → **Accounts** → **Isolated Databases**
- **Schedules** → **Inspections** → **Assets**
- **Products** → **Cost Items** → **Inspections**

## Important Development Patterns

### Account Context Switching
When working with user accounts, always consider:
1. **Data Isolation:** Each sub-account has separate database and preferences
2. **Login Flow:** Must always use main user preferences for credential storage
3. **Database Access:** Use `environment.runSQL` for proper account-scoped database operations

### Legacy vs Modern Code Integration
- **Objective-C Extensions:** Bridge legacy code to Swift using `CommonHelper+Extensions.swift`
- **Environment Pattern:** `AppEnvironment` provides dependency injection across Objective-C/Swift boundary
- **Gradual Migration:** New features should use Swift/TCA in `AppFeatures`, legacy features remain in `TopInspect/`

### File Organization Conventions
- **Legacy UI:** `TopInspect/if_*.{h,m}` files for view controllers
- **Data Models:** `TopInspect/O_*.{h,m}` for entity objects
- **Database:** `TopInspect/db_*.{h,m}` for data access
- **Modern Features:** `SnapInspect/Sources/AppFeatures/` for new SwiftUI/TCA code
- **Shared Utilities:** `SnapInspect/Sources/Extensions/` for cross-platform helpers

### Testing Approach
- **Legacy Tests:** XCTest integration tests in `SnapInspect3Tests/`
- **Modern Tests:** Swift Package tests in `AppFeaturesTests/` and `ExtensionsTests/`
- **Database Tests:** Use temporary database instances to avoid affecting user data
- **Multi-user Tests:** Test account isolation and preference separation

## Dependencies & Third-Party Libraries

### CocoaPods (Legacy)
- AWS SDK (S3, SimpleDB)
- Rich text editing (WordPress-Aztec-iOS, ZSSRichTextEditor)
- UI utilities (Masonry, IQKeyboardManager)

### Swift Package Manager (Modern)
- Composable Architecture for state management
- DataDog for logging and analytics
- Firebase for analytics and crashlytics
- Authentication (Google Sign-In, Microsoft MSAL)
- Image/Media processing (YPImagePicker, TOCropViewController)
```

## Development Warnings

- **Never use Swift Package build/test commands for this project** - Always use Xcode workspace commands for building and testing