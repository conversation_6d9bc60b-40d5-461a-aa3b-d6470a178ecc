//
//  FileUploadHandle.swift
//  SnapInspect3
//
//  Created by <PERSON> on 5/15/25.
//  Copyright © 2025 SnapInspect. All rights reserved.
//

import SwiftUI
import Extensions

enum FileUploadAction: CaseIterable {
    case uploadFile
    case uploadPhoto
    case takePhoto
    case viewFile

    var title: String {
        switch self {
        case .uploadFile: return "Upload File"
        case .uploadPhoto: return "Upload Photo"
        case .takePhoto: return "Take Photo"
        case .viewFile: return "View File"
        }
    }
}

extension ActionSheet.Button {
    static func `default`(_ fileUploadAction: FileUploadAction, action: @escaping () -> Void) -> ActionSheet.Button {
        .default(Text(fileUploadAction.title), action: action)
    }
}

protocol FileInfoProtocol: Codable {
    var fileID: Int { get set }
    var fileName: String { get set }
    
    init(fileID: Int, fileName: String)
}

extension FileInfoProtocol {
    init?(jsonValue: [String: Any]) {
        guard let data = try? JSONSerialization.data(withJSONObject: jsonValue),
              let fileInfo = try? JSONDecoder().decode(Self.self, from: data) else {
            return nil
        }
        self = fileInfo
    }

    init?(json: String) {
        guard let data = json.data(using: .utf8),
              let fileInfo = try? JSONDecoder().decode(Self.self, from: data) else {
            return nil
        }
        self = fileInfo
    }
    
    func toJson() -> String {
        guard fileName.isEmpty == false, fileID > 0,
              let data = try? JSONEncoder().encode(self),
              let json = String(data: data, encoding: .utf8) else {
            return ""
        }
        return json
    }
}

// MARK: - TaskCustomFileInfo
// Used for task custom info
struct TaskCustomFileInfo: FileInfoProtocol {
    private enum CodingKeys: String, CodingKey {
        case fileID = "f_id"
        case fileName = "f_nm"
    }
    
    var fileName: String
    var fileID: Int
    
    init(fileID: Int, fileName: String) {
        self.fileID = fileID
        self.fileName = fileName
    }
}

// MARK: - TaskCommentFileInfo
// Used for task comment info
struct TaskCommentFileInfo: FileInfoProtocol {
    private enum CodingKeys: String, CodingKey {
        case fileID = "iFileID"
        case fileName = "sFileName"
    }

    var fileName: String
    var fileID: Int
    
    init(fileID: Int, fileName: String) {
        self.fileID = fileID
        self.fileName = fileName
    }
}

protocol FileUploadHandle {
    associatedtype FileInfo: FileInfoProtocol

    var assetID: Int { get }
    var photoFileName: String { get }
    var fileInfo: FileInfo? { get }

    func uploadFile(success: @escaping (FileInfo) -> Void)
    func uploadPhoto(success: @escaping (FileInfo) -> Void)
    func takePhoto(success: @escaping (FileInfo) -> Void)
    func viewFile()
}

extension FileUploadHandle {
    var assetID: Int {
        return 0
    }

    var photoFileName: String {
        return ""
    }

    func uploadFile(success: @escaping (FileInfo) -> Void) {
        guard let topMost = UIApplication.shared.topMost else { return }
        FileProvider.shared.openDocumentsPicker(from: topMost) { urls in
            guard let url = urls.first else { return }
            CommonUpload.uploadAttachment(
                at: url,
                iAssetID: assetID,
                success: { iFileID, sName in
                    success(FileInfo(fileID: iFileID, fileName: sName ?? ""))
                },
                completion: {}
            )
        }
    }
    
    func uploadPhoto(success: @escaping (FileInfo) -> Void) {
        guard let topMost = UIApplication.shared.topMost else { return }
        ImagePicker.shared.pick(from: topMost, for: .photoLibrary) { image in
            guard let image else { return }
            CommonUpload.uploadImage(
                image,
                withFileName: photoFileName,
                forAssetID: assetID,
                success: { iFileID, sName in
                    success(FileInfo(fileID: iFileID, fileName: sName ?? ""))
                },
                completion: {}
            )
        }
    }
    
    func takePhoto(success: @escaping (FileInfo) -> Void) {
        guard let topMost = UIApplication.shared.topMost else { return }
        ImagePicker.shared.pick(from: topMost, for: .camera) { image in
            guard let image else { return }
            CommonUpload.uploadImage(
                image,
                withFileName: photoFileName,
                forAssetID: assetID,
                success: { iFileID, sName in
                    success(FileInfo(fileID: iFileID, fileName: sName ?? ""))
                },
                completion: {}
            )
        }
    }
    
    func viewFile() {
        guard let fileInfo else { return }
        Navigator.downloadAndViewFile(withID: Int32(fileInfo.fileID), sTitle: fileInfo.fileName)
    }
}
