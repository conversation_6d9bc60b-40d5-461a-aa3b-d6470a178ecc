//
// Created by <PERSON> on 2019/12/2.
//  Copyright © 2025 SnapInspect. All rights reserved.
//

#import <Foundation/Foundation.h>

extern NSString *const bNewInspectionUI;
extern NSString *const bDisplayInstruction;
extern NSString *const bEnableWord;
extern NSString *const bSaveCamera;
extern NSString *const bPhotoStamp;
extern NSString *const bLocalEmailClient;
extern NSString *const bSavePassword;
extern NSString *const bDisplayInspectionTimer;
extern NSString *const bPhotoGeoTag;

typedef NS_ENUM(NSUInteger, SettingOptionType) {
  //  SettingOptionTypeNewInspectionView = 0,
    SettingOptionTypeDisplayInstruction,
    SettingOptionTypeEnableWord,
    SettingOptionTypeDisplayTimer,
    SettingOptionTypeSavePhotosToLibrary,
    SettingOptionTypeShowDateTimeStamp,
    SettingOptionTypeShowPhotoGeoTag,
    SettingOptionTypeSimpleRecorder,
    SettingOptionTypeCameraAlternativeView,
    SettingOptionTypeFixedCameraRotation,
    SettingOptionTypeLocalEmailClient,
    SettingOptionTypeAutoLogin,
    SettingOptionTypeStartChat,
    SettingOptionTypeAppVersion
};

typedef NS_ENUM(NSUInteger, SettingOptionCellType) {
    SettingOptionCellTypeSwitch = 0,
    SettingOptionCellTypeText,
    SettingOptionCellTypeBadge
};

typedef struct {
    NSString *title;
    SettingOptionCellType cellType;
    BOOL canSelect;
} SettingConfiguration;

extern SettingConfiguration const SettingOptionConfigurations[];

#define OPTION_CONFIGURATION(option) SettingOptionConfigurations[option]
