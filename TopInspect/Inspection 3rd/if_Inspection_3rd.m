
//
//  if_Inspection_3rd.m
//  SnapInspect3
//
//  Created by <PERSON> on 2019/12/27.
//  Copyright © 2019 SnapInspect. All rights reserved.
//

#import "if_Inspection_3rd.h"
#import "if_Inspection_3rd_ViewModel.h"
#import "InspectionFieldCell.h"
#import "if_LayoutSetup_2nd.h"
#import "NSArray+HighOrderFunction.h"
#import "if_NewAsset.h"
#import "if_AssetRoute.h"
#import "if_Inspection_3rd_Edit.h"
#import "InspectionTimerManager.h"
#import "if_Inspection.h"
#import "CustomIOSAlertView.h"
#import "CommonUI.h"
#import "SCNavigationController.h"
#import "db_Inspection.h"
#import "UIView+Extensions.h"

@interface if_Inspection_3rd () <UITableViewDataSource, UITableViewDelegate, SCNavigationControllerDelegate, SCCaptureCameraControllerDelegate>
@property (weak, nonatomic) IBOutlet UITableView *tableView;
@property (weak, nonatomic) IBOutlet UIButton *btnTimerInfo;
@property (weak, nonatomic) IBOutlet UIButton *btnAction;
@property (weak, nonatomic) IBOutlet UILabel *lbsBottomText;
@property (weak, nonatomic) IBOutlet UIView *vBottomToolbar;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *heightButtonToolbar;
@property (strong, nonatomic) if_Inspection_3rd_ViewModel *viewModel;
@property (strong, nonatomic) InspectionTimerManager *timerManager;
@property (strong, nonatomic) UIButton *btnAssistant;
@end

@implementation if_Inspection_3rd

#pragma mark - Life Circle

- (void)viewDidLoad {
    [super viewDidLoad];

    self.viewModel = [[if_Inspection_3rd_ViewModel alloc] initWithInsId: self.iInsID];
    [self.viewModel trackInsStartEvent];
    self.navigationItem.title = self.viewModel.navigationTitle;
    
    [self.tableView registerNibWithClass: InspectionFieldCell.class];
    [self.tableView hidesEmptyCells];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.contentInset = UIEdgeInsetsMake(15.0, 0.0, 0.0, 0.0);

    UIFont *btnTitleFont = [UIFont SFCompactText_Regular: 14.0];
    self.btnTimerInfo.titleLabel.font = btnTitleFont;
    self.btnTimerInfo.userInteractionEnabled = false;
    [self.btnTimerInfo setTitleColor: UIColor.color_4A4A4A forState: UIControlStateNormal];
    
    self.lbsBottomText.textColor = UIColor.color_4A4A4A;
    self.lbsBottomText.font = btnTitleFont;

    UIImage *actionImage = [UIImage imageNamed:@"icon_inspection_item_action" withTintColor: UIColor.color_navigationBar];
    [self.btnAction setImage: actionImage  forState: UIControlStateNormal];
    [self.btnAction addTarget:self tapped:@selector(onBtnActionTapped)];
    
    if (!self.viewModel.oInspection.bComplete) {
        self.navigationItem.rightBarButtonItem = [UIBarButtonItem
            title: @"Complete" target: self action: @selector(onCompletedButtonTapped)];
    }

    [if_AppDelegate ShowTutorial:@"TUT_Ins1_HEAD" sMessage:@"TUT_Ins1_BODY"];

    [self removePreviousControllers];
    
    if ([CommonHelper bKioskMode]) {
        [self ShowAlert:@"Message" sMessage:@"Please remember to press 'Complete' button on the top right corner once the inspection has been completed."];
    }
    
    [self setupFloatingMicrophoneButton];
    [self setupBottomToolbarSeparator];
    [self timerStart];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear: animated];
    [self.viewModel reloadInspection];
    [self.viewModel trackPageView];
    [self.tableView reloadData];
    [self updateBottomText];
    
    [self.timerManager registerDidBecomeActiveNotification];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear: animated];
    
    if (self.isBeingPopped) {
        [self timerEnd];
    }
    [self.timerManager registerDidBecomeActiveNotification];
    
    if (self.isBeingPopped && [CommonHelper bKioskMode] && !self.viewModel.bKioskUpload) {
        [self ShowAlert:@"Message" sMessage:@"Please remember to go to 'Inspection' tab to upload this inspection. Please keep SnapInspect app foreground during uploading to avoid interruption. Thank you!"];
    }
}

#pragma mark - Setup Methods

- (void)setupFloatingMicrophoneButton {
    self.btnAssistant = [CommonUI createFloatingActionButtonWithImage:@"iconAssistant"
                                                               target:self
                                                               action:@selector(onAssistantButtonTapped:)
                                                               inView:self.view
                                                             position:FloatingActionButtonPositionBottomLeft
                                                    horizontalPadding:16.0
                                                        bottomPadding:self.heightButtonToolbar.constant + 10.0];
}

- (void)onAssistantButtonTapped:(UIButton *)sender {
    SCNavigationController *nav = [
        [SCNavigationController alloc] initWithDelegate:self
                                              iParamID:0
                                            iInsItemID:0
                                                iInsID:self.iInsID
                                            iSObjectID:0
                                      cameraSaveOption:CameraSaveOptionPhoto
                                          cameraOption:CameraOptionMultiple
        iPhotoSize:[db_Inspection GetInsTypePhotoSize:self.iInsID]];
    [nav showDictationCameraWithParentController:self];
}

- (void)setupBottomToolbarSeparator {
    // Add a light gray separator line (0.5pt) at the top of the bottom toolbar
    [self.vBottomToolbar addSeparatorLineAtTopWithThickness:0.5 
                                                      color:[UIColor color_F1F1F1] 
                                                 leftMargin:0.0 
                                                rightMargin:0.0];
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.viewModel.arrItems1st.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    Class cellCls = InspectionFieldCell.class;
    UITableViewCell *cell = [tableView dequeueReusableCellWithClass: cellCls forIndexPath: indexPath];
    [self configureCell:cell forRowAtIndexPath:indexPath];
    return cell;
}

- (void)configureCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    O_InsItem *item = [self.viewModel.arrItems1st safe_objectAtIndex: indexPath.row];
    if (item == nil) return;
    
    if ([cell isKindOfClass: InspectionFieldCell.class]) {
        InspectionFieldCell *fieldCell = (InspectionFieldCell *)cell;
        fieldCell.lbsTitle.text = item.sName;
        
        NSString *showExclamationValue = [CommonJson GetJsonKeyValue:@"IN" sJson: item.sConfig];
        if (item.hasCompulsoryItems && [showExclamationValue integerValue] > 0) {
            fieldCell.iconAccessory.image = [UIImage imageNamed: @"icon_exclamation_sign"];
        } else {
            fieldCell.iconAccessory.image = [UIImage imageNamed: item.bCompleted ? @"ic_check" : @"ic_forward"
                                                  withTintColor: UIColor.color_navigationBar];
        }
    }
}

#pragma mark - UITableViewDelegate

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath: indexPath animated: YES];
    @try {
        O_InsItem *oInsItem = [self.viewModel.arrItems1st safe_objectAtIndex:indexPath.row];
        [Navigator PushInspection: oInsItem.iInsID iInsItemId: oInsItem.iInsItemID sName: oInsItem.sName];
        [self timerEnd];
    } @catch (NSException *exception) {
        Log_Exception(exception)
    }
}

#pragma mark - Lazy properties

- (InspectionTimerManager *)timerManager {
    if (_timerManager == nil) {
        InspectionTimerManager *manager = [[InspectionTimerManager alloc] initWithInsId: self.iInsID];
        if ([CommonHelper IFGetPref_Bool: bDisplayInspectionTimer]) {
            @weakify(self)
            manager.timerLoops = ^(NSString *timestamp) {
                @strongify(self)
                [self.btnTimerInfo setTitle: timestamp forState: UIControlStateNormal];
            };
        }
        _timerManager = manager;
    }
    return _timerManager;
}

#pragma mark - Private methods

- (void)updateBottomText {
    self.lbsBottomText.text = [NSString stringWithFormat: @"%ld out of %ld",
                               self.viewModel.completedItems1st.count, self.viewModel.arrItems1st.count];
}

#pragma mark - Button Tapps

- (void)onBackTapped {
    [self.navigationController popViewControllerAnimated: YES];
}

- (void)removePreviousControllers {
    UIViewController *previous = self.navigationController.previousViewController;
    UIViewController *previous2nd = [self.navigationController previousBefore:previous];

    NSMutableArray *controllers = [NSMutableArray arrayWithArray: self.navigationController.viewControllers];
    [controllers removeLastObject];
    
    if ([previous isKindOfClass: if_LayoutSetup_2nd.class] && [previous2nd isKindOfClass: if_NewAsset.class]) {
        [controllers removeLastObjectLoops: 2];
    } else if ([previous isKindOfClass: if_LayoutSetup_2nd.class]) {
        [controllers removeLastObjectLoops: 1];
    }
    
    [controllers addObject: self];
    
    self.navigationController.viewControllers = [NSArray arrayWithArray: controllers];
}

- (void)deleteInspectionAction { // Delete
    @weakify(self)
    [self ShowAlert: @"Message"
           sMessage: @"Are you sure to delete this inspection?"
       cancelButton: @"No"
         doneButton: @"Yes"
         doneAction:^(UIAlertAction *action) {
            @strongify(self);
             [self.viewModel deleteInspection];
             [self onBackTapped];
         }];
}

- (void)onBtnActionTapped {
    @weakify(self)
    CustomIOSAlertView *alertView = [[CustomIOSAlertView alloc] init];
    alertView.showsBorderLines = NO;
    InsActionView *actionView = [InsActionView viewFromNibWithClass: InsActionView.class];
    
    @weakify(alertView)
    actionView.closeButton.onTapped = ^(id  _Nullable sender) {
        @strongify(alertView)
        [alertView close];
    };
    
    actionView.viewTasksButton.onTapped = ^(id  _Nullable sender) {
        @strongify(self, alertView);
        if_NoticeList *oReminderList = [[if_NoticeList alloc] init];
//        oReminderList.iInsItemID = @(oInsItem.iInsItemID);
        oReminderList.iInsID = @(self.viewModel.oInspection.iInsID);
//        oReminderList.sTitle = oInsItem.sName;
        [self.navigationController pushViewController:oReminderList animated:YES];
        [alertView close];
    };
    
    actionView.editInsButton.onTapped = ^(id  _Nullable sender) {
        @strongify(self, alertView);
        if_Inspection_3rd_Edit *edit = [self.storyboard identifier: @"Ins3_edit"];
        edit.iInsID = self.viewModel.oInspection.iInsID;
        [self.navigationController pushViewController: edit animated: YES];
        [alertView close];
    };

    actionView.deleteInsButton.onTapped = ^(id  _Nullable sender) {
        @strongify(self, alertView);
        if ([CommonHelper bKioskMode]) {
            [self ShowAlert:@"Message" sMessage:@"The request inspection can not be deleted."];
        } else {
            [self deleteInspectionAction];
        }
        [alertView close];
    };

    alertView.containerView = actionView;
    alertView.buttonTitles = nil;
    [alertView show];
}

- (void)onCompletedButtonTapped {
    @try {
        if (self.viewModel.hasCompulsoryItems) {
            [self ShowAlert:@"Incompleted Inspection"
                   sMessage:@"The inspection can not be uploaded until all compulsory questions are answered. Please follow ! mark to complete inspection."];
            
            [self.viewModel.arrItems1st forEach:^(O_Item *insItem) {
                [db_InsItem markedAsNeedValidate: insItem];
            }];
             
            [self.viewModel reloadInspection];
            [self.tableView reloadData];
        } else if ([CommonHelper bKioskMode]) {
            @weakify(self)
            [self.viewModel completeInspection];
            UIAlertController *alert = [UIAlertController alertControllerWithTitle:@"Inspection Completed"
                                                                           message:@"Would you like to upload now?"
                                                                    preferredStyle:UIAlertControllerStyleActionSheet];
            UIAlertAction *oKioskUploadInspection = [UIAlertAction actionWithTitle:@"Upload Now"
                                                                             style:UIAlertActionStyleDefault
                                                                           handler:^(UIAlertAction *action) {
                @strongify(self)
                if ([if_AppDelegate.shared bInternet_WithMessage:@"Please connect to Internet to view upload."]) {
                    [self.viewModel uploadInspection];
                    [self onBackTapped];
                }
            }];
            [alert addAction:oKioskUploadInspection];
            
            UIAlertAction *oClose = [UIAlertAction actionWithTitle:@"Upload Later"
                    style: UIAlertActionStyleCancel handler:^(UIAlertAction *action) {
                @strongify(self);
                [self onBackTapped];
            }];
            [alert addAction:oClose];
            
            if ([if_AppDelegate biPad]){
                [alert setModalPresentationStyle:UIModalPresentationFullScreen];
                UIPopoverPresentationController *popPresenter = [alert popoverPresentationController];
                popPresenter.barButtonItem = self.navigationItem.rightBarButtonItem;
            }

            [self presentViewController:alert animated:YES completion:nil];
        } else {
            [self.viewModel completeInspection];
            [self.viewModel traceCompleteEvent];
            
            UIViewController *parent = [self.navigationController previousViewController];
            if ([parent isKindOfClass: if_HomeScreen.class]) {
                if_HomeScreen *home = (if_HomeScreen *)parent;
                if ([home.selectedViewController isKindOfClass: if_Inspection.class]) {
                    if_Inspection *insp = (if_Inspection *)home.selectedViewController;
                    [insp selectCompletedSegment];
                }
            }
            
            [self onBackTapped];
        }
    } @catch (NSException *exception) {
        Log_Exception(exception)
    }
}

- (void)timerStart {
    [self.timerManager start];
}

- (void)timerEnd {
    [self.timerManager end];
}

#pragma mark - SCCaptureCameraControllerDelegate

- (void)PostProcessPhotoID:(int)_iPhotoID iInsItemID:(int)_iInsItemID iParamID:(int)_iParamID {
    // Photos are captured at the top level inspection, refresh the data
    O_InsItem *oInsItem = [db_InsItem GetInsItem:_iInsItemID];
    if (oInsItem) {
        [db_InsItem UpdateInsItemValue:oInsItem withParamID:_iParamID photoID:_iPhotoID];
    }
}

- (void)PostPreviewPhotoID:(int)iPhotoID iInsItemID:(int)iInsItemID iParamID:(int)iParamID {
    // Preview functionality if needed
}

@end
