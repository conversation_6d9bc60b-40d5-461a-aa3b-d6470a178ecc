import AppFeatures
import Extensions
import Foundation
import SnapKit
import UIKit

// MARK: - Layout Constants

private enum LayoutConstants {
    static let horizontalMargin: CGFloat = 15
    static let controlHeight: CGFloat = 54
    static let stackSpacing: CGFloat = 8
    static let stackVerticalPadding: CGFloat = 5
    static let itemSpacing: CGFloat = 10
    static let firstControlFontSize: CGFloat = 14
    static let childControlFontSize: CGFloat = 13
    static let topOffset: CGFloat = 20
}

// MARK: - Recognition Constants

private enum RecognitionConstants {
    static let restartDelayNanoseconds: UInt64 = 100_000_000 // 0.1 seconds
    static let errorRetryDelayNanoseconds: UInt64 = 1_000_000_000 // 1 second
    static let maxConsecutiveErrors = 5 // Maximum consecutive errors before giving up
    static let debounceDelayNanoseconds: UInt64 = 500_000_000 // 0.5 seconds
    static let parentSelectionDelay: TimeInterval = 0.5 // Delay for parent item selection
    static let subtitleHideDelay: TimeInterval = 2.0 // Delay before hiding subtitles
    static let minSearchTextLength = 3 // Minimum length for search text
    static let fuzzyMatchThreshold = 0.7 // Threshold for fuzzy matching
}

// MARK: - Color Constants

private enum ColorConstants {
    static let firstControlBottomLineHex = 0x007AFF
    static let childControlBottomLineHex = 0x34C759
    static let textNormalAlpha: CGFloat = 0.8
    static let childTextNormalAlpha: CGFloat = 0.7
}

// MARK: - Segmented Control Properties

extension DictationCameraController {
    // Associated object keys for properties
    private enum AssociatedKeys {
        static var firstLevelSegmentedControl: UInt8 = 0
        static var childSegmentedControl: UInt8 = 0
        static var segmentedControlContainer: UInt8 = 0
        static var childControlWrapper: UInt8 = 0
        static var arrItems1st: UInt8 = 0
        static var arrChildItems: UInt8 = 0
        // Speech recognition properties
        static var liveSpeechRecognitionTask: UInt8 = 1
        static var subtitleOverlay: UInt8 = 2
        static var autoSelectTask: UInt8 = 3
        static var consecutiveErrorCount: UInt8 = 4
    }
    
    // MARK: - Properties
    
    private var firstLevelSegmentedControl: ScrollableTextSegmentedControl? {
        get { getAssociated(&AssociatedKeys.firstLevelSegmentedControl) }
        set { setAssociated(newValue, for: &AssociatedKeys.firstLevelSegmentedControl) }
    }
    
    private var childSegmentedControl: ScrollableTextSegmentedControl? {
        get { getAssociated(&AssociatedKeys.childSegmentedControl) }
        set { setAssociated(newValue, for: &AssociatedKeys.childSegmentedControl) }
    }
    
    private var segmentedControlContainer: UIView? {
        get { getAssociated(&AssociatedKeys.segmentedControlContainer) }
        set { setAssociated(newValue, for: &AssociatedKeys.segmentedControlContainer) }
    }
    
    private var childControlWrapper: UIView? {
        get { getAssociated(&AssociatedKeys.childControlWrapper) }
        set { setAssociated(newValue, for: &AssociatedKeys.childControlWrapper) }
    }
    
    private var arrItems1st: [O_Item]? {
        get { getAssociated(&AssociatedKeys.arrItems1st) }
        set { setAssociated(newValue, for: &AssociatedKeys.arrItems1st) }
    }
    
    private var arrChildItems: [O_Item]? {
        get { getAssociated(&AssociatedKeys.arrChildItems) }
        set { setAssociated(newValue, for: &AssociatedKeys.arrChildItems) }
    }
    
    // MARK: - Speech Recognition Properties
    
    private var liveSpeechRecognitionTask: Task<Void, Never>? {
        get { getAssociated(&AssociatedKeys.liveSpeechRecognitionTask) }
        set { setAssociated(newValue, for: &AssociatedKeys.liveSpeechRecognitionTask) }
    }
    
    private var _subtitleOverlay: SubtitleOverlayView? {
        get { getAssociated(&AssociatedKeys.subtitleOverlay) }
        set { setAssociated(newValue, for: &AssociatedKeys.subtitleOverlay) }
    }
    
    private var autoSelectTask: Task<Void, Never>? {
        get { getAssociated(&AssociatedKeys.autoSelectTask) }
        set { setAssociated(newValue, for: &AssociatedKeys.autoSelectTask) }
    }
    
    private var consecutiveErrorCount: Int {
        get { getAssociated(&AssociatedKeys.consecutiveErrorCount) ?? 0 }
        set { setAssociated(newValue, for: &AssociatedKeys.consecutiveErrorCount) }
    }
    
    @objc var subtitleOverlay: SubtitleOverlayView? {
        if let overlay = _subtitleOverlay {
            return overlay
        }
        
        let overlay = SubtitleOverlayView()
        view.addSubview(overlay)
        
        overlay.translatesAutoresizingMaskIntoConstraints = true
        
        // Ensure overlay is visible above other views
        view.bringSubviewToFront(overlay)
        
        _subtitleOverlay = overlay
        
        // Update position based on preview view
        updateSubtitleOverlayPosition()
        
        return overlay
    }
    
    @objc func updateSubtitleOverlayPosition() {
        guard let overlay = _subtitleOverlay else { return }
        
        // Get preview view directly from property
        guard let previewView = previewView else { return }
        
        // Get preview view bounds in self.view coordinate system
        let previewRect = previewView.bounds(in: view)
        
        // Position overlay at bottom of preview with some padding
        let horizontalPadding: CGFloat = 20
        let bottomPadding: CGFloat = 20
        let overlayHeight: CGFloat = 50
        
        overlay.frame = CGRect(
            x: previewRect.minX + horizontalPadding,
            y: previewRect.maxY - overlayHeight - bottomPadding,
            width: previewRect.width - (horizontalPadding * 2),
            height: overlayHeight
        )
    }
    
    // MARK: - Public Methods
    
    @objc func setupSegmentedControls() {
        addSegmentedControls()
        loadInspectionItems()
    }
    
    @objc func refreshInsItemPhotoCountBadge() {
        childSegmentedControl?.refreshBadges()
    }
    
    // MARK: - Private Methods
    
    private func addSegmentedControls() {
        // Stack view container for both segmented controls
        let segmentedStackView = UIStackView()
        segmentedStackView.axis = .vertical
        segmentedStackView.distribution = .fillEqually
        segmentedStackView.alignment = .fill
        segmentedStackView.spacing = LayoutConstants.stackSpacing
        segmentedStackView.backgroundColor = .black
        segmentedStackView.layoutMargins = UIEdgeInsets(
            top: LayoutConstants.stackVerticalPadding,
            left: 0,
            bottom: LayoutConstants.stackVerticalPadding,
            right: 0
        )
        segmentedStackView.isLayoutMarginsRelativeArrangement = true
        
        // Add stack view with proper constraints
        rootStackView.addArrangedSubview(segmentedStackView)
        segmentedStackView.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(
                LayoutConstants.controlHeight + 2 * LayoutConstants.stackVerticalPadding
            )
            make.height.lessThanOrEqualTo(120) // Limit max height
        }
        segmentedControlContainer = segmentedStackView
        
        // First level segmented control
        let firstControl = ScrollableTextSegmentedControl()
        firstControl.backgroundColor = .clear
        firstControl.bottomLineBackgroundColor = UIColor(hex: ColorConstants.firstControlBottomLineHex)
        firstControl.textNormalColor = UIColor(white: ColorConstants.textNormalAlpha, alpha: 1.0)
        firstControl.textSelectedColor = .white
        firstControl.textNormalFont = .systemFont(ofSize: LayoutConstants.firstControlFontSize)
        firstControl.textSelectedFont = .boldSystemFont(ofSize: LayoutConstants.firstControlFontSize)
        firstControl.bottomLineWidthInset = 0
        firstControl.itemSpacing = LayoutConstants.itemSpacing
        firstControl.isUserInteractionEnabled = true
        firstControl.defaultSelectedIndex = -1
        
        // Wrap first control in a view with margins
        let firstControlWrapper = UIView()
        firstControlWrapper.addSubview(firstControl)
        firstControl.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalToSuperview().offset(LayoutConstants.horizontalMargin)
            make.right.equalToSuperview().offset(-LayoutConstants.horizontalMargin)
            make.height.equalTo(LayoutConstants.controlHeight)
        }
        firstControlWrapper.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(LayoutConstants.controlHeight)
        }
        segmentedStackView.addArrangedSubview(firstControlWrapper)
        firstLevelSegmentedControl = firstControl
        
        // Child segmented control (initially hidden)
        let childControl = ScrollableTextSegmentedControl()
        childControl.backgroundColor = .clear
        childControl.bottomLineBackgroundColor = UIColor(hex: ColorConstants.childControlBottomLineHex)
        childControl.textNormalColor = UIColor(white: ColorConstants.childTextNormalAlpha, alpha: 1.0)
        childControl.textSelectedColor = .white
        childControl.textNormalFont = .systemFont(ofSize: LayoutConstants.childControlFontSize)
        childControl.textSelectedFont = .boldSystemFont(ofSize: LayoutConstants.childControlFontSize)
        childControl.bottomLineWidthInset = 0
        childControl.itemSpacing = LayoutConstants.itemSpacing
        childControl.isUserInteractionEnabled = true
        childControl.defaultSelectedIndex = -1
        
        // Wrap child control in a view with margins
        let childControlWrapper = UIView()
        childControlWrapper.addSubview(childControl)
        childControl.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalToSuperview().offset(LayoutConstants.horizontalMargin)
            make.right.equalToSuperview().offset(-LayoutConstants.horizontalMargin)
            make.height.equalTo(LayoutConstants.controlHeight)
        }
        childControlWrapper.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(LayoutConstants.controlHeight)
        }
        segmentedStackView.addArrangedSubview(childControlWrapper)
        childSegmentedControl = childControl
        self.childControlWrapper = childControlWrapper
        
        // Set up selection handlers
        firstControl.didSelectItem = { [weak self] selectedIndex in
            self?.handleFirstLevelSelectionChanged(selectedIndex)
        }
        
        childControl.didSelectItem = { [weak self] selectedIndex in
            self?.handleChildSelectionChanged(selectedIndex)
        }

        // Set up badge text for child control
        childControl.badgeTextForIndex = { [weak self] index in
            guard let items = self?.arrChildItems,
                  let selectedItem = items[safe: index],
                  let oItem = db_Inspection.getItem(selectedItem.iInsItemID),
                  let sValue = oItem.configDict[NSNumber(value: SI_Control_Type.PTO.rawValue)],
                  let arrPhotos = db_Media.searchBulkPhotos(sValue)
            else {
                return ""
            }
            
            return arrPhotos.count > 0 ? "\(arrPhotos.count)" : ""
        }
    }
    
    @objc func loadInspectionItems() {
        // Load first-level items (parent ID = 0)
        arrItems1st = filterOutNoNameItems(db_Inspection.getItems(0, iInsID: Int32(iInsID)) as? [O_Item])
        
        // Convert items to text array
        let textItems = arrItems1st?.compactMap { $0.sName } ?? []
        
        // Update first level control
        firstLevelSegmentedControl?.textItems = textItems
        
        if iPInsItemID > 0 {
            // Find the index of the parent item with matching ID
            if let parentIndex = arrItems1st?.firstIndex(
                where: { $0.iInsItemID == Int32(iPInsItemID) }
            ) {
                // Select the parent item
                DispatchQueue.main.asyncAfter(deadline: .now() + RecognitionConstants.parentSelectionDelay) { [weak self] in
                    self?.firstLevelSegmentedControl?.select(parentIndex)
                }
            }
        }
        
        // Show/hide controls based on whether we have items
        segmentedControlContainer?.isHidden = textItems.isEmpty
    }
    
    private func loadChildItems(forParentIndex parentIndex: Int) {
        guard let items = arrItems1st,
              parentIndex >= 0,
              parentIndex < items.count,
              let parentItem = items[safe: parentIndex]
        else {
            return
        }
        
        // Load child items for the selected parent
        arrChildItems = parentItem.isSimpleInsItem ?
            [parentItem] : filterOutNoNameItems(db_Inspection.getItems(parentItem.iInsItemID, iInsID: Int32(iInsID)) as? [O_Item])
        
        // Update child control
        childSegmentedControl?.textItems = arrChildItems?.compactMap(\.sName) ?? []
        childSegmentedControl?.select(-1)
    }
    
    private func filterOutNoNameItems(_ items: [O_Item]?) -> [O_Item] {
        // Filter out items with empty names
        return items?.filter { !$0.sName.isEmpty } ?? []
    }
    
    private func handleFirstLevelSelectionChanged(_ selectedIndex: Int) {
        guard let items = arrItems1st,
              selectedIndex >= 0,
              selectedIndex < items.count,
              let selectedItem = items[safe: selectedIndex]
        else {
            return
        }
        
        // Load child items for the newly selected parent
        loadChildItems(forParentIndex: selectedIndex)
        // Reset the item ID to 0
        iPInsItemID = Int(selectedItem.iInsItemID)
        iInsItemID = 0
        updateViews()
    }
    
    private func handleChildSelectionChanged(_ selectedIndex: Int) {
        guard let childItems = arrChildItems,
              selectedIndex >= 0,
              selectedIndex < childItems.count,
              let selectedItem = childItems[safe: selectedIndex]
        else {
            return
        }
        
        iInsItemID = Int(selectedItem.iInsItemID)
        iParamID = Int(db_InsItem.getPhotoControlParamID(selectedItem))
        updateViews()
    }
    
    @objc private func autoSelectItemWithDictatedText(_ text: String) {
        guard !text.isEmpty, text.count >= RecognitionConstants.minSearchTextLength else { return }
        
        // Extract item names
        let childItemNames = arrChildItems?.compactMap(\.sName) ?? []
        let parentItemNames = arrItems1st?.compactMap(\.sName) ?? []
        
        // Use hierarchical matching from SpeechNormalizer
        guard let match = SpeechNormalizer.findBestMatchInHierarchy(
            spokenText: text,
            childItems: childItemNames,
            parentItems: parentItemNames,
            threshold: RecognitionConstants.fuzzyMatchThreshold,
            domainConfiguration: .inspectionDefaults
        ) else { return }
        
        // Handle the match based on which level it was found in
        switch match.level {
        case .child:
            // Found match in child items
            childSegmentedControl?.select(match.index)
            handleChildSelectionChanged(match.index)
            
        case .parent:
            // Found match in parent items
            firstLevelSegmentedControl?.select(match.index)
            handleFirstLevelSelectionChanged(match.index)
        }
        
        // Provide feedback for any successful match
        provideSelectionFeedback()
    }
    
    private func provideSelectionFeedback() {
        // Light haptic feedback for selection
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    // MARK: - Speech Recognition Control Methods
    
    @objc func startSpeechRecognition() {
        // Cancel any existing recognition task
        liveSpeechRecognitionTask?.cancel()
        
        liveSpeechRecognitionTask = Task { @MainActor in
            let speechService = LiveSpeechRecognitionService.shared
            
            // Request authorization first
            let authStatus = await speechService.requestAuthorization()
            guard authStatus == .authorized else {
                CommonAnalytics.trackEvent("Speech Recognition Not Authorized", meta: [
                    "status": "\(authStatus)",
                ])
                return
            }
            
            // Check availability
            guard speechService.isAvailable() else {
                CommonAnalytics.trackEvent("Speech Recognition Not Available", meta: [:])
                return
            }
            
            self.subtitleOverlay?.isVisible = true
            
            CommonAnalytics.trackEvent("Speech Recognition Started", meta: [
                "InsID": self.iInsID,
                "ItemID": self.iInsItemID,
            ])
            
            // Reset error count on start
            self.consecutiveErrorCount = 0
            
            // Keep trying to maintain continuous recognition
            while !Task.isCancelled {
                do {
                    // Start continuous recognition
                    let recognitionStream = try await speechService.startContinuousRecognition(nil)
                    
                    // Reset error count on successful connection
                    self.consecutiveErrorCount = 0
                    
                    // Process recognition updates
                    for await update in recognitionStream {
                        // Check if task was cancelled
                        guard !Task.isCancelled else {
                            break
                        }
                        
                        await self.handleSpeechUpdate(update)
                    }
                    
                    // Stream ended (after ~60 seconds), but we're still active
                    if !Task.isCancelled {
                        // Small delay before restarting to avoid rapid reconnection
                        try? await Task.sleep(nanoseconds: RecognitionConstants.restartDelayNanoseconds)
                    }
                    
                } catch {
                    // Only track error if task wasn't cancelled
                    if !Task.isCancelled {
                        self.consecutiveErrorCount += 1
                        
                        // Categorize the error
                        let errorCategory = self.categorizeError(error)
                        
                        CommonAnalytics.trackEvent("Speech Recognition Error", meta: [
                            "error": error.localizedDescription,
                            "errorCategory": errorCategory,
                            "consecutiveErrors": self.consecutiveErrorCount,
                            "InsID": self.iInsID,
                            "ItemID": self.iInsItemID,
                        ])
                        
                        // Check if we've exceeded max consecutive errors
                        if self.consecutiveErrorCount >= RecognitionConstants.maxConsecutiveErrors {
                            CommonAnalytics.trackEvent("Speech Recognition Max Errors", meta: [
                                "maxErrors": RecognitionConstants.maxConsecutiveErrors,
                            ])
                            self.subtitleOverlay?.updateSubtitle("Speech recognition stopped due to repeated errors")
                            break
                        }
                        
                        // Wait before retrying
                        try? await Task.sleep(nanoseconds: RecognitionConstants.errorRetryDelayNanoseconds)
                    }
                }
            }
            
            // Speech recognition task ended
        }
    }
    
    @objc func stopSpeechRecognition() {
        // Cancel the recognition task
        liveSpeechRecognitionTask?.cancel()
        liveSpeechRecognitionTask = nil
        
        // Cancel any pending auto-select
        autoSelectTask?.cancel()
        autoSelectTask = nil
        
        // Reset error count
        consecutiveErrorCount = 0
        
        Task { @MainActor in
            let speechService = LiveSpeechRecognitionService.shared
            await speechService.stopContinuousRecognition()
        }
        
        // Keep subtitles visible for a moment before hiding
        DispatchQueue.main.asyncAfter(deadline: .now() + RecognitionConstants.subtitleHideDelay) { [weak self] in
            self?.subtitleOverlay?.clearSubtitle()
        }
        
        CommonAnalytics.trackEvent("Speech Recognition Stopped", meta: [
            "InsID": iInsID,
            "ItemID": iInsItemID,
        ])
    }
    
    private func categorizeError(_ error: Error) -> String {
        switch error {
        case is URLError:
            return "Network"
        case is DecodingError:
            return "Parsing"
        case let nsError as NSError:
            switch nsError.domain {
            case "kAFAssistantErrorDomain":
                return "SpeechFramework"
            case NSCocoaErrorDomain:
                return "System"
            default:
                return "Unknown"
            }
        default:
            return "Unknown"
        }
    }
    
    @MainActor
    private func handleSpeechUpdate(_ update: LiveSpeechUpdate) async {
        switch update {
        case .partialText(let text, _):
            subtitleOverlay?.updateSubtitle(text)
            autoSelectItemWithDictatedTextDebounced(text)
            
        case .finalText(let text, let timestamp):
            autoSelectItemWithDictatedTextDebounced(text)
            
        case .error(let errorMessage):
            // Don't stop on errors - let the service handle recovery
            CommonAnalytics.trackEvent("Speech Recognition Update Error", meta: [
                "error": errorMessage,
            ])
            
        case .recognitionRestarted:
            // Recognition restarted for continuous listening
            break
            
        case .availabilityChanged(let isAvailable):
            if !isAvailable {
                subtitleOverlay?.updateSubtitle("Speech recognition temporarily unavailable")
            }
        }
    }

    private func autoSelectItemWithDictatedTextDebounced(_ text: String) {
        // Cancel any previous auto-select task
        autoSelectTask?.cancel()
        
        // Create a new task with delay
        autoSelectTask = Task { @MainActor in
            // Wait for debounce delay
            try? await Task.sleep(nanoseconds: RecognitionConstants.debounceDelayNanoseconds)
            
            // Check if task was cancelled during the wait
            guard !Task.isCancelled else { return }
            
            // Perform the auto-selection
            autoSelectItemWithDictatedText(text)
        }
    }
}
