//
//  if_Settings.m
//  InspectionFolio
//
//  Created by <PERSON> on 7/02/14.
//  Copyright (c) 2014 Cloudya. All rights reserved.
//

#import "if_Settings.h"
#import "if_Purchase.h"

#import "SettingUserInfoCell.h"
#import "SettingBadgeOptionCell.h"
#import "SettingCatagoryHeaderView.h"

#import <Intercom/Intercom.h>
#import "MBProgressHUD+Extensions.h"

@interface if_Settings () <UITableViewDelegate, UITableViewDataSource>
@property (strong, nonatomic) O_User *oUserInfo;
@property (strong, nonatomic) NSDictionary *tableContents;
@property (strong, nonatomic) NSArray *sortedKeys;

@property (weak, nonatomic) IBOutlet UIButton *purchaseBtn;
@property (weak, nonatomic) IBOutlet UITableView *settingTableView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *tableViewTopContrast;
@end

@implementation if_Settings

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.navigationItem.title = @"Settings";
    
    O_User *user = [[O_User alloc] init];
    user.sName = [CommonUser currentUserName];
    user.sCompany = [CommonUI GetCompanyName];
    user.sEmail = [CommonHelper IFGetPref:@"sEmail"];
    
    self.oUserInfo = user;
    
    self.tableContents = @{
        @"Inspection Settings": @[
                //@(SettingOptionTypeNewInspectionView),
                @(SettingOptionTypeDisplayInstruction),
                @(SettingOptionTypeEnableWord),
                @(SettingOptionTypeDisplayTimer)
            ],
        @"Photo & Video": @[
                @(SettingOptionTypeSavePhotosToLibrary),
                @(SettingOptionTypeShowDateTimeStamp),
                @(SettingOptionTypeShowPhotoGeoTag)
        ],
        @"Email": @[@(SettingOptionTypeLocalEmailClient)],
        @"General": @[@(SettingOptionTypeAutoLogin)],
        @"Help & Support": @[@(SettingOptionTypeStartChat)],
        @"App Version": @[@(SettingOptionTypeAppVersion)]
    };
    self.sortedKeys = @[
        @"General",
        @"Inspection Settings",
        @"Photo & Video",
        @"Email",
        @"Help & Support",
        @"App Version"
    ];

    UIBarButtonItem *btn_Logout = [UIBarButtonItem title: @"Log out" target: self action: @selector(btn_Logout)];
    btn_Logout.tintColor = UIColor.whiteColor;
    self.navigationItem.rightBarButtonItem = btn_Logout;
   
    [self.settingTableView registerNibWithClass: SettingUserInfoCell.class];
    [self.settingTableView registerNibWithClass: SettingTextOptionCell.class];
    [self.settingTableView registerNibWithClass: SettingSwitchOptionCell.class];
    [self.settingTableView registerNibWithClass: SettingBadgeOptionCell.class];
    [self.settingTableView registerHeaderFooterViewNibWithClass: SettingCatagoryHeaderView.class];
    
    self.settingTableView.sectionHeaderHeight = UITableViewAutomaticDimension;
    self.settingTableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.settingTableView.backgroundColor = UIColor.color_F9F9F9;
    self.settingTableView.tableFooterView = ({
        UIView *footerView = [[UIView alloc] init];
        footerView.height = 135.0;
        UIButton *btn = [UIButton buttonWithType: UIButtonTypeCustom];
        btn.layer.cornerRadius = 6.0;
        btn.clipsToBounds = YES;
        [btn addTarget: self action: @selector(btn_Logout) forControlEvents: UIControlEventTouchUpInside];
        [btn setTitle:@"Log Out" forState: UIControlStateNormal];
        btn.titleLabel.font = [UIFont SanFranciscoDisplay_Regular: 17.0];
        [btn setTitleColor: UIColor.whiteColor forState: UIControlStateNormal];
        [btn setBackgroundImage: [UIImage imageWithColorHex: 0xFF6565] forState: UIControlStateNormal];
        [footerView addSubview: btn];
        [btn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(footerView).offset(24.0);
            make.left.equalTo(footerView).offset(20.0);
            make.right.equalTo(footerView).offset(-20.0);
            make.height.equalTo(@40.0);
        }];
        footerView;
    });
    
   // if ([CommonHelper IFHasSubscribe])
   //  [self DisplayStartInspectionButton:false];

    [self updateLocalSettingsBasedOnRoleRestriction];
}

// Update the local settings based on the role restriction
- (void)updateLocalSettingsBasedOnRoleRestriction {
    if ([CommonPermission shouldTurnOnInstruction]) {
        [CommonHelper IFSavePref:bDisplayInstruction sValue:@"1"];
    }
    if ([CommonPermission shouldTurnOnPhotoDate]) {
        [CommonHelper IFSavePref:bPhotoStamp sValue:@"1"];
    }
    if ([CommonPermission shouldTurnOnLocation]) {
        [CommonHelper IFSavePref:bPhotoGeoTag sValue:@"1"];
    }
}

-(void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    [CommonAnalytics trackEvent:@"iOS View Settings" meta:nil];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
}

#pragma mark - UITableViewDataSourece

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.sortedKeys.count + 1;
}

- (NSInteger)tableView:(UITableView *)table numberOfRowsInSection:(NSInteger)section {
    if (section == 0) return 1;
    NSArray *options = self.tableContents[self.sortedKeys[section - 1]];
    return options.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    @try {
        UITableViewCell *cell = nil;
        if (indexPath.section == 0) {
            cell = [tableView dequeueReusableCellWithClass: SettingUserInfoCell.class forIndexPath: indexPath];
        } else {
            Class cellTypeClass = [self cellClassForOption: [self optionForIndexPath: indexPath]];
            cell = [tableView dequeueReusableCellWithClass: cellTypeClass forIndexPath: indexPath];
        }
        
        [self configureCell:cell forRowAtIndexPath:indexPath];
        return cell;
        
    } @catch (NSException *exception) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
}

- (Class)cellClassForOption:(SettingOptionType)option {
    SettingConfiguration configuration = OPTION_CONFIGURATION(option);
    Class cellTypeClass = nil;
    switch (configuration.cellType) {
        case SettingOptionCellTypeSwitch:
            cellTypeClass = SettingSwitchOptionCell.class;
            break;
        case SettingOptionCellTypeText:
            cellTypeClass = SettingTextOptionCell.class;
            break;
        case SettingOptionCellTypeBadge:
            cellTypeClass = SettingBadgeOptionCell.class;
            break;
    }
    return cellTypeClass;
}

- (void)configureCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    @weakify(self)
    if ([cell isKindOfClass: SettingUserInfoCell.class]) {
        SettingUserInfoCell *uCell = (SettingUserInfoCell *)cell;
        [uCell UpdateUserInfo: self.oUserInfo];
    } else {
        SettingConfiguration configuration = [self configurationForIndexPath:indexPath];
        SettingOptionType option = [self optionForIndexPath: indexPath];
        
        NSString *optionTitle = configuration.title;
        if ([cell isKindOfClass: SettingTextOptionCell.class]) {
            SettingTextOptionCell *textCell = (SettingTextOptionCell *)cell;
            [textCell UpdateText: optionTitle];
            textCell.canSelected = [self configurationForIndexPath:indexPath].canSelect;
            if (option == SettingOptionTypeAppVersion) {
                [textCell enableTapGesture: YES];
                textCell.onTapped3 = ^{
                    @strongify(self)
                    MBProgressHUD *hud = [MBProgressHUD si_showHUDAddedTo:self.view animated:YES];
                    hud.userInteractionEnabled = NO;
                    hud.mode = MBProgressHUDModeText;
                    hud.labelText = @"Info";
                    hud.detailsLabelText = @"Reset/Recover function will be started after 2 clicks.";
                    [hud hideAnimated:YES afterDelay: 2];
                };
                @weakify(textCell)
                textCell.onTapped5 = ^{
                    @strongify(self, textCell)
                    [CommonHelper showsActionSheetWithOptions:@[@"Submit Data", @"Reset Sync"]
                                                   sourceView:textCell
                                                     selected:^(NSString * _Nonnull option, NSInteger idx) {
                        switch (idx) {
                            case 0: {
                                [self ShowAlert:@"" sMessage:@"You are about to submit data. Are you sure?"
                                   cancelButton:@"Cancel"
                                     doneButton:@"Yes" doneAction:(void (^)(UIAlertAction *)) ^() {
                                    NSDictionary *params = @{
                                        @"iCustomerID": [CommonHelper IFGetPref:@"iCustomerID"],
                                        @"sToken": [CommonHelper IFGetPref:@"sToken"]
                                    };
                                    [self.homeScreenDelegate forceUploadLocalDataWithEndpoint:EndpointGetFolderToken params:params];
                                }];
                            }
                                break;
                            case 1:
                                [CommonHelper resetSyncDates];
                                [self ShowAlert:@"" sMessage:@"Please go back to Asset List screen to sync the mobile app"
                                   cancelButton:@"OK" cancelAction:(void (^)(UIAlertAction *)) ^() {
                                            [self.navigationController popViewControllerAnimated:YES];
                                }];
                                break;
                        }
                    }];
                };
            } else {
                [textCell enableTapGesture: NO];
                textCell.onTapped3 = ^{};
                textCell.onTapped5 = ^{};
            }
        } else if ([cell isKindOfClass: SettingSwitchOptionCell.class]) {
            SettingSwitchOptionCell *switchCell = (SettingSwitchOptionCell *)cell;
            [switchCell UpdateTitle: optionTitle];
            switchCell.optionChecked = [self isOptionChecked: indexPath];
            @weakify(switchCell);
            switchCell.onSwitchStateChanged = ^(BOOL state) {
                @strongify(self, switchCell);
                NSIndexPath *cellIndexPath = [self.settingTableView indexPathForCell: switchCell];
                SettingOptionType option = [self optionForIndexPath: cellIndexPath];
                if (![self validateOptionEditable:option]) {
                    SettingConfiguration configuration = [self configurationForIndexPath:indexPath];
                    [self ShowAlert:@"" sMessage:[NSString stringWithFormat:@"The %@ cannot be changed due to the role restriction.", configuration.title]];
                } else {
                    [self updateOptionChecked: state atIndexPath: cellIndexPath];
                }
                [self.settingTableView reloadRowsAtIndexPaths: @[cellIndexPath] withRowAnimation: UITableViewRowAnimationNone];
            };
        } else if ([cell isKindOfClass: SettingBadgeOptionCell.class]) {
            SettingBadgeOptionCell *badgeOptionCell = (SettingBadgeOptionCell *)cell;
            [badgeOptionCell UpdateTitle: optionTitle];
            [badgeOptionCell UpdateBadgeNumber: 270];
            badgeOptionCell.onBtnBadgeSelected = ^ {

            };
        }
    }
}

- (SettingConfiguration)configurationForIndexPath: (NSIndexPath *)indexPath {
    return OPTION_CONFIGURATION([self optionForIndexPath: indexPath]);
}

- (SettingOptionType)optionForIndexPath: (NSIndexPath *)indexPath {
    NSArray *options = self.tableContents[self.sortedKeys[(NSUInteger) (indexPath.section - 1)]];
    SettingOptionType option = (SettingOptionType)[options[(NSUInteger) indexPath.row] integerValue];
    return option;
}

- (BOOL)isOptionChecked: (NSIndexPath *)indexPath {
    SettingOptionType option = [self optionForIndexPath: indexPath];
    return [CommonHelper IFGetPref_Bool: [self preferenceKeyForOption: option]
                           defaultValue: [self defaultValueForOption:option]];
}

- (void)updateOptionChecked: (BOOL)state atIndexPath: (NSIndexPath *)indexPath {
    SettingOptionType option = [self optionForIndexPath: indexPath];
    [CommonHelper IFSavePref:[self preferenceKeyForOption: option] sValue:[@(state) stringValue]];
}

// The {option} cannot be changed due to the role restriction.
- (BOOL)validateOptionEditable: (SettingOptionType)option {
    switch (option) {
        case SettingOptionTypeDisplayInstruction:
            return ![CommonPermission shouldTurnOnInstruction];
        case SettingOptionTypeShowDateTimeStamp:
            return ![CommonPermission shouldTurnOnPhotoDate];
        case SettingOptionTypeShowPhotoGeoTag:
            return ![CommonPermission shouldTurnOnLocation];
        default: return true;
    }
}

- (BOOL)defaultValueForOption: (SettingOptionType)option {
    switch (option) {
        default: return NO;
    }
}

- (NSString *)preferenceKeyForOption: (SettingOptionType)option {
    switch (option) {
        //case SettingOptionTypeNewInspectionView: return bNewInspectionUI;
        case SettingOptionTypeDisplayInstruction: return bDisplayInstruction;
        case SettingOptionTypeEnableWord: return bEnableWord;
        case SettingOptionTypeSavePhotosToLibrary: return bSaveCamera;
        case SettingOptionTypeShowDateTimeStamp: return bPhotoStamp;
        case SettingOptionTypeSimpleRecorder: return @"";
        case SettingOptionTypeCameraAlternativeView: return @"";
        case SettingOptionTypeLocalEmailClient: return bLocalEmailClient;
        case SettingOptionTypeAutoLogin: return bSavePassword;
        case SettingOptionTypeDisplayTimer: return bDisplayInspectionTimer;
        case SettingOptionTypeShowPhotoGeoTag: return bPhotoGeoTag;
        default: return nil;
    }
}

#pragma mark - UITableViewDelegate

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    if (section == 0) return 0.001;
    return UITableViewAutomaticDimension;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    if (section == 0) { return nil; }
    SettingCatagoryHeaderView *view =
            [tableView dequeueReusableHeaderFooterViewWithClass:SettingCatagoryHeaderView.class];
    [view UpdateHeaderTitle: self.sortedKeys[(NSUInteger) (section - 1)]];
    return view;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    @try {
        [tableView deselectRowAtIndexPath:indexPath animated:YES];
        SettingOptionType option = [self optionForIndexPath: indexPath];
        switch (option) {
            case SettingOptionTypeStartChat:
                [Intercom presentMessageComposer:@""];
                break;
            default:
                break;
        }
    }
    @catch (NSException *exception) {
        [db_Log ProcessException:@"Exception" sMessage:[NSString stringWithFormat:@"%@ %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd)] exception:exception];
    }
}

- (void)btn_Logout {
    [CommonHelper IntercomLogout];
    [self.navigationController popToRootViewControllerAnimated:YES];
}

@end
