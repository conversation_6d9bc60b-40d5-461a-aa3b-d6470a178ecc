//
//  CommonJson.h
//  SnapInspect3
//
//  Created by <PERSON> on 13/05/16.
//  Copyright © 2016 SnapInspect. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "O_User.h"

extern NSString *const sKeyCustomRole;

typedef NSString *UserConfigKey NS_STRING_ENUM;
//when bM_Assets_Hide = "1" then hide assets tab.
extern UserConfigKey const UserConfigKeyAssetsHide;
extern UserConfigKey const UserConfigKeyEditInsHide;
extern UserConfigKey const UserConfigKeyCopyInsHide;

NS_ASSUME_NONNULL_BEGIN
@interface CommonJson : NSObject
+(nullable NSArray *)GetJsonArray:(NSString *)sKey sJson:(NSString *)sJson;
+(nullable NSString *)GetJsonKeyValue:(NSString *)sKey sJson:(NSString *)sJson;
+(NSString *)RemoveJsonKey:(NSString *)sKey sJson:(NSString *)sJson;
+(NSString *)AddJsonKeyValueString:(NSString *)sKey sValue:(id)sValue sJson:(NSString *)sJson;
+(bool)TestStringIsJson:(NSString *)sJson;
+(NSString *)sDictionaryToJsonString:(NSDictionary *)oDic;
+(nullable NSDictionary *)oJsonStringToDictionary:(NSString *)sJsonString;

+(nullable NSArray *)oJsonStringToArray:(NSString *)sJsonString;
+(NSString *)sArrayToJsonString:(NSArray *)oArray;
+(NSString *)GetPropertyManager:(int)iCustomerID;
+(NSArray *)GetAllUsers ;
+(NSString *)ConvertStringOrJsonToString:(id)oData;
+ (id)oJsonStringToObject:(NSString *)sJsonString;

+ (BOOL)isUserConfigEnabled: (UserConfigKey)sKey;
+ (nullable NSArray<O_User *> *)GetUsers;
+ (nullable O_User *)getUserByID:(NSInteger)iUserID;

+ (NSString *)addGpsValue:(CLLocationCoordinate2D)coordinate2D toJson:(NSString *)json;
+ (NSString *)addGpsDataToJson:(NSString *)json;

+ (NSString *)sGroupPermission;
+ (NSString *)simplifyJsonString:(NSString *)jsonString;
+ (BOOL)compareJsonStrings:(NSString *)str1 str2:(NSString *)str2;
@end
NS_ASSUME_NONNULL_END
