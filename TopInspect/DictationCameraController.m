//
//  DictationCameraController.m
//  SnapInspectCamera
//
//  Created by <PERSON> on 3/4/16.
//  Copyright © 2016 Osama Petran. All rights reserved.
//

#import "DictationCameraController.h"
#import <CoreMotion/CoreMotion.h>
#import "SCSlider.h"
@import Extensions;
#import <AVFoundation/AVFoundation.h>

#define SWITCH_SHOW_FOCUSVIEW_UNTIL_FOCUS_DONE      0
#define SWITCH_SHOW_DEFAULT_IMAGE_FOR_NONE_CAMERA   1

//height
#define CAMERA_TOPVIEW_HEIGHT   44  //title
#define THUMBNAIL_VIEW_HEIGHT   80  //thumbnail

//focus
#define ADJUSTING_FOCUS @"adjustingFocus"
#define LOW_ALPHA   0.7f
#define HIGH_ALPHA  1.0f

@interface DictationCameraController ()

@property (nonatomic, strong) SCCaptureSessionManager *captureManager;

@property (nonatomic, weak) UIView *lastSpacerView;
@property (nonatomic, weak) UIView *previewView;
@property (nonatomic, weak) NSMutableSet *cameraBtnSet;
@property (nonatomic, weak) UIImageView *focusImageView;
@property (nonatomic, weak) SCSlider *scSlider;
@property (nonatomic, weak) UILabel *zoomLevelLabel;
@property (nonatomic, weak) UIButton *shutterBtn;
@property (nonatomic, weak) UIImageView *thumbnailView;
@property (nonatomic, weak) SCSlider *exposureSlider;

@property (nonatomic, assign) BOOL doubleTouch;
@property (nonatomic, assign) int alphaTimes;
@property (nonatomic, assign) CGPoint currTouchPoint;
@property (nonatomic, assign) BOOL isPreviewConfigured;
@end

@implementation DictationCameraController

- (instancetype)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil {
    self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    if (self) {
        // Custom initialization
        self.alphaTimes = -1;
        self.currTouchPoint = CGPointZero;
        self.isPreviewConfigured = NO;
        self.cameraBtnSet = [NSMutableSet set];
    }
    return self;
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    return UIInterfaceOrientationMaskPortrait;
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation {
    return UIInterfaceOrientationPortrait;
}

- (BOOL)prefersStatusBarHidden {
    return YES;
}

- (void)viewDidLoad {
    [super viewDidLoad];

    // Setup views
    [self setupViews];
    [self updateViews];

    // Background
    self.view.backgroundColor = UIColor.blackColor;
    
    //navigation bar
    if (self.navigationController) {
        self.navigationController.navigationBarHidden = YES;
    }
    
    //status bar
    _isStatusBarHiddenBeforeShowCamera = [UIApplication sharedApplication].statusBarHidden;
    
    //notification
    [[NSNotificationCenter defaultCenter] removeObserver:self name:kNotificationOrientationChange object:nil];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(deviceOrientationDidChangeNotification)
                                                 name:UIDeviceOrientationDidChangeNotification
                                               object:nil];

    //session manager
    SCCaptureSessionManager *manager = [[SCCaptureSessionManager alloc] init];
    manager.photoSize = self.iPhotoSize;
    @weakify(self)
    manager.didCapturePhotoOutput = ^(UIImage *photo) {
        @strongify(self)
        [self didCapturePhoto: photo];
    };
    self.captureManager = manager;

    CMMotionManager *cmManager = [[CMMotionManager alloc] init];
    if ([cmManager isDeviceMotionAvailable]) {
        cmManager.deviceMotionUpdateInterval = 0.2;
        [cmManager startDeviceMotionUpdatesToQueue:[NSOperationQueue mainQueue] withHandler:
         ^(CMDeviceMotion *motion, NSError *error) {
             [self outputAccelertionData:motion.userAcceleration];
        }];
    }
#ifndef ORIG_CAMERA_PART
    if ([CommonHelper IFGetPref:bSaveCamera] != nil && [[CommonHelper IFGetPref:bSaveCamera] boolValue]){
        
    }
#endif
    UITapGestureRecognizer *singleTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(tapDetected:)];
    singleTap.numberOfTapsRequired = 1;
    [self.thumbnailView setUserInteractionEnabled:YES];
    [self.thumbnailView addGestureRecognizer:singleTap];
    
    // Start speech recognition if in dictate mode
    if ([self respondsToSelector:@selector(startSpeechRecognition)]) {
        [self performSelector:@selector(startSpeechRecognition) withObject:nil afterDelay:0.5];
    }
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    // Check the view layout is complete and the preview view has the correct bounds.
    if (self.previewView.height > 0 && self.previewView.width == self.previewView.height) {
        [self configureCaptureManager];
        self.isPreviewConfigured = YES;
    }
}

// Invoke this method only once.
- (void)configureCaptureManager {
    if (self.isPreviewConfigured || self.captureManager.session.isRunning) return;
    CGRect previewRect = [self.previewView boundsInView:self.view];
    [self.captureManager configureWithParentLayer:self.view previewRect:previewRect];
    [self.captureManager startSession];
    [self.view removeAllGestureRecognizers];
    [self addFocusView];
    [self addPinchGesture];
    [self updateViews];
}

- (void)setupViews {
    UIStackView *stackView = [[UIStackView alloc] init];
    stackView.axis = UILayoutConstraintAxisVertical;
    stackView.distribution = UIStackViewDistributionFill;
    stackView.alignment = UIStackViewAlignmentFill;
    stackView.spacing = 10.0;
    [self.view addSubview:stackView];
    [stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop);
        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom);
        make.left.equalTo(self.view.mas_safeAreaLayoutGuideLeft);
        make.right.equalTo(self.view.mas_safeAreaLayoutGuideRight);
    }];
    self.rootStackView = stackView;

    [self addTopViewWithText];
    [self addSpacerViewToRootStackView];
    [self addPreviewView];
    [self addSpacerViewToRootStackView];
    [self setupSegmentedControls];
    [self addSpacerViewToRootStackView];
    [self addCameraMenuView];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    self.navigationController.navigationBarHidden = YES;
    int iCurrentPhotoID = (int)self.thumbnailView.tag;
    BOOL isExist = NO;
    
    if (_iSaveOption == CameraSaveOptionPhoto){
        NSArray *arrPhoto = [db_Media SearchPhotos:iCurrentPhotoID iInsItemID:0 iInsID:0];
        if (arrPhoto != nil && [arrPhoto count] == 1){
            isExist = YES;
        }
    } else if (_iSaveOption == CameraSaveOptionFile){
        O_File* oFile = [db_Media GetFile:iCurrentPhotoID];
        if (oFile != nil && (oFile.iFileID > 0)) {
            isExist = YES;
        }
    }
    
    if (!isExist) {
        self.thumbnailView.tag = 0;
        self.thumbnailView.image = nil;
    }

    // Update the location
    [LocationManager.shared updateLocation: nil];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [self.captureManager checkCameraPermission];
    [self deviceOrientationDidChangeNotification];
}

- (void)tapDetected:(UITapGestureRecognizer*)tap {
    if ([tap.view isKindOfClass:[UIImageView class]]){
        int iCurrentPhotoID = (int)tap.view.tag;
        if (iCurrentPhotoID == 0) {
            return;
        }

        if ([self.delegate respondsToSelector:@selector(PostPreviewPhotoID:iInsItemID:iParamID:)]) {
            [self.delegate PostPreviewPhotoID:iCurrentPhotoID iInsItemID:(int)self.iInsItemID iParamID:(int)self.iParamID];
        }
    }
}

- (void)outputAccelertionData:(CMAcceleration)acceleration {
    if (acceleration.z > 0.5 ||
        acceleration.z < - 0.5) {
        CGPoint centerPoint = CGPointMake(SCREEN_WIDTH / 2, SCREEN_WIDTH / 2 + CAMERA_TOPVIEW_HEIGHT);
        [self.captureManager focusInPoint:centerPoint];
        self.focusImageView.center = centerPoint;
        self.focusImageView.transform = CGAffineTransformMakeScale(2.0, 2.0);
        
#if SWITCH_SHOW_FOCUSVIEW_UNTIL_FOCUS_DONE
        [UIView animateWithDuration:0.1f animations:^{
            self.focusImageView.alpha = HIGH_ALPHA;
            self.focusImageView.transform = CGAffineTransformMakeScale(1.0, 1.0);
        } completion:^(BOOL finished) {
            [self showFocusInPoint:centerPoint];
        }];
#else
        [UIView animateWithDuration:0.3f delay:0.f options:UIViewAnimationOptionAllowUserInteraction animations:^{
            self.focusImageView.alpha = 1.f;
            self.focusImageView.transform = CGAffineTransformMakeScale(1.0, 1.0);
        } completion:^(BOOL finished) {
            [UIView animateWithDuration:0.5f delay:0.5f options:UIViewAnimationOptionAllowUserInteraction animations:^{
                self.focusImageView.alpha = 0.f;
            } completion:nil];
        }];
#endif
    }

}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)dealloc {
    if (!self.navigationController) {
        if ([UIApplication sharedApplication].statusBarHidden != _isStatusBarHiddenBeforeShowCamera) {
            [[UIApplication sharedApplication] setStatusBarHidden:_isStatusBarHiddenBeforeShowCamera withAnimation:UIStatusBarAnimationSlide];
        }
    }
    
    [[NSNotificationCenter defaultCenter] removeObserver:self name:kNotificationOrientationChange object:nil];
    
#if SWITCH_SHOW_FOCUSVIEW_UNTIL_FOCUS_DONE
    AVCaptureDevice *device = [AVCaptureDevice defaultDeviceWithMediaType:AVMediaTypeVideo];
    if (device && [device isFocusPointOfInterestSupported]) {
        [device removeObserver:self forKeyPath:ADJUSTING_FOCUS context:nil];
    }
#endif
    
    self.captureManager = nil;
    
    [[AVAudioSession sharedInstance] setActive:NO error:nil];
}

#pragma mark -------------UI---------------

- (UIView *)createSpacerView {
    UIView *spacerView = [[UIView alloc] init];
    spacerView.backgroundColor = [UIColor clearColor];
    return spacerView;
}

- (void)addSpacerViewToRootStackView {
    UIView *spacerView = [self createSpacerView];
    [self.rootStackView addArrangedSubview:spacerView];
    if (self.lastSpacerView != nil) {
        [spacerView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(self.lastSpacerView.mas_height);
        }];
    }
    self.lastSpacerView = spacerView;
}

- (void)addTopViewWithText {
    // Create container view for the top bar
    UIView *topContainer = [[UIView alloc] init];
    [self.rootStackView addArrangedSubview:topContainer];
    [topContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@(CAMERA_TOPVIEW_HEIGHT));
    }];
    
    UIButton *btn;
    NSString *flash_mode;
    if (FLASH_MODE == 0)
        flash_mode = @"flash-auto.png";
    else if (FLASH_MODE == 1)
        flash_mode = @"flash.png";
    else
        flash_mode = @"flash-off.png";
    
    // Flash button - aligned to left
    btn = [self buildButton:CGRectZero
               normalImgStr:flash_mode
            highlightImgStr:@""
             selectedImgStr:@""
                     action:@selector(flashBtnPressed:)];
    btn.showsTouchWhenHighlighted = YES;
    [self.cameraBtnSet addObject:btn];
    [topContainer addSubview:btn];
    [btn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(topContainer).offset(16);
        make.centerY.equalTo(topContainer);
        make.width.equalTo(@(64.0));
        make.height.equalTo(@(44.0));
    }];
    
    // Right side container for switch camera and library buttons
    UIStackView *rightStackView = [[UIStackView alloc] init];
    rightStackView.axis = UILayoutConstraintAxisHorizontal;
    rightStackView.distribution = UIStackViewDistributionFill;
    rightStackView.alignment = UIStackViewAlignmentCenter;
    rightStackView.spacing = 8.0f;
    [topContainer addSubview:rightStackView];
    [rightStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(topContainer).offset(-16);
        make.centerY.equalTo(topContainer);
        make.height.equalTo(@(44.0));
    }];
    
    // switch camera button
    btn = [self buildButton:CGRectZero
               normalImgStr:@"front-camera.png"
            highlightImgStr:@""
             selectedImgStr:@""
                     action:@selector(switchCameraBtnPressed:)];
    btn.showsTouchWhenHighlighted = YES;
    [self.cameraBtnSet addObject:btn];
    [rightStackView addArrangedSubview:btn];
    [btn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@(44.0));
        make.height.equalTo(@(44.0));
    }];
    
    // library button
    btn = [self buildButton:CGRectZero
               normalImgStr:@"library.png"
            highlightImgStr:@""
             selectedImgStr:@""
                     action:@selector(openLibrary:)];
    btn.showsTouchWhenHighlighted = YES;
    [self.cameraBtnSet addObject:btn];
    [rightStackView addArrangedSubview:btn];
    [btn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@(44.0));
        make.height.equalTo(@(44.0));
    }];
    
    // Zoom level view - centered in remaining space
    UIView *zoomView = [[UIView alloc] init];
    [topContainer addSubview:zoomView];
    
    // Zoom level label
    UILabel *zoomLevelLabel = [[UILabel alloc] init];
    zoomLevelLabel.font = [UIFont SanFranciscoText_Regular:14.0f];
    zoomLevelLabel.textColor = [UIColor whiteColor];
    zoomLevelLabel.textAlignment = NSTextAlignmentCenter;
    [zoomView addSubview:zoomLevelLabel];
    self.zoomLevelLabel = zoomLevelLabel;
    
    UILabel *xLabel = [[UILabel alloc] init];
    xLabel.font = [UIFont SanFranciscoText_Regular:14.0f];
    xLabel.textColor = [UIColor whiteColor];
    xLabel.text = @"x";
    [zoomView addSubview:xLabel];
    
    [zoomLevelLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.bottom.equalTo(zoomView);
    }];
    
    [xLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(zoomLevelLabel.mas_right).offset(2);
        make.centerY.equalTo(zoomLevelLabel);
        make.right.equalTo(zoomView);
    }];
    
    [zoomView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(topContainer);
        make.centerY.equalTo(topContainer);
    }];
}

- (void)addPreviewView {
    UIView *view = [[UIView alloc] init];
    [self.rootStackView addArrangedSubview:view];
    [view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(view.mas_width);
    }];
    self.previewView = view;
}

- (void)addCameraMenuView {
    UIStackView *stackView = [[UIStackView alloc] init];
    stackView.axis = UILayoutConstraintAxisHorizontal;
    stackView.distribution = UIStackViewDistributionFill;
    stackView.alignment = UIStackViewAlignmentCenter;
    stackView.spacing = 20.0f;
    [self.rootStackView addArrangedSubview:stackView];

    //Add Done button
    UIButton *doneBtn = [[UIButton alloc] initWithFrame:CGRectZero];
    [doneBtn setTitle:@"Done" forState:UIControlStateNormal];
    [doneBtn addTarget:self action:@selector(dismissBtnPressed:) forControlEvents:UIControlEventTouchUpInside];
    doneBtn.showsTouchWhenHighlighted = YES;
    [stackView addArrangedSubview:doneBtn];
    [self.cameraBtnSet addObject:doneBtn];
    [doneBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.equalTo(@(CGSizeMake(80.0, 40.0)));
    }];
    
    // Add 0 width spacer view
    UIView *spacerNoWidth = [self createSpacerView];
    [stackView addArrangedSubview:spacerNoWidth];
    [spacerNoWidth mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@0.0);
    }];
    
    // Add spacer view
    UIView *spacerView1 = [self createSpacerView];
    [stackView addArrangedSubview:spacerView1];
    
    //Add Shutter button
    UIButton *shutBtn = [self buildButton:CGRectZero
                             normalImgStr:@"take-snap.png"
                          highlightImgStr:@""
                           selectedImgStr:@"taken-snap.png"
                                   action:@selector(takePictureBtnPressed:)];
    [self.cameraBtnSet addObject:shutBtn];
    [stackView addArrangedSubview:shutBtn];
    [shutBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@(THUMBNAIL_VIEW_HEIGHT));
        make.height.equalTo(@(THUMBNAIL_VIEW_HEIGHT));
    }];
    self.shutterBtn = shutBtn;
    
    // Set initial shutter button state
    [self updateShutterButtonState];

    // Add spacer view
    UIView *spacerView2 = [self createSpacerView];
    [stackView addArrangedSubview:spacerView2];
    [spacerView2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(spacerView1.mas_width);
    }];
    
    //Add Thumbnail view
    UIImageView *imgView = [[UIImageView alloc] init];
    imgView.clipsToBounds = YES;
    imgView.contentMode = UIViewContentModeScaleAspectFill;
    [stackView addArrangedSubview:imgView];
    [imgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@(THUMBNAIL_VIEW_HEIGHT));
        make.height.equalTo(@(THUMBNAIL_VIEW_HEIGHT));
    }];
    self.thumbnailView = imgView;

    // Add spacer view
    spacerNoWidth = [self createSpacerView];
    [stackView addArrangedSubview:spacerNoWidth];
    [spacerNoWidth mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.equalTo(@0.0);
    }];
}

- (UIButton*)buildButton:(CGRect)frame
            normalImgStr:(NSString*)normalImgStr
         highlightImgStr:(NSString*)highlightImgStr
          selectedImgStr:(NSString*)selectedImgStr
                  action:(SEL)action {
    
    UIButton *btn = [UIButton buttonWithType:UIButtonTypeCustom];
    btn.frame = frame;
    if (normalImgStr.length > 0) {
        [btn setImage:[UIImage imageNamed:normalImgStr] forState:UIControlStateNormal];
    }
    if (highlightImgStr.length > 0) {
        [btn setImage:[UIImage imageNamed:highlightImgStr] forState:UIControlStateHighlighted];
    }
    if (selectedImgStr.length > 0) {
        [btn setImage:[UIImage imageNamed:selectedImgStr] forState:UIControlStateSelected];
    }
    [btn addTarget:self action:action forControlEvents:UIControlEventTouchUpInside];
    
    return btn;
}

- (void)addFocusView {
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleTapFocus:)];
    [self.view addGestureRecognizer:tap];
    
    UIImageView *imgView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"focus-crosshair.png"]];
    imgView.alpha = 0;
    [self.view addSubview:imgView];
    self.focusImageView = imgView;
    
#if SWITCH_SHOW_FOCUSVIEW_UNTIL_FOCUS_DONE
    AVCaptureDevice *device = [AVCaptureDevice defaultDeviceWithMediaType:AVMediaTypeVideo];
    if (device && [device isFocusPointOfInterestSupported]) {
        [device addObserver:self forKeyPath:ADJUSTING_FOCUS options:NSKeyValueObservingOptionNew|NSKeyValueObservingOptionOld context:nil];
    }
#endif
    
    // brightness slider
    SCSlider *slider = [[SCSlider alloc] initWithFrame:CGRectZero direction:SCSliderDirectionVertical];
    slider.minValue = self.captureManager.minExposureTargetBias;
    slider.maxValue = self.captureManager.maxExposureTargetBias;
    [slider setValue:self.captureManager.currentExposureTargetBias shouldCallBack:NO];
    @weakify(self)
    [slider buildDidChangeValueBlock:^(CGFloat value) {
        @strongify(self)
        self.captureManager.currentExposureTargetBias = value;
    }];
    [slider buildTouchEndBlock:^(CGFloat value, BOOL isTouchEnd) {
        @strongify(self)
        [self setSlider:self.exposureSlider alpha:isTouchEnd];
        [self setFocusImageViewAlpha:isTouchEnd];
    }];
    
    [self.view addSubview:slider];
    [slider mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(imgView.mas_trailing);
        make.width.equalTo(@(44.0));
        make.centerY.equalTo(imgView);
        make.height.equalTo(@(200.0));
    }];
    slider.alpha = 0.f;
    self.exposureSlider = slider;
}

#pragma mark -------------touch to focus---------------
#if SWITCH_SHOW_FOCUSVIEW_UNTIL_FOCUS_DONE
- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary *)change context:(void *)context {
    if ([keyPath isEqualToString:ADJUSTING_FOCUS]) {
        BOOL isAdjustingFocus = [[change objectForKey:NSKeyValueChangeNewKey] isEqualToNumber:[NSNumber numberWithInt:1] ];

        if (!isAdjustingFocus) {
            alphaTimes = -1;
        }
    }
}

- (void)showFocusInPoint:(CGPoint)touchPoint {
    
    [UIView animateWithDuration:0.1f delay:0 options:UIViewAnimationOptionAllowUserInteraction animations:^{
        
        int alphaNum = (alphaTimes % 2 == 0 ? HIGH_ALPHA : LOW_ALPHA);
        self.focusImageView.alpha = alphaNum;
        alphaTimes++;
        
    } completion:^(BOOL finished) {
        
        if (alphaTimes != -1) {
            [self showFocusInPoint:currTouchPoint];
        } else {
            self.focusImageView.alpha = 0.0f;
        }
    }];
}
#endif

- (void)handleTapFocus:(UITapGestureRecognizer *)tap {
    self.alphaTimes = -1;
    self.currTouchPoint = [tap locationInView:self.view];
    CGRect previewRect = [self.previewView boundsInView:self.view];
    if (CGRectContainsPoint(previewRect, self.currTouchPoint) == NO) {
        return;
    }
    
    // Ignore touches that are inside the exposure slider
    if (CGRectContainsPoint(self.exposureSlider.frame, self.currTouchPoint)) {
        return;
    }
    
    if (self.currTouchPoint.x < CAMERA_TOPVIEW_HEIGHT) return;
    if (self.doubleTouch) return;
    
    [self.captureManager focusInPoint:self.currTouchPoint];
    
    [self.focusImageView setCenter:self.currTouchPoint];
    self.focusImageView.transform = CGAffineTransformMakeScale(2.0, 2.0);
    
#if SWITCH_SHOW_FOCUSVIEW_UNTIL_FOCUS_DONE
    [UIView animateWithDuration:0.1f animations:^{
        self.focusImageView.alpha = HIGH_ALPHA;
        self.focusImageView.transform = CGAffineTransformMakeScale(1.0, 1.0);
    } completion:^(BOOL finished) {
        [self showFocusInPoint:currTouchPoint];
    }];
#else
    [UIView animateWithDuration:0.3f delay:0.f options:UIViewAnimationOptionAllowUserInteraction animations:^{
        self.focusImageView.alpha = 1.f;
        self.focusImageView.transform = CGAffineTransformMakeScale(1.0, 1.0);
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.5f delay:0.5f options:UIViewAnimationOptionAllowUserInteraction animations:^{
            self.focusImageView.alpha = 0.f;
        } completion:nil];
    }];
#endif
    
    // show the exposure slider
    [self updateExposureSliderVisible:YES];
    [self setSlider:self.exposureSlider alpha:YES];
}

#pragma mark -------------button actions---------------
- (void)takePictureBtnPressed:(UIButton*)sender {
#if SWITCH_SHOW_DEFAULT_IMAGE_FOR_NONE_CAMERA
    if (![UIImagePickerController isSourceTypeAvailable:UIImagePickerControllerSourceTypeCamera]) {
        [self ShowAlert:@"Error!" sMessage:@"Device does not support the camera."];
        return;
    }
#endif
    
    [sender setSelected:YES];
    sender.userInteractionEnabled = NO;
    
    [self.captureManager takePicture];
}

- (void)image:(UIImage *)image didFinishSavingWithError:(NSError *)error contextInfo:(void *)contextInfo {
    if (error != NULL) {
        [self ShowAlert: @"Error" sMessage: @"Failed to save"];
    } else {
        DLog(@"Save successfully");
    }
}

#ifndef ORIG_CAMERA_PART

- (UIImage *)addTextsIfNeeded: (UIImage *)resImg {
    BOOL bPhotoStampValue = [CommonHelper IFGetPref_Bool: bPhotoStamp] || CommonPermission.shouldTurnOnPhotoDate;
    NSMutableArray *texts = [NSMutableArray array];
    if (bPhotoStampValue) {
        NSString *timeStamp = [CommonHelper GetDateStrFromDate: @"yyyy-MM-dd HH:mm" date: [NSDate new]];
        [texts addObject: timeStamp];
    }

    BOOL bPhotoGeoTagValue = [CommonHelper IFGetPref_Bool: bPhotoGeoTag] || CommonPermission.shouldTurnOnLocation;
    if (bPhotoGeoTagValue && LocationManager.shared.currentLocation != nil)
        [texts addObject: self.geoTag];

    if (texts.count > 0) {
        NSDictionary<NSAttributedStringKey, id> * textFontAttributes = @{
            NSFontAttributeName: [UIFont fontWithName: @"Arial" size: 30],
            NSForegroundColorAttributeName: UIColor.redColor
        };
        NSString *text = [texts componentsJoinedByString: @"\n"];
        CGSize textSize = [text sizeWithMaxSize: resImg.size attributes: textFontAttributes];
        resImg = [resImg drawText: text
                startPoint: CGPointMake(10, resImg.size.height - textSize.height - 10.0)
                textFontAttributes: textFontAttributes
                scale: 1.0];
    }
    return resImg;
}

-(UIImage *)addText:(UIImage *)img text:(NSString *)text1 {

    size_t w = (size_t) img.size.width, h = (size_t) img.size.height;

    CGColorSpaceRef colorSpace = CGColorSpaceCreateDeviceRGB();
    CGContextRef context = CGBitmapContextCreate(NULL, w, h, 8, 4 * w, colorSpace, kCGImageAlphaPremultipliedFirst);
    CGContextDrawImage(context, CGRectMake(0, 0, w, h), img.CGImage);

    char *text = (char *) [text1 cStringUsingEncoding:NSASCIIStringEncoding];
    CGContextSelectFont(context, "Arial", 30, kCGEncodingMacRoman);
    CGContextSetTextDrawingMode(context, kCGTextFill);
    CGContextSetRGBFillColor(context, 255, 0, 0, 1);
    CGContextShowTextAtPoint(context, 10, 10, text, strlen(text));
    CGImageRef imgCombined = CGBitmapContextCreateImage(context);

    CGContextRelease(context);
    CGColorSpaceRelease(colorSpace);

    UIImage *retImage = [UIImage imageWithCGImage:imgCombined];
    CGImageRelease(imgCombined);
    return retImage;
}

/*- (UIImage *) processImage_Thumb:(UIImage *)image { //process captured image, crop, resize and rotate
    
    UIImage *smallImage = [self imageWithImage:image scaledToWidth:80.0f]; //UIGraphicsGetImageFromCurrentImageContext();
    
    CGRect cropRect = CGRectMake(0, 0, 80,80 );
    CGImageRef imageRef = CGImageCreateWithImageInRect([smallImage CGImage], cropRect);
    
    
    UIImage *croppedImage = [UIImage imageWithCGImage:imageRef];
    
    
    CGImageRelease(imageRef);
    return croppedImage;
}

- (UIImage*)imageWithImage:(UIImage *)sourceImage scaledToWidth:(float) i_width
{
    float oldWidth = sourceImage.size.width;
    float scaleFactor = i_width / oldWidth;
    
    float newHeight = sourceImage.size.height * scaleFactor;
    float newWidth = oldWidth * scaleFactor;
    
    UIGraphicsBeginImageContext(CGSizeMake(newWidth, newHeight));
    [sourceImage drawInRect:CGRectMake(0, 0, newWidth, newHeight)];
    UIImage *newImage = UIGraphicsGetImageFromCurrentImageContext();
    UIGraphicsEndImageContext();
    return newImage;
}*/
#endif

- (void)dismissBtnPressed:(id)sender {
    [self dismissCamera];
    // [self dismissViewControllerAnimated:YES completion:nil];
    
    // Stop speech recognition if it's running
    [self stopSpeechRecognition];
}

- (void)dismissCamera {
    if (self.navigationController) {
        if (self.navigationController.viewControllers.count == 1) {
            [self.navigationController dismissViewControllerAnimated:YES completion:nil];
        } else {
            [self.navigationController popToViewController:self animated:YES];
            [self.navigationController popViewControllerAnimated:YES];
        }
    } else {
        [self dismissViewControllerAnimated:YES completion:nil];
    }
}

- (void)switchCameraBtnPressed:(UIButton*)sender {
    sender.selected = !sender.selected;
    // Prevent user from tapping to take photo while switching camera
    self.shutterBtn.userInteractionEnabled = NO;
    [self.captureManager switchCamera:sender.selected];
    self.shutterBtn.userInteractionEnabled = YES;
    [self updateViews];
}

- (void)flashBtnPressed:(UIButton*)sender {
    [self.captureManager switchFlashMode:sender];
}

- (void)openLibrary:(UIButton*)sender {
    @weakify(self);
    NSInteger maxLimit = _iOption == CameraOptionSingle ? 1 : kMaxImageCount;

    [PhotoLibraryManager.shared
            openLibraryFrom:self
                 sourceView:sender
                   maxLimit:maxLimit
                   editable:YES
                 completion:^(NSArray<UIImage *> *images) {
        @strongify(self);
        if (images.count > 0) {
            [self processImages:images];
        }
    }];
}

- (void)processImages:(NSArray *)images {
    MBProgressHUD *hud = [[MBProgressHUD alloc] si_initWithView:self.view];
    [self.view addSubview:hud];
    hud.labelText = @"Importing Photo...";
    
    @weakify(self);
    [hud showAnimated:YES whileExecutingBlock:^{
        @strongify(self);
        for (UIImage * chosenImage in images) {
            [[NSOperationQueue mainQueue] addOperationWithBlock:^{
                UIImage *resImg;
                if (chosenImage.size.width > self.iPhotoSize) {
                    CGFloat width = chosenImage.size.width, height = chosenImage.size.height;
                    CGFloat squareLength = self.iPhotoSize;
                    CGFloat ratio = squareLength / MAX(width, height);
                    CGSize size = CGSizeMake(ratio * width, ratio * height);
                    UIImage *scaleImg = [self resizeImageWithImage:chosenImage toSize:size];
                    resImg = [self mergeImageWithWhiteBG:scaleImg];
                } else {
                    resImg = chosenImage;
                }
                
    #ifndef ORIG_CAMERA_PART
                resImg = [self addTextsIfNeeded: resImg];
    #endif
                //   dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                //#ifndef ORIG_CAMERA_PART
                if ([CommonHelper IFGetPref:bSaveCamera] != nil && [[CommonHelper IFGetPref:bSaveCamera] boolValue]){
                    [CommonHelper saveImageToCameraRoll: resImg completion:^(BOOL success) { }];
                }
                if (_iSaveOption == CameraSaveOptionPhoto){
                    NSString *sPath = [[if_AppDelegate GetGlobalVar] valueForKeyPath:@"sFilePath"];
                    NSString *sUniqueFileName = [CommonHelper GetUniqueFileName];
                    NSString *sPhotoName = [NSString stringWithFormat:@"p_%@.jpg", sUniqueFileName];
                    NSString *sThumbName = [NSString stringWithFormat:@"pt_%@.jpg", sUniqueFileName];
                    NSString *sPhotoPath = [sPath stringByAppendingPathComponent: sPhotoName];
                    NSString *sThumbPath = [sPath stringByAppendingPathComponent: sThumbName];
                    [UIImageJPEGRepresentation(resImg, 0.8)  writeToFile:sPhotoPath atomically:YES];
                    [UIImage writeGpsDataFrom:LocationManager.shared.currentLocation.coordinate
                                           to:sPhotoPath completion:nil];

                    UIImage *oTempImage = [CommonMedia processImage_Thumb:resImg];
                    [UIImageJPEGRepresentation(oTempImage, 0.8)  writeToFile:sThumbPath atomically:YES];
                    [UIImage writeGpsDataFrom:LocationManager.shared.currentLocation.coordinate
                                           to:sThumbPath completion:nil];

                    O_Photo *oPhoto = [[O_Photo alloc] init];
                    oPhoto.iInsItemID = (int)self.iInsItemID;
                    oPhoto.iInsID =  (int)self.iInsID;
                    oPhoto.iSize = 0;
                    oPhoto.sFile = sPhotoName;
                    oPhoto.sThumb = sThumbName;
                    oPhoto.iSPhotoID = 0;
                    oPhoto.iPhotoID = 0;
                    oPhoto.sField1 = [CommonJson addGpsDataToJson:oPhoto.sField1];
                    oPhoto.sField2 = @"";
                    oPhoto.sField3 = @"";

                    int iPhotoID = [db_Media UpdatePhoto:oPhoto];
                    
                    [self.delegate PostProcessPhotoID:iPhotoID iInsItemID:(int)self.iInsItemID iParamID:(int)self.iParamID];
                    
                    self.thumbnailView.image = resImg;
                    self.thumbnailView.tag = iPhotoID;
                }
                else if (_iSaveOption == CameraSaveOptionFile){
                    NSString *sPath = [[if_AppDelegate GetGlobalVar] valueForKeyPath:@"sFilePath"];
                    NSString *sUniqueFileName = [CommonHelper GetUniqueFileName];
                    NSString *sPhotoName = [NSString stringWithFormat:@"f_%@.jpg", sUniqueFileName];
                    //NSString *sThumbName = [NSString stringWithFormat:@"pt_%@.jpg", sUniqueFileName];
                    NSString *sPhotoPath = [sPath stringByAppendingPathComponent: sPhotoName];
                    //NSString *sThumbPath = [sPath stringByAppendingPathComponent: sThumbName];
                    [UIImageJPEGRepresentation(resImg, 0.8)  writeToFile:sPhotoPath atomically:YES];
                    //UIImage *oTempImage = [self processImage_Thumb:stillImage];
                    
                    //[UIImageJPEGRepresentation(oTempImage, 0.8)  writeToFile:sThumbPath atomically:YES];
                    /*O_Photo *oPhoto = [[O_Photo alloc] init];
                     oPhoto.iInsItemID = iInsItemID;
                     oPhoto.iInsID =  iInsID;
                     oPhoto.iSize = 0;
                     oPhoto.sFile = sPhotoName;
                     oPhoto.sThumb = sThumbName;
                     oPhoto.iSPhotoID = 0;
                     oPhoto.iPhotoID = 0;
                     int iPhotoID = [db_Media UpdatePhoto:oPhoto];
                     
                     O_File */
                    O_File *oFile = [[O_File alloc] init];
                    oFile.iSObjectID = (int)_iSObjectID;
                    oFile.iSFileID = 0;
                    oFile.iFileID = 0;
                    oFile.sFile = sPhotoName;
                    oFile.sLat = @"";
                    oFile.sLong = @"";
                    oFile.sComments = @"";
                    oFile.bUploaded = 0;
                    oFile.bDeleted = 0;
                    oFile.dtDateTime = [CommonHelper GetDateString:[NSDate new]];
                    oFile.iSize = 0;
                    oFile.sCustom1 = @"";
                    oFile.sCustom2 = @"";
                    int iFileID = [db_Media UpdateFile:oFile];
                    [self.delegate PostProcessFileID:iFileID iSObjectID:(int)_iSObjectID];
                    //#endif
                    
                    //        });
                    self.thumbnailView.image = resImg;
                    self.thumbnailView.tag = iFileID;
                }

            }];
        }
    } completionBlock:^{
        @strongify(self);
        [hud removeFromSuperview];
        
        if (_iOption == CameraOptionSingle) {
            [self performSelector:@selector(dismissCamera) withObject:nil afterDelay:0.5f];
        } else {
            [self ShowAlert: @"Success" sMessage: @"Photo Imported"];
        }
    }];
}

- (NSString *)geoTag {
    CLLocationCoordinate2D coordinate2D = LocationManager.shared.currentLocation.coordinate;
    return [NSString stringWithFormat:@"%f,%f", coordinate2D.latitude, coordinate2D.longitude];;
}

- (UIImage*)resizeImageWithImage:(UIImage*)image toSize:(CGSize)newSize {
    // Create a graphics image context
    UIGraphicsBeginImageContext(newSize);
    
    // draw in new context, with the new size
    [image drawInRect:CGRectMake(0, 0, newSize.width, newSize.height)];
    
    // Get the new image from the context
    UIImage* newImage = UIGraphicsGetImageFromCurrentImageContext();
    
    // End the context
    UIGraphicsEndImageContext();
    
    return newImage;
}

- (UIImage*)mergeImageWithWhiteBG:(UIImage*)image {
    if (image.size.width == image.size.height) {
        return image;
    }
    
    UIImage *bottomImage = [UIImage imageNamed:@"white_bg.png"];
    
    CGFloat squareLength = self.iPhotoSize;
    CGSize newSize = CGSizeMake(squareLength, squareLength);
    UIGraphicsBeginImageContext( newSize );
    
    // Use existing opacity as is
    [bottomImage drawInRect:CGRectMake(0,0,newSize.width,newSize.height)];
    
    // Apply supplied opacity if applicable
    CGFloat offx, offy;
    if (image.size.width > image.size.height) {
        offy = (squareLength - image.size.height)/2;
        offx = 0;
    } else {
        offx = (squareLength - image.size.width)/2;
        offy = 0;
    }
    

    [image drawInRect:CGRectMake(offx,offy,image.size.width, image.size.height)];
    
    UIImage *newImage = UIGraphicsGetImageFromCurrentImageContext();
    
    UIGraphicsEndImageContext();
    return newImage;
}

//#pragma mark -------------save image to local---------------
//- (void)saveImageToPhotoAlbum:(UIImage*)image {
//    UIImageWriteToSavedPhotosAlbum(image, self, @selector(image:didFinishSavingWithError:contextInfo:), nil);
//}
//
//- (void)image:(UIImage *)image didFinishSavingWithError:(NSError *)error contextInfo:(void *)contextInfo {
//    if (error != NULL) {
//        UIAlertView *alert = [[UIAlertView alloc] initWithTitle:@"Error!" message:@"Can not save" delegate:self cancelButtonTitle:@"Ok" otherButtonTitles:nil, nil];
//        [alert show];
//    } else {
//    }
//}

- (void)addPinchGesture {
    UIPinchGestureRecognizer *pinch = [[UIPinchGestureRecognizer alloc] initWithTarget:self action:@selector(handlePinch:)];
    [self.view addGestureRecognizer:pinch];
    
    //    CGFloat width = _previewRect.size.width - 100;
    //    CGFloat height = 40;
    //    SCSlider *slider = [[SCSlider alloc] initWithFrame:CGRectMake((SC_APP_SIZE.width - width) / 2, SC_APP_SIZE.width + CAMERA_MENU_VIEW_HEIGH - height, width, height)];
    
    CGFloat width = 40;
    CGFloat offsetY = 20.f;
    SCSlider *slider = [[SCSlider alloc] initWithFrame:CGRectZero direction:SCSliderDirectionVertical];
    slider.alpha = 0.f;
    slider.minValue = self.captureManager.minScaleNum;
    slider.maxValue = self.captureManager.maxScaleNum;
    slider.value = self.captureManager.scaleNum;
    
    @weakify(self)
    [slider buildDidChangeValueBlock:^(CGFloat value) {
        @strongify(self)
        [self.captureManager pinchCameraViewWithScalNum:value];
    }];
    [slider buildTouchEndBlock:^(CGFloat value, BOOL isTouchEnd) {
        @strongify(self)
        [self setSlider:self.scSlider alpha:isTouchEnd];
    }];
    
    [self.view addSubview:slider];
    [slider mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(self.previewView).offset(- 2 * offsetY);
        make.width.equalTo(@(width));
        make.centerY.equalTo(self.previewView);
        make.right.equalTo(self.previewView);
    }];
    self.scSlider = slider;
    
    // restore the default zoom level
    CGFloat defaultScaleNum = [CommonHelper IFGetPref: Constants.kCameraZoomLevel] == nil ?
    1.0 : [[CommonHelper IFGetPref: Constants.kCameraZoomLevel] floatValue];
    self.scSlider.value = defaultScaleNum;
    [self.captureManager pinchCameraViewWithScalNum:slider.value];
}

- (void)updateViews {
    [self updateShutterButtonState];
    
    self.scSlider.minValue = self.captureManager.minScaleNum;
    self.scSlider.maxValue = self.captureManager.maxScaleNum;
    self.scSlider.value = self.captureManager.scaleNum;
    
    self.exposureSlider.minValue = self.captureManager.minExposureTargetBias;
    self.exposureSlider.maxValue = self.captureManager.maxExposureTargetBias;
    self.exposureSlider.value = self.captureManager.currentExposureTargetBias;
    
    [self updateZoomLevel];
    [self updateSubtitleOverlayPosition];
}

- (void)updateShutterButtonState {
    // Only update the shutter button state if in dictate mode
    BOOL hasInspectionItem = self.iInsItemID > 0 && self.iParamID > 0;
    self.shutterBtn.userInteractionEnabled = hasInspectionItem;
    self.shutterBtn.alpha = hasInspectionItem ? 1.0 : 0.5;
}

- (void)updateZoomLevel {
    CGFloat zoomLevel = self.captureManager.cameraZoomFactorMultiplier * self.captureManager.scaleNum;
    self.zoomLevelLabel.text = [NSString stringWithFormat:@"%.1f", zoomLevel];
}

- (void)setSlider:(SCSlider *)slider alpha:(BOOL)isTouchEnd {
    if (slider) {
        slider.isSliding = !isTouchEnd;
        if (slider.alpha != 0.f && !slider.isSliding) {
            double delayInSeconds = 3.88;
            dispatch_time_t popTime = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayInSeconds * NSEC_PER_SEC));
            dispatch_after(popTime, dispatch_get_main_queue(), ^(void){
                if (slider.alpha != 0.f && !slider.isSliding) {
                    [UIView animateWithDuration:0.3f animations:^{
                        slider.alpha = 0.f;
                    }];
                }
            });
        }
    }
}

- (void)setFocusImageViewAlpha:(BOOL)isTouchEnd {
    if (self.focusImageView.alpha == 0.f && !isTouchEnd) {
        self.focusImageView.alpha = 1.f;
    }
    
    if (self.focusImageView.alpha != 0.f) {
        double delayInSeconds = 3.88;
        dispatch_time_t popTime = dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayInSeconds * NSEC_PER_SEC));
        dispatch_after(popTime, dispatch_get_main_queue(), ^(void){
            if (self.focusImageView.alpha != 0.f) {
                [UIView animateWithDuration:0.3f animations:^{
                    self.focusImageView.alpha = 0.f;
                }];
            }
        });
    }
}

- (void)deviceOrientationDidChangeNotification {
    //rotate all buttons on camera view
    if (!_cameraBtnSet || _cameraBtnSet.count <= 0) {
        return;
    }
    [_cameraBtnSet enumerateObjectsUsingBlock:^(id obj, BOOL *stop) {
        UIButton *btn = ([obj isKindOfClass:[UIButton class]] ? (UIButton *) obj : nil);
        if (!btn) {
            *stop = YES;
            return;
        }

        btn.layer.anchorPoint = CGPointMake(0.5, 0.5);
        CGAffineTransform transform = CGAffineTransformMakeRotation(0);
        switch ([UIDevice currentDevice].orientation) {
            case UIDeviceOrientationPortrait://1
            {
                transform = CGAffineTransformMakeRotation(0);
                break;
            }
            case UIDeviceOrientationPortraitUpsideDown://2
            {
                transform = CGAffineTransformMakeRotation(M_PI);
                break;
            }
            case UIDeviceOrientationLandscapeLeft://3
            {
                transform = CGAffineTransformMakeRotation(M_PI_2);
                break;
            }
            case UIDeviceOrientationLandscapeRight://4
            {
                transform = CGAffineTransformMakeRotation(-M_PI_2);
                break;
            }
            default:
                break;
        }
        [UIView animateWithDuration:0.3f animations:^{
            btn.transform = transform;
            self.thumbnailView.transform = transform;
        }];
    }];
}

- (void)didCapturePhoto: (UIImage *)stillImage {
#ifndef ORIG_CAMERA_PART
        stillImage = [self addTextsIfNeeded: stillImage];
#endif
     //   dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
//#ifndef ORIG_CAMERA_PART
       /*     if ([CommonHelper IFGetPref:bSaveCamera] != nil && [[CommonHelper IFGetPref:bSaveCamera] boolValue]){
                [self.library saveImage:stillImage   toAlbum:@"SnapInspect" withCompletionBlock:^(NSError                 *error) {
                    if (error!=nil) {
                        DLog(@"Big error: %@", [error description]);
                    }
                }];
            }*/
//#ifdef IF_SAVE_CAMERA_ROLL
       //     [self saveImageToPhotoAlbum:stillImage];
//#endif

        if (_iSaveOption == CameraSaveOptionPhoto){
            NSString *sPath = [[if_AppDelegate GetGlobalVar] valueForKeyPath:@"sFilePath"];
            NSString *sUniqueFileName = [CommonHelper GetUniqueFileName];
            NSString *sPhotoName = [NSString stringWithFormat:@"p_%@.jpg", sUniqueFileName];
            NSString *sThumbName = [NSString stringWithFormat:@"pt_%@.jpg", sUniqueFileName];
            NSString *sPhotoPath = [sPath stringByAppendingPathComponent: sPhotoName];
            NSString *sThumbPath = [sPath stringByAppendingPathComponent: sThumbName];
            [UIImageJPEGRepresentation(stillImage, 0.8)  writeToFile:sPhotoPath atomically:YES];
            [UIImage writeGpsDataFrom:LocationManager.shared.currentLocation.coordinate
                                   to:sPhotoPath completion:nil];

            UIImage *oTempImage = [CommonMedia processImage_Thumb:stillImage];
            [UIImageJPEGRepresentation(oTempImage, 0.8)  writeToFile:sThumbPath atomically:YES];
            [UIImage writeGpsDataFrom:LocationManager.shared.currentLocation.coordinate
                                   to:sThumbPath completion:nil];

            O_Photo *oPhoto = [[O_Photo alloc] init];
            oPhoto.iInsItemID = (int)self.iInsItemID;
            oPhoto.iInsID =  (int)self.iInsID;
            oPhoto.iSize = 0;
            oPhoto.sFile = sPhotoName;
            oPhoto.sThumb = sThumbName;
            oPhoto.iSPhotoID = 0;
            oPhoto.iPhotoID = 0;
            oPhoto.sField1 = [CommonJson addGpsDataToJson:oPhoto.sField1];
            oPhoto.sField2 = @"";
            oPhoto.sField3 = @"";

            int iPhotoID = [db_Media UpdatePhoto:oPhoto];
            
            [self.delegate PostProcessPhotoID:iPhotoID iInsItemID:(int)self.iInsItemID iParamID:(int)self.iParamID];
//#endif
            
//        });
            
            self.thumbnailView.image = stillImage;
            self.thumbnailView.tag = iPhotoID;
            [self.shutterBtn setSelected:NO];
            self.shutterBtn.userInteractionEnabled = YES;
            
            [self refreshInsItemPhotoCountBadge];
        }
        else if (_iSaveOption == CameraSaveOptionFile){
            NSString *sPath = [[if_AppDelegate GetGlobalVar] valueForKeyPath:@"sFilePath"];
            NSString *sUniqueFileName = [CommonHelper GetUniqueFileName];
            NSString *sPhotoName = [NSString stringWithFormat:@"f_%@.jpg", sUniqueFileName];
            //NSString *sThumbName = [NSString stringWithFormat:@"pt_%@.jpg", sUniqueFileName];
            NSString *sPhotoPath = [sPath stringByAppendingPathComponent: sPhotoName];
            //NSString *sThumbPath = [sPath stringByAppendingPathComponent: sThumbName];
            [UIImageJPEGRepresentation(stillImage, 0.8)  writeToFile:sPhotoPath atomically:YES];
            //UIImage *oTempImage = [self processImage_Thumb:stillImage];
            
            //[UIImageJPEGRepresentation(oTempImage, 0.8)  writeToFile:sThumbPath atomically:YES];
            /*O_Photo *oPhoto = [[O_Photo alloc] init];
            oPhoto.iInsItemID = iInsItemID;
            oPhoto.iInsID =  iInsID;
            oPhoto.iSize = 0;
            oPhoto.sFile = sPhotoName;
            oPhoto.sThumb = sThumbName;
            oPhoto.iSPhotoID = 0;
            oPhoto.iPhotoID = 0;
            int iPhotoID = [db_Media UpdatePhoto:oPhoto];
            
            O_File */
            O_File *oFile = [[O_File alloc] init];
            oFile.iSObjectID = (int)_iSObjectID;
            oFile.iSFileID = 0;
            oFile.iFileID = 0;
            oFile.sFile = sPhotoName;
            oFile.sLat = @"";
            oFile.sLong = @"";
            oFile.sComments = @"";
            oFile.bUploaded = 0;
            oFile.bDeleted = 0;
            oFile.dtDateTime = [CommonHelper GetDateString:[NSDate new]];
            oFile.iSize = 0;
            oFile.sCustom1 = @"";
            oFile.sCustom2 = @"";
            int iFileID = [db_Media UpdateFile:oFile];
            
            [self.delegate PostProcessFileID:iFileID iSObjectID:(int)_iSObjectID];
            //#endif
            
            //        });
            
            self.thumbnailView.image = stillImage;
            self.thumbnailView.tag = iFileID;
            [self.shutterBtn setSelected:NO];
            self.shutterBtn.userInteractionEnabled = YES;
        }
    
        if (_iOption == CameraOptionSingle) [self dismissCamera];
}

#pragma mark -------------pinch camera---------------
- (void)handlePinch:(UIPinchGestureRecognizer*)gesture {
    // Check if gesture is starting
    if (gesture.state == UIGestureRecognizerStateBegan) {
        self.doubleTouch = YES;
        [self setSlider:self.scSlider alpha:NO];
        // Hide exposure slider and focus image view
        [self updateExposureSliderVisible:NO];
        self.focusImageView.alpha = 0.f;
    }
    
    [self.captureManager pinchCameraView:gesture];
    
    if (self.scSlider) {
        if (self.scSlider.alpha != 1.f) {
            [UIView animateWithDuration:0.3f animations:^{
                self.scSlider.alpha = 1.f;
            }];
        }
        [self.scSlider setValue:self.captureManager.scaleNum shouldCallBack:NO];
        
        // Handle gesture ending
        if (gesture.state == UIGestureRecognizerStateEnded || 
            gesture.state == UIGestureRecognizerStateCancelled) {
            self.doubleTouch = NO;
            [self setSlider:self.scSlider alpha:YES];
        }
        
        [self updateZoomLevel];

        // Save the zoom level to user defaults
        [CommonHelper IFSavePref:Constants.kCameraZoomLevel sValue:@(self.captureManager.scaleNum).stringValue];
    }
}

- (void)updateExposureSliderVisible:(BOOL)visible {
    self.exposureSlider.alpha = visible ? 1.f : 0.f;
}


@end
