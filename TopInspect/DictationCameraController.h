//
//  DictationCameraController.h
//  SnapInspectCamera
//
//  Created by <PERSON> on 3/4/16.
//  Copyright © 2016 Osama Petran. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "SCCaptureSessionManager.h"
#import <Photos/PHPhotoLibrary.h>
#import "ALAssetsLibrary+CustomPhotoAlbum.h"
#import "SCCaptureCameraController.h"

@protocol SCCaptureCameraControllerDelegate;
@interface DictationCameraController : UIViewController

@property (weak, nonatomic) id<SCCaptureCameraControllerDelegate> delegate;

@property (nonatomic, assign) NSInteger iParamID;
@property (nonatomic, assign) NSInteger iPInsItemID;
@property (nonatomic, assign) NSInteger iInsItemID;
@property (nonatomic, assign) NSInteger iInsID;
@property (nonatomic, assign) NSInteger iSObjectID;

@property (nonatomic, assign) CameraOption iOption;
@property (nonatomic, assign) CameraSaveOption iSaveOption;
@property (nonatomic, assign) BOOL isStatusBarHiddenBeforeShowCamera;

// Photo size for the photo taken, might be set from InsType.sCustom1
@property (nonatomic, assign) NSInteger iPhotoSize;

// The root stack view for the camera controller
@property (nonatomic, weak) UIStackView *rootStackView;

// The preview view for camera display
@property (nonatomic, weak, readonly) UIView *previewView;

// Update the views in the stack view
- (void)updateViews;

@end
