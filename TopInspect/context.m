//
//  context.m
//  SnapInspect3
//
//  Created by <PERSON> on 26/02/18.
//  Copyright © 2018 SnapInspect. All rights reserved.
//

#import "context.h"



@implementation context

@synthesize queue = _queue;

+ (context *)sharedInstance {
    static dispatch_once_t onceToken;
    static context *instance = nil;
    dispatch_once(&onceToken, ^{
        instance = [[context alloc] init];
    });
    return instance;
}

- (id)init {
    self = [super init];
    if (self) {
        _queue = [FMDatabaseQueue databaseQueueWithPath:[CommonHelper getDatabaseFilePath]];
    
    }
    return self;
}

@end
