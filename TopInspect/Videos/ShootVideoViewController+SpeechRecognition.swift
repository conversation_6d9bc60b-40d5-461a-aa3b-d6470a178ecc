import AppFeatures
import AVFoundation
import Extensions
import UIKit

// MARK: - Speech Recognition Extension

extension ShootVideoViewController {
    // MARK: - Associated Objects Keys

    private enum AssociatedKeys {
        static var liveSpeechRecognitionTask: UInt8 = 0
        static var subtitleOverlay: UInt8 = 1
        static var srtFileWriter: UInt8 = 2
        static var subtitleButton: UInt8 = 3
    }

    // MARK: - Computed Properties

    private var liveSpeechRecognitionTask: Task<Void, Never>? {
        get { getAssociated(&AssociatedKeys.liveSpeechRecognitionTask) }
        set { setAssociated(newValue, for: &AssociatedKeys.liveSpeechRecognitionTask) }
    }

    // Private storage for subtitle overlay
    private var _subtitleOverlay: SubtitleOverlayView? {
        get { getAssociated(&AssociatedKeys.subtitleOverlay) }
        set { setAssociated(newValue, for: &AssociatedKeys.subtitleOverlay) }
    }

    @objc var subtitleOverlay: SubtitleOverlayView? {
        if let overlay = _subtitleOverlay {
            return overlay
        }

        guard let viewContainer = value(forKey: "viewContainer") as? UIView else {
            return nil
        }

        let overlay = SubtitleOverlayView()
        viewContainer.addSubview(overlay)

        // Use frame-based positioning for manual control during rotation
        overlay.translatesAutoresizingMaskIntoConstraints = true

        // Set initial frame (will be updated by orientation handler)
        let screenBounds = UIScreen.main.bounds
        let baseWidth = min(screenBounds.width, screenBounds.height)
        let overlayWidth = min(baseWidth * 1.5, screenBounds.width - 40)
        let initialHeight: CGFloat = 50 // Initial height, will adjust dynamically
        overlay.frame = CGRect(
            x: (screenBounds.width - overlayWidth) / 2,
            y: screenBounds.height - 120,
            width: overlayWidth,
            height: initialHeight
        )

        // Don't add to rotation set - we'll handle rotation and positioning manually
        // rotationViewSet.add(overlay)

        // Set anchor point for proper rotation
        overlay.layer.anchorPoint = CGPoint(x: 0.5, y: 0.5)

        // Ensure overlay is visible above other views
        viewContainer.bringSubviewToFront(overlay)

        // Set initial position based on current orientation
        DispatchQueue.main.async {
            self.updateSubtitleOverlayPositionAndRotation()
        }

        _subtitleOverlay = overlay
        return overlay
    }

    @objc var srtFileWriter: SRTFileWriter {
        return getOrSetAssociated(&AssociatedKeys.srtFileWriter) {
            SRTFileWriter()
        }
    }

    private var subtitleButton: UIButton? {
        get { getAssociated(&AssociatedKeys.subtitleButton) }
        set { setAssociated(newValue, for: &AssociatedKeys.subtitleButton) }
    }

    // MARK: - Speech Recognition Control Methods

    @objc func startSpeechRecognition() {
        // Cancel any existing recognition task
        liveSpeechRecognitionTask?.cancel()

        liveSpeechRecognitionTask = Task { @MainActor in
            let speechService = LiveSpeechRecognitionService.shared

            do {
                // Request authorization first
                let authStatus = await speechService.requestAuthorization()
                guard authStatus == .authorized else {
                    print("Speech recognition not authorized: \(authStatus)")
                    return
                }

                // Check availability
                guard speechService.isAvailable() else {
                    print("Speech recognition not available")
                    return
                }

                self.subtitleOverlay?.isVisible = true

                // Start continuous recognition
                let recognitionStream = try await speechService.startContinuousRecognition(nil)

                CommonAnalytics.trackEvent("Speech Recognition Started", meta: [
                    "InsID": self.iInsID,
                    "ItemID": self.iInsItemID,
                ])

                // Process recognition updates
                for await update in recognitionStream {
                    await self.handleSpeechUpdate(update)
                }

            } catch {
                print("Failed to start speech recognition: \(error)")
                CommonAnalytics.trackEvent("Speech Recognition Start Failed", meta: [
                    "error": error.localizedDescription,
                    "InsID": self.iInsID,
                    "ItemID": self.iInsItemID,
                ])
            }
        }
    }

    @objc func stopSpeechRecognition() {
        // Cancel the recognition task
        liveSpeechRecognitionTask?.cancel()
        liveSpeechRecognitionTask = nil

        Task { @MainActor in
            let speechService = LiveSpeechRecognitionService.shared
            await speechService.stopContinuousRecognition()
        }

        // Keep subtitles visible for a moment before hiding
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
            self?.subtitleOverlay?.clearSubtitle()
        }

        CommonAnalytics.trackEvent("Speech Recognition Stopped", meta: [
            "InsID": iInsID,
            "ItemID": iInsItemID,
        ])
    }

    @MainActor
    private func handleSpeechUpdate(_ update: LiveSpeechUpdate) async {
        switch update {
        case .partialText(let text, let timestamp):
            subtitleOverlay?.updateSubtitle(text)

        case .finalText(let text, let timestamp):
            print("Final speech result at \(String(format: "%.2f", timestamp)): \(text)")

        case .error(let errorMessage):
            print("Speech recognition error: \(errorMessage)")
            // Don't stop on errors - let the service handle recovery

        case .recognitionRestarted:
            print("Speech recognition restarted for continuous listening")

        case .availabilityChanged(let isAvailable):
            print("Speech recognition availability changed: \(isAvailable)")
            if !isAvailable {
                subtitleOverlay?.updateSubtitle("Speech recognition temporarily unavailable")
            }
        }
    }

    @objc func saveSRTFile(for video: O_Video) {
        guard let videoFile = video.sFile, !videoFile.isEmpty else {
            return
        }

        Task { @MainActor in
            let speechService = LiveSpeechRecognitionService.shared

            let srtContent = await speechService.generateSRTContent()
            guard !srtContent.isEmpty else {
                print("No transcription content to save")
                return
            }

            let srtFilePath = srtFileWriter.generateSRTFilePath(for: videoFile)

            do {
                try srtFileWriter.writeSRTFile(content: srtContent, to: srtFilePath)

                print("Successfully saved SRT file at: \(srtFilePath)")

                // Update video with SRT file reference using sField3 for SRT metadata
                let existingSRTData = video.sField3 ?? ""
                let srtFileName = (srtFilePath as NSString).lastPathComponent
                let srtFileReference = existingSRTData.isEmpty ? "SRT: \(srtFileName)" : "\(existingSRTData)\nSRT: \(srtFileName)"
                video.sField3 = srtFileReference
                db_Media.update(video)

                CommonAnalytics.trackEvent("SRT File Saved", meta: [
                    "videoID": video.iVideoID,
                    "contentLength": srtContent.count,
                    "filePath": srtFilePath,
                ])
            } catch {
                print("Failed to write SRT file: \(error.localizedDescription)")
                CommonAnalytics.trackEvent("SRT File Write Failed", meta: [
                    "error": error.localizedDescription,
                    "videoID": video.iVideoID,
                ])
            }
        }
    }

    // MARK: - UI Control Methods

    @objc func toggleSubtitleVisibility() {
        guard let overlay = subtitleOverlay else { return }
        let newVisibility = !overlay.isVisible
        overlay.isVisible = newVisibility

        CommonAnalytics.trackEvent("Subtitle Visibility Toggled", meta: [
            "visible": newVisibility,
        ])
    }

    @objc func isSubtitleVisible() -> Bool {
        return subtitleOverlay?.isVisible ?? false
    }

    // MARK: - UI Setup

    @objc func addSubtitleToggleButton() {
        guard let viewContainer = value(forKey: "viewContainer") as? UIView,
              let rotationViewSet = value(forKey: "rotationViewSet") as? NSMutableSet
        else {
            print("Could not find viewContainer or rotationViewSet")
            return
        }

        // Create subtitle toggle button
        let button = UIButton(type: .system)
        button.frame = CGRect(x: 10, y: 60, width: 44, height: 44) // Initial position
        button.setImage(UIImage(systemName: "captions.bubble"), for: .normal)

        button.isSelected = true // Default to showing subtitles

        // Set colors based on selected state
        button.tintColor = .white
        button.backgroundColor = .clear
        button.addTarget(self, action: #selector(subtitleButtonTapped(_:)), for: .touchUpInside)

        // Set anchor point for proper rotation (like other UI elements)
        button.layer.anchorPoint = CGPoint(x: 0.5, y: 0.5)

        // Add directly to viewContainer so it floats
        viewContainer.addSubview(button)

        // Add to rotation set so it rotates with other UI elements
        rotationViewSet.add(button)

        // Store reference
        subtitleButton = button

        // Update position immediately if topView exists
        updateSubtitleButtonPosition()
    }

    @objc private func subtitleButtonTapped(_ sender: UIButton) {
        sender.isSelected.toggle()

        toggleSubtitleVisibility()

        CommonAnalytics.trackEvent("Subtitle Toggle Button Tapped", meta: [
            "enabled": sender.isSelected,
        ])
    }

    @objc func updateSubtitleButtonPosition() {
        guard let button = subtitleButton,
              let topView = value(forKey: "topView") as? UIView
        else {
            return
        }

        // Update button position based on topView height
        button.frame = CGRect(x: 10, y: topView.frame.height + 10, width: 44, height: 44)
    }

    @objc func updateSubtitleOverlayPositionAndRotation() {
        guard let overlay = subtitleOverlay,
              let topView = value(forKey: "topView") as? UIView,
              let bottomView = value(forKey: "bottomView") as? UIView
        else {
            return
        }

        // Remember if overlay was visible before rotation
        let wasVisible = overlay.alpha > 0

        // Hide overlay during rotation transition
        overlay.alpha = 0

        let orientation = UIDevice.current.orientation
        let validOrientation: UIDeviceOrientation

        // Use valid orientation or fallback to current interface orientation
        if orientation.isValidInterfaceOrientation {
            validOrientation = orientation
        } else {
            if let windowScene = view.window?.windowScene {
                switch windowScene.interfaceOrientation {
                case .portrait:
                    validOrientation = .portrait
                case .portraitUpsideDown:
                    validOrientation = .portraitUpsideDown
                case .landscapeLeft:
                    validOrientation = .landscapeLeft
                case .landscapeRight:
                    validOrientation = .landscapeRight
                default:
                    validOrientation = .portrait
                }
            } else {
                validOrientation = .portrait
            }
        }

        // assign the valid orientation to the overlay
        overlay.orientation = validOrientation

        let screenBounds = view.bounds
        let safeInsets = view.safeAreaInsets
        let screenWidth = screenBounds.width
        let screenHeight = screenBounds.height

        // Calculate reasonable width (portrait width × 1.5, but not too long)
        let baseWidth = min(screenWidth, screenHeight) // Shorter screen dimension
        let overlayWidth = min(baseWidth * 1.5, screenWidth - 40) // Cap at screen width - margins
        let overlayHeight: CGFloat = max(overlay.totalHeight, 50.0)

        var transform = CGAffineTransform.identity
        var newFrame = CGRect.zero

        switch validOrientation {
        case .portrait: // Bottom of screen
            transform = CGAffineTransform.identity
            let bottomViewTop = bottomView.frame.minY
            newFrame = CGRect(
                x: (screenWidth - overlayWidth) / 2, // Center horizontally
                y: bottomViewTop - overlayHeight - 20,
                width: overlayWidth,
                height: overlayHeight
            )

        case .portraitUpsideDown: // Bottom of screen when upside down (appears at top visually)
            transform = CGAffineTransform(rotationAngle: .pi)
            let topOffset = topView.frame.maxY
            newFrame = CGRect(
                x: (screenWidth - overlayWidth) / 2, // Center horizontally
                y: topOffset + 20,
                width: overlayWidth,
                height: overlayHeight
            )

        case .landscapeLeft: // Bottom edge when rotated left (right side of screen)
            transform = CGAffineTransform(rotationAngle: .pi / 2)
            newFrame = CGRect(
                x: safeInsets.left + 20,
                y: (screenHeight - overlayWidth) / 2, // Center vertically
                width: overlayHeight,
                height: overlayWidth
            )

        case .landscapeRight: // Bottom edge when rotated right (left side of screen)
            transform = CGAffineTransform(rotationAngle: -.pi / 2)
            newFrame = CGRect(
                x: screenWidth - safeInsets.right - overlayHeight - 20,
                y: (screenHeight - overlayWidth) / 2, // Center vertically
                width: overlayHeight,
                height: overlayWidth
            )

        default:
            // Default to portrait
            transform = CGAffineTransform.identity
            let bottomOffset = bottomView.frame.minY
            newFrame = CGRect(
                x: (screenWidth - overlayWidth) / 2,
                y: bottomOffset - overlayHeight - 20,
                width: overlayWidth,
                height: overlayHeight
            )
        }

        // Animate the position and rotation change, then show overlay
        UIView.animate(withDuration: 0.3, animations: {
            overlay.transform = transform
            overlay.frame = newFrame
        }) { _ in
            // Show overlay again after layout change is complete (if it was visible before)
            if wasVisible, overlay.isVisible {
                UIView.animate(withDuration: 0.2) {
                    overlay.alpha = 1.0
                }
            }
        }
    }
}
