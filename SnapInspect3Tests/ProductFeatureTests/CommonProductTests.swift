//
//  CommonProductTests.swift
//  TopInspectTests
//
//  Created by <PERSON> on 2024/7/10.
//  Copyright © 2025 SnapInspect. All rights reserved.
//

import XCTest
@testable import SnapInspect3

final class CommonProductTests: XCTestCase {
    override func setUp() {
        CommonHelper.ifRemovePref(Constants.kJsonKeyProduct)
    }
    
    func test_EnableProduct_ReturnsTrue() {
        CommonHelper.ifSavePref(Constants.kJsonKeyProduct, sValue: "1")
        XCTAssertTrue(CommonProduct.hasEnabledProduct)
    }
    
    func test_DisableProduct_ReturnsFalse() {
        XCTAssertFalse(CommonProduct.hasEnabledProduct)
        
        CommonHelper.ifSavePref(Constants.kJsonKeyProduct, sValue: "0")
        XCTAssertFalse(CommonProduct.hasEnabledProduct)
        
        CommonHelper.ifSavePref(Constants.kJsonKeyProduct, sValue: "ppososo")
        XCTAssertFalse(CommonProduct.hasEnabledProduct)
        
        CommonHelper.ifSavePref(Constants.kJsonKeyProduct, sValue: "")
        XCTAssertFalse(CommonProduct.hasEnabledProduct)
    }
}
