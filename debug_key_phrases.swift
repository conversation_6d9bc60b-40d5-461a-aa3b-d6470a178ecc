#!/usr/bin/env swift

import Foundation
import NaturalLanguage

// Simulate the extract<PERSON>eyPhrases logic to understand the issue
func extractKeyPhrases(_ text: String) -> [String] {
    var keyPhrases: [String] = []
    
    let tagger = NLTagger(tagSchemes: [.lexicalClass])
    tagger.string = text.lowercased()
    
    let options: NLTagger.Options = [.omitPunctuation, .omitWhitespace]
    let tags: [NLTag] = [.noun, .verb, .adjective]
    
    var foundAnyTags = false
    tagger.enumerateTags(in: text.startIndex ..< text.endIndex, unit: .word, scheme: .lexicalClass, options: options) { tag, tokenRange in
        let word = String(text[tokenRange])
        if let tag = tag, tags.contains(tag) {
            keyPhrases.append(word.lowercased())
            foundAnyTags = true
        }
        return true
    }
    
    // Fallback if NLTagger didn't find anything
    if !foundAnyTags {
        let stopWords = Set(["the", "is", "are", "was", "were", "been", "be", "have", "has", "had",
                             "do", "does", "did", "will", "would", "could", "should", "may", "might",
                             "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with",
                             "by", "from", "up", "about", "into", "through", "during", "before", "after",
                             "it", "its", "this", "that", "these", "those", "them", "they", "their"])
        
        let words = text.lowercased().split(separator: " ").map(String.init)
        keyPhrases = words.filter { word in
            !stopWords.contains(word) && !word.isEmpty && word.count > 2
        }
    }
    
    return Array(Set(keyPhrases))
}

let testCases = [
    ("physical damage", "Is there any physical property damage?"),
    ("mold exists", "No visible mold or biological growth exists?"),
    ("vandalism graffiti", "No vandalism or significant graffiti?"),
    ("signage secure", "Signage is secure, with no damage?"),
    ("signage damage", "Signage is secure, with no damage?")
]

print("Testing key phrase extraction...")

for (spoken, item) in testCases {
    print("\n=== Testing: '\(spoken)' -> '\(item)' ===")
    
    let spokenPhrases = extractKeyPhrases(spoken)
    let itemPhrases = extractKeyPhrases(item)
    
    print("Spoken phrases: \(spokenPhrases)")
    print("Item phrases: \(itemPhrases)")
    
    let intersection = Set(spokenPhrases).intersection(Set(itemPhrases))
    print("Common phrases: \(intersection)")
    print("Match ratio: \(Double(intersection.count) / Double(spokenPhrases.count))")
}
