#!/usr/bin/env swift

import Foundation

// Simple test to understand the issue
let testCases = [
    ("physical damage", "Is there any physical property damage?"),
    ("mold exists", "No visible mold or biological growth exists?"),
    ("vandalism graffiti", "No vandalism or significant graffiti?"),
    ("significant graffiti", "No vandalism or significant graffiti?"),
    ("signage secure", "Signage is secure, with no damage?"),
    ("signage damage", "Signage is secure, with no damage?")
]

print("Testing phrase matching issues...")

for (spoken, item) in testCases {
    print("\nTesting: '\(spoken)' -> '\(item)'")
    
    // Extract key words manually to understand the issue
    let spokenWords = spoken.lowercased().split(separator: " ").map(String.init)
    let itemWords = item.lowercased().split(separator: " ").map(String.init)
    
    print("Spoken words: \(spokenWords)")
    print("Item words: \(itemWords)")
    
    // Check for word overlap
    let overlap = spokenWords.filter { spokenWord in
        itemWords.contains { itemWord in
            itemWord.contains(spokenWord) || spokenWord.contains(itemWord)
        }
    }
    
    print("Word overlap: \(overlap)")
    print("Overlap ratio: \(Double(overlap.count) / Double(spokenWords.count))")
}
