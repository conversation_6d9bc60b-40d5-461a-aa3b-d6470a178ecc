@testable import AppFeatures
import XCTest

final class SpeechNormalizerFindBestMatchTests: XCTestCase {
    // MARK: - Private Method Tests (findBestMatchWithPhrases)
    
    func testFindBestMatchWithPhrases_BasicMatching() {
        let items = ["Smoke Detector", "Fire Alarm", "Emergency Exit"]
        
        // Test exact match
        if let match = SpeechNormalizer.findBestMatchWithPhrases(
            spokenText: "smoke detector",
            in: items
        ) {
            XCTAssertEqual(match.index, 0)
            XCTAssertGreaterThan(match.score, 1.0, "Should be greater than 1.0")
        } else {
            XCTFail("Should find exact match")
        }
        
        // Test phrase-based matching with extra words
        if let match = SpeechNormalizer.findBestMatchWithPhrases(
            spokenText: "the smoke detector is working properly",
            in: items
        ) {
            XCTAssertEqual(match.index, 0)
            XCTAssertGreaterThan(match.score, 0.8)
        } else {
            XCTFail("Should find match with extra context")
        }
        
        // Test no match scenario
        let noMatch = SpeechNormalizer.findBestMatchWithPhrases(
            spokenText: "water fountain",
            in: items,
            threshold: 0.7
        )
        XCTAssertNil(noMatch)
    }
    
    func testFindBestMatchWithPhrases_KeyPhraseExtraction() {
        let items = [
            "HVAC System",
            "HVAC System - Mechanical Room",
            "Fire Suppression System"
        ]
        
        // Should prefer more specific match when key phrases align
        if let match = SpeechNormalizer.findBestMatchWithPhrases(
            spokenText: "hvac system in the mechanical room needs service",
            in: items
        ) {
            XCTAssertEqual(match.index, 1, "Should match more specific 'HVAC System - Mechanical Room'")
            XCTAssertGreaterThan(match.score, 0.8)
        } else {
            XCTFail("Should find match")
        }
        
        // Test with normalized speech variations
        if let match = SpeechNormalizer.findBestMatchWithPhrases(
            spokenText: "h vac systems mechanical rooms",
            in: items
        ) {
            XCTAssertEqual(match.index, 1)
        } else {
            XCTFail("Should handle speech variations")
        }
    }
    
    func testFindBestMatchWithPhrases_PhoneticMatching() {
        let items = ["Fire Extinguisher", "Smoke Detector", "Emergency Light"]
        
        // Test phonetic similarity
        if let match = SpeechNormalizer.findBestMatchWithPhrases(
            spokenText: "fyre extinguisher", // phonetic misspelling
            in: items
        ) {
            XCTAssertEqual(match.index, 0)
            XCTAssertGreaterThan(match.score, 0.7)
        } else {
            XCTFail("Should match phonetically similar words")
        }
    }
    
    func testFindBestMatchWithPhrases_ComplexScoring() {
        let items = [
            "Exit",
            "Emergency Exit",
            "Emergency Exit Sign",
            "Emergency Exit Sign - Illuminated"
        ]
        
        // Test specificity bonus
        if let match = SpeechNormalizer.findBestMatchWithPhrases(
            spokenText: "emergency exit sign illuminated and functioning",
            in: items
        ) {
            XCTAssertEqual(match.index, 3, "Should prefer most specific match")
        } else {
            XCTFail("Should find specific match")
        }
        
        // Test with fewer matching phrases
        if let match = SpeechNormalizer.findBestMatchWithPhrases(
            spokenText: "exit sign",
            in: items
        ) {
            XCTAssertEqual(match.index, 2, "Should match 'Emergency Exit Sign'")
        } else {
            XCTFail("Should find partial match")
        }
    }
    
    func testFindBestMatchWithPhrases_OrdinalNumberMatching() {
        // MARK: - Test Data Structure
        struct OrdinalTestCase {
            let spokenText: String
            let expectedIndex: Int
            let expectedItem: String
            let description: String
            let minScore: Double
        }
        
        // MARK: - Bedroom Tests (Original)
        let bedroomItems = [
            "Bedroom",        // index 0 - base bedroom
            "Bedroom 2",      // index 1
            "Bedroom 3",      // index 2
            "Bedroom 4",      // index 3
            "Master Bedroom", // index 4
            "Guest Bedroom"   // index 5
        ]
        
        let bedroomTests: [OrdinalTestCase] = [
            // Ordinal words (first, second, third, etc.)
            OrdinalTestCase(
                spokenText: "now I am in the second bedroom",
                expectedIndex: 1,
                expectedItem: "Bedroom 2",
                
                description: "Ordinal 'second' → 'Bedroom 2'",
                minScore: 0.7
            ),
            OrdinalTestCase(
                spokenText: "third bedroom needs inspection",
                expectedIndex: 2,
                expectedItem: "Bedroom 3",
                
                description: "Ordinal 'third' → 'Bedroom 3'",
                minScore: 0.7
            ),
            OrdinalTestCase(
                spokenText: "checking the fourth bedroom",
                expectedIndex: 3,
                expectedItem: "Bedroom 4",
                
                description: "Ordinal 'fourth' → 'Bedroom 4'",
                minScore: 0.7
            ),
            OrdinalTestCase(
                spokenText: "first bedroom is complete",
                expectedIndex: 0,
                expectedItem: "Bedroom",
                
                description: "Ordinal 'first' → base 'Bedroom'",
                minScore: 0.7
            ),
            
            // Numeric ordinals (1st, 2nd, 3rd, etc.)
            OrdinalTestCase(
                spokenText: "2nd bedroom has issues",
                expectedIndex: 1,
                expectedItem: "Bedroom 2",
                
                description: "Numeric ordinal '2nd' → 'Bedroom 2'",
                minScore: 0.7
            ),
            OrdinalTestCase(
                spokenText: "3rd bedroom window",
                expectedIndex: 2,
                expectedItem: "Bedroom 3",
                
                description: "Numeric ordinal '3rd' → 'Bedroom 3'",
                minScore: 0.7
            ),
            
            // Written numbers (one, two, three, etc.)
            OrdinalTestCase(
                spokenText: "bedroom one is ready",
                expectedIndex: 0,
                expectedItem: "Bedroom",
                
                description: "Written 'one' → base 'Bedroom'",
                minScore: 0.7
            ),
            OrdinalTestCase(
                spokenText: "bedroom two ceiling",
                expectedIndex: 1,
                expectedItem: "Bedroom 2",
                
                description: "Written 'two' → 'Bedroom 2'",
                minScore: 0.7
            ),
            OrdinalTestCase(
                spokenText: "bedroom three floor",
                expectedIndex: 2,
                expectedItem: "Bedroom 3",
                
                description: "Written 'three' → 'Bedroom 3'",
                minScore: 0.7
            ),
            OrdinalTestCase(
                spokenText: "bedroom four wall",
                expectedIndex: 3,
                expectedItem: "Bedroom 4",
                
                description: "Written 'four' → 'Bedroom 4'",
                minScore: 0.7
            ),
            
            // Direct number matching
            OrdinalTestCase(
                spokenText: "bedroom 2 window",
                expectedIndex: 1,
                expectedItem: "Bedroom 2",
                
                description: "Direct number '2' → 'Bedroom 2'",
                minScore: 0.8
            ),
            OrdinalTestCase(
                spokenText: "bedroom 3 door",
                expectedIndex: 2,
                expectedItem: "Bedroom 3",
                
                description: "Direct number '3' → 'Bedroom 3'",
                minScore: 0.8
            ),
            OrdinalTestCase(
                spokenText: "bedroom 4 needs cleaning",
                expectedIndex: 3,
                expectedItem: "Bedroom 4",
                
                description: "Direct number '4' → 'Bedroom 4'",
                minScore: 0.8
            ),
            
            // Special cases
            OrdinalTestCase(
                spokenText: "master bedroom ceiling",
                expectedIndex: 4,
                expectedItem: "Master Bedroom",
                
                description: "Specific match 'master' → 'Master Bedroom'",
                minScore: 0.8
            ),
            OrdinalTestCase(
                spokenText: "guest bedroom door",
                expectedIndex: 5,
                expectedItem: "Guest Bedroom",
                
                description: "Specific match 'guest' → 'Guest Bedroom'",
                minScore: 0.8
            ),
            OrdinalTestCase(
                spokenText: "bedroom inspection complete",
                expectedIndex: 0,
                expectedItem: "Bedroom",
                
                description: "Generic 'bedroom' → base 'Bedroom'",
                minScore: 0.8
            )
        ]
        
        // Run bedroom tests
        for testCase in bedroomTests {
            if let match = SpeechNormalizer.findBestMatchWithPhrases(
                spokenText: testCase.spokenText,
                in: bedroomItems
            ) {
                XCTAssertEqual(match.index, testCase.expectedIndex, 
                             "\(testCase.description) - Expected index \(testCase.expectedIndex), got \(match.index)")
                XCTAssertGreaterThan(match.score, testCase.minScore, 
                                   "\(testCase.description) - Score \(match.score) should be > \(testCase.minScore)")
            } else {
                XCTFail("\(testCase.description) - No match found for '\(testCase.spokenText)'")
            }
        }
        
        // MARK: - Generic Item Type Tests (Floors, Rooms, Units, etc.)
        
        // Test floors
        let floorItems = ["Floor", "Floor 2", "Floor 3", "Floor 4", "Mezzanine Floor"]
        let floorTests: [OrdinalTestCase] = [
            OrdinalTestCase(spokenText: "going to the third floor", expectedIndex: 2, expectedItem: "Floor 3", 
                          description: "Floor: 'third floor' → 'Floor 3'", minScore: 0.7),
            OrdinalTestCase(spokenText: "floor one lobby", expectedIndex: 0, expectedItem: "Floor", 
                          description: "Floor: 'floor one' → base 'Floor'", minScore: 0.7),
            OrdinalTestCase(spokenText: "4th floor elevator", expectedIndex: 3, expectedItem: "Floor 4", 
                          description: "Floor: '4th floor' → 'Floor 4'", minScore: 0.7)
        ]
        
        for testCase in floorTests {
            if let match = SpeechNormalizer.findBestMatchWithPhrases(
                spokenText: testCase.spokenText,
                in: floorItems
            ) {
                XCTAssertEqual(match.index, testCase.expectedIndex, 
                             "\(testCase.description) - Expected index \(testCase.expectedIndex), got \(match.index)")
                XCTAssertGreaterThan(match.score, testCase.minScore)
            } else {
                XCTFail("\(testCase.description) - No match found")
            }
        }
        
        // Test rooms
        let roomItems = ["Room", "Room 2", "Room 3", "Conference Room", "Storage Room"]
        let roomTests: [OrdinalTestCase] = [
            OrdinalTestCase(spokenText: "second room on the left", expectedIndex: 1, expectedItem: "Room 2", 
                          description: "Room: 'second room' → 'Room 2'", minScore: 0.7),
            OrdinalTestCase(spokenText: "room three needs cleaning", expectedIndex: 2, expectedItem: "Room 3", 
                          description: "Room: 'room three' → 'Room 3'", minScore: 0.7)
        ]
        
        for testCase in roomTests {
            if let match = SpeechNormalizer.findBestMatchWithPhrases(
                spokenText: testCase.spokenText,
                in: roomItems
            ) {
                XCTAssertEqual(match.index, testCase.expectedIndex, 
                             "\(testCase.description)")
                XCTAssertGreaterThan(match.score, testCase.minScore)
            } else {
                XCTFail("\(testCase.description) - No match found")
            }
        }
        
        // Test units (apartment example)
        let unitItems = ["Unit 1", "Unit 2", "Unit 3", "Unit 4", "Penthouse Unit"]
        let unitTests: [OrdinalTestCase] = [
            OrdinalTestCase(spokenText: "third unit has a leak", expectedIndex: 2, expectedItem: "Unit 3", 
                          description: "Unit: 'third unit' → 'Unit 3'", minScore: 0.7),
            OrdinalTestCase(spokenText: "unit four inspection", expectedIndex: 3, expectedItem: "Unit 4", 
                          description: "Unit: 'unit four' → 'Unit 4'", minScore: 0.7)
        ]
        
        for testCase in unitTests {
            if let match = SpeechNormalizer.findBestMatchWithPhrases(
                spokenText: testCase.spokenText,
                in: unitItems
            ) {
                XCTAssertEqual(match.index, testCase.expectedIndex, 
                             "\(testCase.description)")
                XCTAssertGreaterThan(match.score, testCase.minScore)
            } else {
                XCTFail("\(testCase.description) - No match found")
            }
        }
        
        // Test custom/unknown item types
        let customItems = ["Station", "Station 2", "Station 3", "Main Station"]
        let customTests: [OrdinalTestCase] = [
            OrdinalTestCase(spokenText: "second station check complete", expectedIndex: 1, expectedItem: "Station 2", 
                          description: "Custom: 'second station' → 'Station 2'", minScore: 0.7),
            OrdinalTestCase(spokenText: "station one ready", expectedIndex: 0, expectedItem: "Station", 
                          description: "Custom: 'station one' → base 'Station'", minScore: 0.7)
        ]
        
        for testCase in customTests {
            if let match = SpeechNormalizer.findBestMatchWithPhrases(
                spokenText: testCase.spokenText,
                in: customItems
            ) {
                XCTAssertEqual(match.index, testCase.expectedIndex, 
                             "\(testCase.description)")
                XCTAssertGreaterThan(match.score, testCase.minScore)
            } else {
                XCTFail("\(testCase.description) - No match found")
            }
        }
        
        // MARK: - Edge Cases
        
        // Test mixed list with various item types
        let mixedItems = ["Floor 2", "Room 3", "Unit 4", "Level 5", "Section 6"]
        let mixedTests: [OrdinalTestCase] = [
            OrdinalTestCase(spokenText: "going to the second floor", expectedIndex: 0, expectedItem: "Floor 2", 
                          description: "Mixed: 'second floor' → 'Floor 2'", minScore: 0.7),
            OrdinalTestCase(spokenText: "third room inspection", expectedIndex: 1, expectedItem: "Room 3", 
                          description: "Mixed: 'third room' → 'Room 3'", minScore: 0.7),
            OrdinalTestCase(spokenText: "unit four status", expectedIndex: 2, expectedItem: "Unit 4", 
                          description: "Mixed: 'unit four' → 'Unit 4'", minScore: 0.7)
        ]
        
        for testCase in mixedTests {
            if let match = SpeechNormalizer.findBestMatchWithPhrases(
                spokenText: testCase.spokenText,
                in: mixedItems
            ) {
                XCTAssertEqual(match.index, testCase.expectedIndex, 
                             "\(testCase.description)")
                XCTAssertGreaterThan(match.score, testCase.minScore)
            } else {
                XCTFail("\(testCase.description) - No match found")
            }
        }
    }
    
    func testFindBestMatchWithPhrases_GenericOrdinalMatching() {
        // This test demonstrates that ordinal matching works with ANY item type,
        // not just predefined ones like bedroom, floor, room, etc.
        
        struct GenericTestCase {
            let itemType: String
            let items: [String]
            let spokenText: String
            let expectedIndex: Int
            let description: String
        }
        
        let genericTests = [
            // Inspection-specific items
            GenericTestCase(
                itemType: "Checkpoint",
                items: ["Checkpoint", "Checkpoint 2", "Checkpoint 3", "Final Checkpoint"],
                spokenText: "second checkpoint verified",
                expectedIndex: 1,
                description: "Checkpoint inspection"
            ),
            
            // Equipment items
            GenericTestCase(
                itemType: "Generator",
                items: ["Generator", "Generator 2", "Generator 3", "Backup Generator"],
                spokenText: "generator one maintenance complete",
                expectedIndex: 0,
                description: "Generator equipment"
            ),
            
            // Custom inspection areas
            GenericTestCase(
                itemType: "Zone",
                items: ["Zone 1", "Zone 2", "Zone 3", "Zone 4", "Restricted Zone"],
                spokenText: "fourth zone inspection",
                expectedIndex: 3,
                description: "Inspection zones"
            ),
            
            // Made-up item types (to prove it works with anything)
            GenericTestCase(
                itemType: "Widget",
                items: ["Widget", "Widget 2", "Widget 3", "Special Widget"],
                spokenText: "second widget tested",
                expectedIndex: 1,
                description: "Generic widget items"
            ),
            
            // Complex naming patterns
            GenericTestCase(
                itemType: "Bay",
                items: ["Loading Bay", "Loading Bay 2", "Loading Bay 3", "Service Bay"],
                spokenText: "third loading bay clear",
                expectedIndex: 2,
                description: "Loading bays with compound names"
            )
        ]
        
        // Run all generic tests
        for test in genericTests {
            if let match = SpeechNormalizer.findBestMatchWithPhrases(
                spokenText: test.spokenText,
                in: test.items
            ) {
                XCTAssertEqual(match.index, test.expectedIndex,
                             "\(test.description): '\(test.spokenText)' should match index \(test.expectedIndex)")
                XCTAssertGreaterThan(match.score, 0.7,
                                   "\(test.description): Score should be > 0.7")
            } else {
                XCTFail("\(test.description): No match found for '\(test.spokenText)'")
            }
        }
    }
    
    func testFindBestMatchWithPhrases_OrdinalMatchingPatterns() {
        // Test various speech patterns to ensure robustness
        let items = ["Window", "Window 2", "Window 3", "Window 4", "Bay Window"]
        
        struct PatternTest {
            let pattern: String
            let expectedIndex: Int
            let description: String
        }
        
        let patternTests = [
            // Different word orders
            PatternTest(pattern: "the second window from the left", expectedIndex: 1, 
                       description: "Ordinal before item with context"),
            PatternTest(pattern: "window number two is broken", expectedIndex: 1, 
                       description: "Item then 'number' then ordinal"),
            PatternTest(pattern: "I checked window three already", expectedIndex: 2, 
                       description: "Item then written number"),
            
            // Mixed with other numbers
            PatternTest(pattern: "second window has 5 cracks", expectedIndex: 1, 
                       description: "Ordinal with other numbers in context"),
            
            // Conversational patterns
            PatternTest(pattern: "okay so the third window needs replacing", expectedIndex: 2, 
                       description: "Conversational prefix"),
            PatternTest(pattern: "window four looks good to me", expectedIndex: 3, 
                       description: "Written number after item"),
            
            // First/one special cases
            PatternTest(pattern: "starting with window one", expectedIndex: 0, 
                       description: "Window one matches base Window"),
            PatternTest(pattern: "the first window is fine", expectedIndex: 0, 
                       description: "First matches base item")
        ]
        
        for test in patternTests {
            if let match = SpeechNormalizer.findBestMatchWithPhrases(
                spokenText: test.pattern,
                in: items
            ) {
                XCTAssertEqual(match.index, test.expectedIndex,
                             "\(test.description): '\(test.pattern)' should match index \(test.expectedIndex)")
            } else {
                XCTFail("\(test.description): No match found for '\(test.pattern)'")
            }
        }
    }
    
    func testFindBestMatchWithPhrases_OrdinalMatchingPatternsExtended() {
        // Extended test cases for more complex patterns and edge cases
        
        struct ExtendedPatternTest {
            let items: [String]
            let pattern: String
            let expectedIndex: Int
            let description: String
        }
        
        let extendedTests = [
            // Test ambiguous number references
            ExtendedPatternTest(
                items: ["Room", "Room 2", "Room 3", "Room 4"],
                pattern: "between room 2 and 3",
                expectedIndex: 1, // Should match Room 2 (first mentioned)
                description: "Ambiguous range reference"
            ),
            
            // Test technical notation patterns
            ExtendedPatternTest(
                items: ["Unit 1", "Unit 2", "Unit 3"],
                pattern: "unit #2 needs service",
                expectedIndex: 1,
                description: "Hash notation pattern"
            ),
            ExtendedPatternTest(
                items: ["Suite 1", "Suite 2", "Suite 3"],
                pattern: "suite no. 3",
                expectedIndex: 2,
                description: "Number abbreviation pattern"
            ),
            ExtendedPatternTest(
                items: ["Office 1", "Office 2", "Office 3"],
                pattern: "office (2)",
                expectedIndex: 1,
                description: "Parenthetical number pattern"
            ),
            
            // Test speech corrections and hesitations
            ExtendedPatternTest(
                items: ["Floor", "Floor 2", "Floor 3", "Floor 4"],
                pattern: "third, no wait, fourth floor",
                expectedIndex: 3,
                description: "Speech correction pattern"
            ),
            ExtendedPatternTest(
                items: ["Window", "Window 2", "Window 3"],
                pattern: "the the second window",
                expectedIndex: 1,
                description: "Stuttering pattern"
            ),
            
            // Test compound patterns with multiple numbers
            ExtendedPatternTest(
                items: ["Building 1 Floor 2", "Building 2 Floor 3", "Building 3 Floor 1"],
                pattern: "building two floor three",
                expectedIndex: 1,
                description: "Multiple number compound pattern"
            ),
            
            // Test numbers that aren't item references
            ExtendedPatternTest(
                items: ["Meeting Room", "Meeting Room 2", "Meeting Room 3"],
                pattern: "at 3 pm in meeting room 2",
                expectedIndex: 1,
                description: "Time number vs item number"
            ),
            
            // Test very long compound nouns
            ExtendedPatternTest(
                items: ["Emergency Exit Stairwell", "Emergency Exit Stairwell 2", "Emergency Exit Stairwell 3"],
                pattern: "third emergency exit stairwell door is stuck",
                expectedIndex: 2,
                description: "Very long compound noun"
            ),
            
            // Test mixed ordinal formats
            ExtendedPatternTest(
                items: ["Zone A", "Zone B", "Zone 1", "Zone 2", "Zone 3"],
                pattern: "second zone",
                expectedIndex: 3, // Matches Zone 2 (the actual number 2)
                description: "Mixed letter/number zone naming"
            ),
            
            // Test with no clear match
            ExtendedPatternTest(
                items: ["Red Room", "Blue Room", "Green Room"],
                pattern: "second room",
                expectedIndex: 0, // Will match first item when no numbered pattern found
                description: "No numbered items available"
            )
        ]
        
        for test in extendedTests {
            if let match = SpeechNormalizer.findBestMatchWithPhrases(
                spokenText: test.pattern,
                in: test.items
            ) {
                XCTAssertEqual(match.index, test.expectedIndex,
                             "\(test.description): '\(test.pattern)' should match '\(test.items[test.expectedIndex])' at index \(test.expectedIndex), but matched '\(test.items[match.index])' at index \(match.index)")
            } else {
                XCTFail("\(test.description): No match found for '\(test.pattern)'")
            }
        }
        
        // Test edge cases that should NOT match or have specific behavior
        let negativeTests = [
            // Multiple contradictory numbers
            (items: ["Room 1", "Room 2", "Room 3"],
             pattern: "room 2 3 4",
             shouldMatch: true,
             description: "Multiple numbers in sequence"),
            
            // Decimal numbers - may still match on partial text similarity
            (items: ["Version 1", "Version 2", "Version 3"],
             pattern: "version 2.5",
             shouldMatch: true, // Will likely match Version 2 by text similarity
             description: "Decimal number may match similar version"),
            
            // Very large numbers - may still match on text similarity  
            (items: ["Page 1", "Page 2", "Page 3"],
             pattern: "page 999",
             shouldMatch: true, // Will likely match Page 1 by text similarity
             description: "Large number may match by text similarity")
        ]
        
        for test in negativeTests {
            let match = SpeechNormalizer.findBestMatchWithPhrases(
                spokenText: test.pattern,
                in: test.items,
                threshold: 0.7
            )
            
            if test.shouldMatch {
                XCTAssertNotNil(match, "\(test.description): Expected a match for '\(test.pattern)'")
            } else {
                XCTAssertNil(match, "\(test.description): Should not match '\(test.pattern)'")
            }
        }
    }
    
    func testFindBestMatchWithPhrases_ComplexInspectionScenarios() {
        // Test real-world inspection scenarios with complex naming
        
        struct InspectionScenario {
            let items: [String]
            let spokenText: String
            let expectedItem: String
            let description: String
        }
        
        let scenarios = [
            // HVAC system components
            InspectionScenario(
                items: ["HVAC Unit", "HVAC Unit 2", "HVAC Unit 3", "Rooftop HVAC"],
                spokenText: "checking the second hvac unit on the roof",
                expectedItem: "HVAC Unit 2",
                description: "HVAC with location context"
            ),
            
            // Fire safety equipment
            InspectionScenario(
                items: ["Fire Extinguisher Station", "Fire Extinguisher Station 2", "Fire Extinguisher Station 3"],
                spokenText: "third fire extinguisher station is blocked",
                expectedItem: "Fire Extinguisher Station 3",
                description: "Long compound safety equipment name"
            ),
            
            // Electrical panels
            InspectionScenario(
                items: ["Main Electrical Panel", "Sub Panel 1", "Sub Panel 2", "Sub Panel 3"],
                spokenText: "second sub panel has loose connections",
                expectedItem: "Sub Panel 2",
                description: "Technical equipment with sub-naming"
            ),
            
            // Mixed naming conventions
            InspectionScenario(
                items: ["Apartment 1A", "Apartment 1B", "Apartment 2A", "Apartment 2B", "Apartment 3"],
                spokenText: "apartment three needs inspection",
                expectedItem: "Apartment 3",
                description: "Mixed alphanumeric naming"
            ),
            
            // Inspection with observations
            InspectionScenario(
                items: ["Smoke Detector", "Smoke Detector 2", "Smoke Detector 3", "Carbon Monoxide Detector"],
                spokenText: "second smoke detector battery low reading 2 volts",
                expectedItem: "Smoke Detector 2",
                description: "Item reference with measurement numbers"
            )
        ]
        
        for scenario in scenarios {
            if let match = SpeechNormalizer.findBestMatchWithPhrases(
                spokenText: scenario.spokenText,
                in: scenario.items
            ) {
                let matchedItem = scenario.items[match.index]
                XCTAssertEqual(matchedItem, scenario.expectedItem,
                             "\(scenario.description): Expected '\(scenario.expectedItem)', got '\(matchedItem)'")
            } else {
                XCTFail("\(scenario.description): No match found")
            }
        }
    }
    
    func testFindBestMatchWithPhrases_HomophoneConfusion() {
        // Test cases where homophones near numbers might confuse the matching
        let bedroomItems = [
            "Bedroom",        // index 0
            "Bedroom 2",      // index 1
            "Bedroom 3",      // index 2
            "Bedroom 4",      // index 3
        ]
        
        struct HomophoneTest {
            let spokenText: String
            let expectedIndex: Int
            let expectedItem: String
            let description: String
        }
        
        let homophoneTests: [HomophoneTest] = [
            // The specific reported issue
            HomophoneTest(
                spokenText: "I move to bedroom 3. To word confused the model.",
                expectedIndex: 2,
                expectedItem: "Bedroom 3",
                description: "'To' after '3' should not override to match 'Bedroom 2'"
            ),
            
            // Similar homophone confusion cases
            HomophoneTest(
                spokenText: "bedroom 4. For the inspection",
                expectedIndex: 3,
                expectedItem: "Bedroom 4",
                description: "'For' should not override '4' to match 'four'"
            ),
            
            HomophoneTest(
                spokenText: "going to bedroom 2. Won more time",
                expectedIndex: 1,
                expectedItem: "Bedroom 2",
                description: "'Won' should not override '2' to match 'one'"
            ),
            
            HomophoneTest(
                spokenText: "bedroom 3 is done. Too many issues",
                expectedIndex: 2,
                expectedItem: "Bedroom 3",
                description: "'Too' should not override '3' to match 'two'"
            ),
            
            HomophoneTest(
                spokenText: "checked bedroom 4. Ate lunch after",
                expectedIndex: 3,
                expectedItem: "Bedroom 4",
                description: "'Ate' should not override '4' to match 'eight'"
            ),
            
            // Multiple homophones in sequence
            HomophoneTest(
                spokenText: "bedroom 3. To check: won window, too doors",
                expectedIndex: 2,
                expectedItem: "Bedroom 3",
                description: "Multiple homophones should not override clear '3'"
            ),
            
            // Homophone before the number
            HomophoneTest(
                spokenText: "went to bedroom too but meant bedroom 3",
                expectedIndex: 2,
                expectedItem: "Bedroom 3",
                description: "Later clear number should take precedence"
            ),
            
            // Test with punctuation variations
            HomophoneTest(
                spokenText: "bedroom 3, to be clear",
                expectedIndex: 2,
                expectedItem: "Bedroom 3",
                description: "Comma separation should maintain '3'"
            ),
            
            HomophoneTest(
                spokenText: "bedroom 3 - to clarify",
                expectedIndex: 2,
                expectedItem: "Bedroom 3",
                description: "Dash separation should maintain '3'"
            )
        ]
        
        // Run homophone confusion tests
        for test in homophoneTests {
            if let match = SpeechNormalizer.findBestMatchWithPhrases(
                spokenText: test.spokenText,
                in: bedroomItems
            ) {
                XCTAssertEqual(match.index, test.expectedIndex,
                             "\(test.description)\n" +
                             "Input: '\(test.spokenText)'\n" +
                             "Expected: '\(test.expectedItem)' (index \(test.expectedIndex))\n" +
                             "Got: '\(bedroomItems[match.index])' (index \(match.index))")
            } else {
                XCTFail("\(test.description) - No match found for '\(test.spokenText)'")
            }
        }
        
        // Test that actual homophones work correctly when intended
        let intendedHomophoneTests: [HomophoneTest] = [
            HomophoneTest(
                spokenText: "go to bedroom too",
                expectedIndex: 1,
                expectedItem: "Bedroom 2",
                description: "'too' should match 'two' when no other number is present"
            ),
            
            HomophoneTest(
                spokenText: "bedroom for inspection",
                expectedIndex: 3,
                expectedItem: "Bedroom 4",
                description: "'for' should match 'four' when no other number is present"
            )
        ]
        
        for test in intendedHomophoneTests {
            if let match = SpeechNormalizer.findBestMatchWithPhrases(
                spokenText: test.spokenText,
                in: bedroomItems
            ) {
                XCTAssertEqual(match.index, test.expectedIndex,
                             "\(test.description)\n" +
                             "Input: '\(test.spokenText)'\n" +
                             "Expected: '\(test.expectedItem)' (index \(test.expectedIndex))\n" +
                             "Got: '\(bedroomItems[match.index])' (index \(match.index))")
            } else {
                XCTFail("\(test.description) - No match found for '\(test.spokenText)'")
            }
        }
    }
    
    // MARK: - Private Method Tests (findBestMatchWithEmbedding)
    
    @available(iOS 17.0, *)
    func testFindBestMatchWithEmbedding_SemanticMatching() {
        let items = ["Fire Damage", "Water Damage", "Structural Damage"]
        
        // Test semantic understanding
        if let match = SpeechNormalizer.findBestMatchWithEmbedding(
            spokenText: "moisture intrusion visible",
            in: items,
            threshold: 0.4
        ) {
            XCTAssertEqual(match.index, 1, "Should semantically match 'Water Damage'")
            XCTAssertGreaterThan(match.score, 0.4)
        } else {
            XCTFail("Should find semantic match")
        }
        
        // Test with related concepts
        if let match = SpeechNormalizer.findBestMatchWithEmbedding(
            spokenText: "flames and burn marks",
            in: items,
            threshold: 0.4
        ) {
            XCTAssertEqual(match.index, 0, "Should match 'Fire Damage'")
            XCTAssertGreaterThan(match.score, 0.4)
        } else {
            XCTFail("Should find conceptual match")
        }
    }
    
    @available(iOS 17.0, *)
    func testFindBestMatchWithEmbedding_LanguageSupport() {
        let items = ["Smoke Detector", "Fire Alarm", "Sprinkler System"]
        
        // Test with unsupported language (should return nil)
        let unsupportedMatch = SpeechNormalizer.findBestMatchWithEmbedding(
            spokenText: "smoke detector",
            in: items,
            language: "zz" // Invalid language code
        )
        XCTAssertNil(unsupportedMatch, "Should return nil for unsupported language")
        
        // Test with supported language
        if let match = SpeechNormalizer.findBestMatchWithEmbedding(
            spokenText: "smoke detector",
            in: items,
            language: "en"
        ) {
            XCTAssertEqual(match.index, 0)
            XCTAssertGreaterThan(match.score, 0.9)
        } else {
            XCTFail("Should find match with valid language")
        }
    }
    
    @available(iOS 17.0, *)
    func testFindBestMatchWithEmbedding_FallbackBehavior() {
        // Test with empty text (should fail to get embedding)
        let emptyMatch = SpeechNormalizer.findBestMatchWithEmbedding(
            spokenText: "",
            in: ["Test Item"]
        )
        XCTAssertNil(emptyMatch, "Should return nil for empty text")
        
        // Test with very short text
        let shortMatch = SpeechNormalizer.findBestMatchWithEmbedding(
            spokenText: "a",
            in: ["Test Item"]
        )
        // May or may not return nil depending on embedding behavior
        if shortMatch != nil {
            XCTAssertLessThan(shortMatch!.score, 0.5, "Very short text should have low score")
        }
    }
    
    // MARK: - Private Method Tests (findBestMatchMultiSentence)
    
    @available(iOS 17.0, *)
    func testFindBestMatchMultiSentence_SegmentProcessing() {
        let items = ["Fire Alarm Panel", "Smoke Detector", "Sprinkler System"]
        
        // Test multi-segment matching
        let results = SpeechNormalizer.findBestMatchMultiSentence(
            spokenText: "checked the smoke detector and then inspected the fire alarm panel",
            in: items
        )
        
        XCTAssertFalse(results.isEmpty)
        // Since we return multiple matches, check the first one
        if let firstMatch = results.first {
            // Should find relevant items - could be Smoke Detector or Fire Alarm Panel
            XCTAssertTrue([0, 1].contains(firstMatch.index), "Should find Fire Alarm Panel or Smoke Detector")
            XCTAssertTrue(firstMatch.segmentInfo.contains("Segment"))
        }
    }
    
    @available(iOS 17.0, *)
    func testFindBestMatchMultiSentence_BackToFrontProcessing() {
        let items = ["HVAC Filter", "Air Filter", "Return Air Filter"]
        
        // Later context should influence earlier matches
        let results = SpeechNormalizer.findBestMatchMultiSentence(
            spokenText: "check the filter and inspect the return air system",
            in: items,
            threshold: 0.1  // Lower threshold to increase match likelihood
        )
        
        XCTAssertFalse(results.isEmpty, "Should find contextual matches")
        if let firstMatch = results.first {
            // Should find one of the filter-related items
            XCTAssertTrue([0, 1, 2].contains(firstMatch.index), "Should find a filter item")
            XCTAssertTrue(firstMatch.segmentInfo.contains("Segment") || firstMatch.segmentInfo == "Single segment")
        }
    }
    
    @available(iOS 17.0, *)
    func testFindBestMatchMultiSentence_SingleBestMatch() {
        let items = ["Item A", "Item B", "Item C", "Item D", "Item E"]
        
        // Test that it returns multiple matches from back-to-front processing
        let results = SpeechNormalizer.findBestMatchMultiSentence(
            spokenText: "check item a and item b also item c plus item d and item e",
            in: items
        )
        
        XCTAssertFalse(results.isEmpty)
        // Should return multiple matches, with good scores for mentioned items
        // The function may return up to 3 matches by default
        XCTAssertGreaterThan(results.count, 0)
        
        // Verify that at least one of the items is found
        let matchedIndices = results.map { $0.index }
        XCTAssertTrue(matchedIndices.contains(where: { (0...4).contains($0) }), "Should find at least one item")
    }
    
    // MARK: - Private Method Tests (findBestMatchWithMultiSentence)
    
    @available(iOS 17.0, *)
    func testFindBestMatchWithMultiSentence_AutomaticSegmentation() {
        let items = ["Electrical Panel", "Circuit Breaker", "Main Disconnect"]
        
        // Test with short text (should not use segmentation)
        if let match = SpeechNormalizer.findBestMatchWithMultiSentence(
            spokenText: "electrical panel",
            in: items
        ) {
            XCTAssertEqual(match.index, 0)
            // Short text should use direct matching
        } else {
            XCTFail("Should find match")
        }
        
        // Test with complex text (should use segmentation)
        if let match = SpeechNormalizer.findBestMatchWithMultiSentence(
            spokenText: "inspected the breakers and tested the main disconnect switch",
            in: items
        ) {
            // Should find one of the relevant items
            XCTAssertTrue([1, 2].contains(match.index))
        } else {
            XCTFail("Should find match with segmentation")
        }
    }
    
    @available(iOS 17.0, *)
    func testFindBestMatchWithMultiSentence_ConjunctionHandling() {
        let items = ["Fire Extinguisher", "Fire Alarm", "Fire Hose"]
        
        // Test coordination patterns
        let testCases = [
            ("fire extinguisher and alarm", [0, 1]),
            ("checked fire alarm then fire hose", [1, 2]),
            ("fire extinguisher plus fire alarm systems", [0, 1])
        ]
        
        for (text, expectedIndices) in testCases {
            if let match = SpeechNormalizer.findBestMatchWithMultiSentence(
                spokenText: text,
                in: items
            ) {
                XCTAssertTrue(expectedIndices.contains(match.index),
                              "\(text) should match one of indices \(expectedIndices)")
            } else {
                XCTFail("Should find match for: \(text)")
            }
        }
    }
    
    @available(iOS 17.0, *)
    func testFindBestMatchWithMultiSentence_ThresholdBehavior() {
        let items = ["Roof Access", "Roof Damage", "Roof Drainage"]
        
        // Test with low threshold
        if let match = SpeechNormalizer.findBestMatchWithMultiSentence(
            spokenText: "rooftop equipment inspection",
            in: items,
            threshold: 0.3
        ) {
            // Should find something roof-related
            XCTAssertTrue(match.score > 0.3)
        } else {
            XCTFail("Should find match with low threshold")
        }
        
        // Test with high threshold
        let noMatch = SpeechNormalizer.findBestMatchWithMultiSentence(
            spokenText: "rooftop equipment inspection",
            in: items,
            threshold: 0.9
        )
        XCTAssertNil(noMatch, "Should not find match with very high threshold")
    }
    
    @available(iOS 17.0, *)
    func testFindBestMatchWithMultiSentence_EdgeCases() {
        let items = ["Test Item 1", "Test Item 2"]
        
        // Test with text containing only conjunctions
        let conjunctionOnly = SpeechNormalizer.findBestMatchWithMultiSentence(
            spokenText: "and then also plus",
            in: items
        )
        XCTAssertNil(conjunctionOnly, "Should not match conjunction-only text")
        
        // Test with very long text (should trigger segmentation)
        let longText = String(repeating: "test ", count: 20) + "item one"
        if let match = SpeechNormalizer.findBestMatchWithMultiSentence(
            spokenText: longText,
            in: items
        ) {
            XCTAssertTrue([0, 1].contains(match.index), "Should find one of the test items in long text")
        } else {
            XCTFail("Should handle long text")
        }
    }
    
    // MARK: - Integration Tests for New Architecture
    
    func testPublicFindBestMatch_SimplifiedAPI() {
        let items = ["Smoke Detector", "Fire Alarm", "Sprinkler System"]
        
        // Test that public API works seamlessly
        if let match = SpeechNormalizer.findBestMatch(
            spokenText: "smoke detector tested and working",
            in: items
        ) {
            XCTAssertEqual(match.index, 0)
            XCTAssertGreaterThan(match.score, 0.8)
        } else {
            XCTFail("Public API should find match")
        }
    }
    
    @available(iOS 17.0, *)
    func testPublicFindBestMatch_AutomaticMethodSelection() {
        let items = ["Emergency Lighting", "Exit Signs", "Emergency Exit Lighting System"]
        
        // Test with complex multi-part speech (should use multi-sentence internally)
        if let match = SpeechNormalizer.findBestMatch(
            spokenText: "checked emergency systems and verified exit lighting is operational",
            in: items
        ) {
            // Should intelligently pick the best match
            XCTAssertNotNil(match)
            XCTAssertGreaterThan(match.score, 0.5)
        } else {
            XCTFail("Should handle complex speech")
        }
        
        // Test with simple speech (should use direct matching)
        if let match = SpeechNormalizer.findBestMatch(
            spokenText: "exit signs",
            in: items
        ) {
            XCTAssertEqual(match.index, 1)
            XCTAssertGreaterThan(match.score, 0.9)
        } else {
            XCTFail("Should handle simple speech")
        }
    }
}
