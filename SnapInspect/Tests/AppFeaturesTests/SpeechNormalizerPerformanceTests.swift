@testable import AppFeatures
import XCTest

final class SpeechNormalizerPerformanceTests: XCTestCase {
    // MARK: - Test Data Generation
    
    /// Generates a large list of room items for performance testing
    private func generateLargeItemList(count: Int) -> [String] {
        var items: [String] = []
        
        // Base items
        let baseItems = ["Bedroom", "Bathroom", "Kitchen", "Living Room", "Office", "Garage", "Basement", "Attic"]
        
        // Add numbered variations
        for base in baseItems {
            items.append(base)
            for i in 2 ... 10 {
                items.append("\(base) \(i)")
            }
        }
        
        // Add descriptive variations
        let prefixes = ["Master", "Guest", "Main", "Upper", "Lower", "Front", "Back", "Side"]
        for prefix in prefixes {
            for base in baseItems {
                items.append("\(prefix) \(base)")
            }
        }
        
        // Fill remaining with generic items
        while items.count < count {
            let index = items.count + 1
            items.append("Room \(index)")
        }
        
        return Array(items.prefix(count))
    }
    
    // MARK: - Performance Tests
    
    func testOrdinalMatchingPerformance_SmallList() {
        let items = generateLargeItemList(count: 50)
        let testPhrases = [
            "second bedroom",
            "third bathroom",
            "kitchen 4",
            "fifth office",
            "room twenty"
        ]
        
        measure {
            for phrase in testPhrases {
                _ = SpeechNormalizer.findBestMatchWithPhrases(spokenText: phrase, in: items)
            }
        }
    }
    
    func testOrdinalMatchingPerformance_MediumList() {
        let items = generateLargeItemList(count: 200)
        let testPhrases = [
            "second bedroom inspection",
            "checking the third bathroom",
            "kitchen 4 needs work",
            "fifth office window",
            "room twenty ceiling"
        ]
        
        measure {
            for phrase in testPhrases {
                _ = SpeechNormalizer.findBestMatchWithPhrases(spokenText: phrase, in: items)
            }
        }
    }
    
    func testOrdinalMatchingPerformance_LargeList() {
        let items = generateLargeItemList(count: 300) // Reduced from 500 to 300 for faster execution
        let testPhrases = [
            "now I'm in the second bedroom",
            "checking the third bathroom door",
            "kitchen 4 appliances need inspection",
            "fifth office has water damage",
            "room twenty needs complete review"
        ]
        
        measure {
            for phrase in testPhrases {
                _ = SpeechNormalizer.findBestMatchWithPhrases(spokenText: phrase, in: items)
            }
        }
    }
    
    func testOrdinalVariationGeneration_Performance() {
        let testPhrases = [
            "first second third fourth fifth sixth seventh eighth ninth tenth",
            "bedroom one two three four five six seven eight nine ten",
            "1st 2nd 3rd 4th 5th floor inspection complete",
            "twenty first twenty second twenty third items",
            "checking rooms one through fifty"
        ]
        
        measure {
            for phrase in testPhrases {
                _ = OrdinalMatcher.createVariations(phrase)
            }
        }
    }
    
    func testOrdinalVariationCaching_Performance() {
        // Test that caching doesn't significantly degrade performance on repeated calls
        let testPhrase = "second bedroom third bathroom fourth kitchen"
        
        // Clear cache to start fresh
        OrdinalMatcher.clearCache()
        
        // Warm up cache first
        _ = OrdinalMatcher.createVariations(testPhrase)
        
        // Measure cached performance
        measure(metrics: [XCTClockMetric()]) {
            for _ in 0..<20 {
                _ = OrdinalMatcher.createVariations(testPhrase)
            }
        }
        
        // This test measures performance with cache warmed up
        // The cache should help with repeated calls to the same phrase
    }
    
    func testComplexPhraseMatching_Performance() {
        let items = [
            "Master Bedroom Suite",
            "Guest Bedroom 2",
            "Children's Bedroom 3",
            "Office Space 1",
            "Conference Room A",
            "Storage Room B-12",
            "Utility Closet 4-A",
            "Mechanical Room M-1"
        ]
        
        let complexPhrases = [
            "I'm now inspecting the second guest bedroom which has water damage",
            "The third children's bedroom ceiling shows signs of moisture",
            "Conference room A needs immediate attention for safety",
            "Storage room B twelve has structural issues",
            "Mechanical room M one systems are functioning properly"
        ]
        
        measure {
            for phrase in complexPhrases {
                for _ in 0..<3 {
                    _ = SpeechNormalizer.findBestMatchWithPhrases(spokenText: phrase, in: items)
                }
            }
        }
    }
    
    // MARK: - Baseline Tests
    
    func testEstablishBaseline_SimpleMatch() {
        let items = ["Bedroom", "Kitchen", "Bathroom"]
        
        measure(metrics: [XCTClockMetric(), XCTMemoryMetric()]) {
            for _ in 0..<100 {
                _ = SpeechNormalizer.findBestMatchWithPhrases(spokenText: "bedroom", in: items)
            }
        }
    }
    
    func testEstablishBaseline_OrdinalMatch() {
        let items = ["Bedroom", "Bedroom 2", "Bedroom 3"]
        
        measure(metrics: [XCTClockMetric(), XCTMemoryMetric()]) {
            for _ in 0..<100 {
                _ = SpeechNormalizer.findBestMatchWithPhrases(spokenText: "second bedroom", in: items)
            }
        }
    }
    
    func testFindBestMatchWithPhrases_Performance() {
        let items = Array(repeating: ["Smoke Detector", "Fire Alarm", "Emergency Exit", "Sprinkler System", "Exit Sign"], count: 20).flatMap { $0 }
        
        measure {
            for _ in 0..<100 {
                _ = SpeechNormalizer.findBestMatchWithPhrases(
                    spokenText: "smoke detector checked and functioning",
                    in: items
                )
            }
        }
    }
    
    @available(iOS 17.0, *)
    func testFindBestMatchWithMultiSentence_Performance() {
        let items = ["Fire Alarm Panel", "Smoke Detector", "Sprinkler System", "Emergency Exit", "Fire Extinguisher"]
        let complexText = "checked the smoke detector and fire alarm panel then inspected the sprinkler system and verified emergency exits are clear"
        
        measure {
            for _ in 0..<50 {
                _ = SpeechNormalizer.findBestMatchWithMultiSentence(
                    spokenText: complexText,
                    in: items
                )
            }
        }
    }
    
    func testCachePerformance() {
        // First call should be slower (not cached)
        let text = "one thousand nine hundred eighty four dollars and twenty five percent off"
        _ = SpeechNormalizer.normalize(text)

        // Measure cached performance
        measure {
            for _ in 0..<1000 {
                _ = SpeechNormalizer.normalize(text)
            }
        }
    }
    
    func testPerformanceOfComplexNormalization() {
        measure {
            for _ in 0..<20 {
                _ = SpeechNormalizer.normalize("one thousand nine hundred eighty four dollars and twenty five percent off")
            }
        }
    }

    func testPerformanceOfSimpleNormalization() {
        measure {
            for _ in 0..<100 {
                _ = SpeechNormalizer.normalize("twenty three percent")
            }
        }
    }

    // MARK: - Linguistic Analysis Tests

    func testExtractKeyPhrases() {
        let phrases = SpeechNormalizer.extractKeyPhrases("The smoke detector is checked and working properly")
        print("Extracted phrases: \(phrases)")
        XCTAssertTrue(phrases.contains("smoke"))
        XCTAssertTrue(phrases.contains("detector"))
        XCTAssertTrue(phrases.contains("check") || phrases.contains("checked")) // Lemmatized form of "checked"
        XCTAssertTrue(phrases.contains("work") || phrases.contains("working")) // Lemmatized form of "working"
        // only extract .noun, .verb, .adjective
        XCTAssertFalse(phrases.contains("properly"))
    }

    func testLevenshteinDistance() {
        XCTAssertEqual(SpeechNormalizer.levenshteinDistance("smoke", "smoke"), 0)
        XCTAssertEqual(SpeechNormalizer.levenshteinDistance("detector", "detectors"), 1)
        XCTAssertEqual(SpeechNormalizer.levenshteinDistance("check", "checked"), 2)
        XCTAssertEqual(SpeechNormalizer.levenshteinDistance("cat", "dog"), 3)
    }

    func testSimilarityScore() {
        XCTAssertEqual(SpeechNormalizer.similarityScore("smoke", "smoke"), 1.0)
        XCTAssertGreaterThan(SpeechNormalizer.similarityScore("detector", "detectors"), 0.8)
        XCTAssertGreaterThan(SpeechNormalizer.similarityScore("check", "checked"), 0.6)
        XCTAssertLessThan(SpeechNormalizer.similarityScore("cat", "dog"), 0.5)
    }

    func testLemmatize() {
        // Test common lemmatizations
        XCTAssertEqual(SpeechNormalizer.lemmatize("detectors"), "detector")
        XCTAssertEqual(SpeechNormalizer.lemmatize("checked"), "check")
        XCTAssertEqual(SpeechNormalizer.lemmatize("working"), "work")
        XCTAssertEqual(SpeechNormalizer.lemmatize("alarms"), "alarm")
    }

    func testDebugMatchingIssue() {
        let items = [
            "Fire Extinguisher",
            "Smoke Detector",
            "Exit Signs",
            "Emergency Lighting",
            "Smoke Detectors checked"
        ]

        let spokenText = "smoke detector checked the condition is good"
        let spokenKeyPhrases = SpeechNormalizer.extractKeyPhrases(spokenText)
        print("\nSpoken key phrases: \(spokenKeyPhrases)")

        for (index, item) in items.enumerated() {
            let score = SpeechNormalizer.matchScore(spokenText: spokenText, itemName: item)
            let itemKeyPhrases = SpeechNormalizer.extractKeyPhrases(item)
            print("\nItem \(index) '\(item)':")
            print("  Key phrases: \(itemKeyPhrases)")
            print("  Base score: \(score)")

            let spokenKeyPhrasesSet = Set(spokenKeyPhrases)
            let itemKeyPhrasesSet = Set(itemKeyPhrases)
            let common = itemKeyPhrasesSet.intersection(spokenKeyPhrasesSet)
            print("  Common phrases: \(common)")

            if let match = SpeechNormalizer.findBestMatch(spokenText: spokenText, in: [item]) {
                print("  Final score with bonus: \(match.score)")
            }
        }

        // Custom calculation to show raw scores
        for (index, item) in items.enumerated() {
            var score = SpeechNormalizer.matchScore(spokenText: spokenText, itemName: item)
            let itemKeyPhrases = Set(SpeechNormalizer.extractKeyPhrases(item))
            let commonKeyPhrases = itemKeyPhrases.intersection(Set(spokenKeyPhrases))
            if !commonKeyPhrases.isEmpty {
                let matchRatio = Double(commonKeyPhrases.count) / Double(spokenKeyPhrases.count)
                let countBonus = Double(commonKeyPhrases.count) * 0.15
                let keyPhraseBonus = (matchRatio * 0.4) + countBonus
                score = score + keyPhraseBonus
                print("Item \(index): raw score with bonus = \(score)")
            }
        }

        if let match = SpeechNormalizer.findBestMatch(spokenText: spokenText, in: items) {
            print("\nBest match: index \(match.index) ('\(items[match.index])') with score \(match.score)")
        }
    }

    // MARK: - Inspection Item Matching Tests

    func testMatchScore() {
        // Test exact match
        let exactScore = SpeechNormalizer.matchScore(
            spokenText: "smoke detector",
            itemName: "Smoke Detector"
        )
        XCTAssertGreaterThan(exactScore, 0.9)

        // Test plural/singular variation
        let pluralScore = SpeechNormalizer.matchScore(
            spokenText: "smoke detectors checked",
            itemName: "Smoke Detector Check"
        )
        XCTAssertGreaterThan(pluralScore, 0.7)

        // Test complex phrase matching
        let complexScore = SpeechNormalizer.matchScore(
            spokenText: "smoke detector checked the condition is good",
            itemName: "Smoke Detectors checked"
        )
        XCTAssertGreaterThanOrEqual(complexScore, 0.6)

        // Test unrelated phrases
        let unrelatedScore = SpeechNormalizer.matchScore(
            spokenText: "fire extinguisher inspected",
            itemName: "Smoke Detector"
        )
        XCTAssertLessThan(unrelatedScore, 0.3)
    }

    func testFindBestMatch() {
        let items = [
            "Fire Extinguisher",
            "Smoke Detector",
            "Exit Signs",
            "Emergency Lighting",
            "Smoke Detectors checked"
        ]

        // Test exact match
        if let match = SpeechNormalizer.findBestMatch(
            spokenText: "smoke detector",
            in: items
        ) {
            XCTAssertEqual(match.index, 1)
            XCTAssertGreaterThan(match.score, 0.8)
        } else {
            XCTFail("Expected to find a match")
        }

        // Test complex phrase matching
        if let match = SpeechNormalizer.findBestMatch(
            spokenText: "smoke detector checked the condition is good",
            in: items
        ) {
            XCTAssertEqual(match.index, 4) // Should match "Smoke Detectors checked"
            XCTAssertGreaterThan(match.score, 0.5)
        } else {
            XCTFail("Expected to find a match")
        }

        // Test no match with high threshold
        let noMatch = SpeechNormalizer.findBestMatch(
            spokenText: "water fountain",
            in: items,
            threshold: 0.8
        )
        XCTAssertNil(noMatch)
    }

    func testRealWorldInspectionMatching() {
        let inspectionItems = [
            "HVAC System",
            "Fire Alarm Panel",
            "Emergency Exit Doors",
            "Sprinkler System",
            "Electrical Panel",
            "Carbon Monoxide Detectors"
        ]

        // Test various spoken variations
        let testCases: [(spoken: String, expectedIndex: Int)] = [
            ("h vac system checked", 0),
            ("fire alarm panel is working", 1),
            ("emergency exit door functional", 2),
            ("sprinklers tested", 3),
            ("electrical panels inspected", 4),
            ("carbon monoxide detector alarm tested", 5)
        ]

        for testCase in testCases {
            if let match = SpeechNormalizer.findBestMatch(
                spokenText: testCase.spoken,
                in: inspectionItems,
                threshold: 0.7
            ) {
                XCTAssertEqual(match.index, testCase.expectedIndex,
                               "Failed to match '\(testCase.spoken)' to '\(inspectionItems[testCase.expectedIndex])'")
            } else {
                XCTFail("Failed to find match for '\(testCase.spoken)'")
            }
        }
    }

    // MARK: - Performance Tests for Linguistic Matching

    func testPerformanceOfLinguisticMatching() {
        let items = [
            "Fire Extinguisher",
            "Smoke Detector",
            "Exit Signs",
            "Emergency Lighting",
            "HVAC System",
            "Electrical Panel",
            "Sprinkler System",
            "Fire Alarm Panel"
        ]

        measure {
            for _ in 0..<20 {
                _ = SpeechNormalizer.findBestMatch(
                    spokenText: "smoke detector checked and functioning properly",
                    in: items
                )
            }
        }
    }
    
    func testFindBestMatchInHierarchy_PerformanceWithLargeDataset() {
        // Test performance with a large number of items
        var parentItems: [String] = []
        var childItems: [String] = []
        
        // Generate 100 parent items
        for i in 1...100 {
            parentItems.append("Area \(i)")
        }
        
        // Generate 200 child items
        for i in 1...200 {
            childItems.append("Item \(i) - Status Check")
        }
        
        // Measure performance
        let startTime = CFAbsoluteTimeGetCurrent()
        
        let result = SpeechNormalizer.findBestMatchInHierarchy(
            spokenText: "checking item 150 status",
            childItems: childItems,
            parentItems: parentItems,
            threshold: 0.7
        )
        
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        
        // Performance assertion - should complete within reasonable time
        XCTAssertLessThan(timeElapsed, 1.0, "Search should complete within 1 second")
        
        // Verify correct match
        XCTAssertNotNil(result)
        // Verify correct match
        XCTAssertNotNil(result, "Expected to find match for 'Item 150'")
        XCTAssertEqual(result?.level, .child)
        XCTAssertEqual(result?.index, 149) // "Item 150" (0-indexed)
        
        print("Performance test completed in \(timeElapsed) seconds")
    }
}
