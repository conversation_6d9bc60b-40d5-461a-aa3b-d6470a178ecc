@testable import AppFeatures
import XCTest

final class OrdinalMatcherTests: XCTestCase {
    
    // MARK: - createVariations Tests
    
    func testCreateVariations_EmptyString() {
        let variations = OrdinalMatcher.createVariations("")
        XCTAssertEqual(variations, [])
    }
    
    func testCreateVariations_SingleWord() {
        let variations = OrdinalMatcher.createVariations("bedroom")
        XCTAssertEqual(variations, ["bedroom"])
    }
    
    func testCreateVariations_BasicOrdinalPatterns() {
        // Test "second bedroom" pattern
        let variations1 = OrdinalMatcher.createVariations("second bedroom")
        XCTAssertTrue(variations1.contains("bedroom 2"))
        XCTAssertTrue(variations1.contains("second bedroom"))
        
        // Test "bedroom three" pattern
        let variations2 = OrdinalMatcher.createVariations("bedroom three")
        XCTAssertTrue(variations2.contains("bedroom 3"))
        XCTAssertTrue(variations2.contains("bedroom three"))
        
        // Test "bedroom 4" pattern (should remain as-is)
        let variations3 = OrdinalMatcher.createVariations("bedroom 4")
        XCTAssertTrue(variations3.contains("bedroom 4"))
    }
    
    func testCreateVariations_CompoundNouns() {
        let contextItems = ["Loading Bay", "Loading Bay 2", "Loading Bay 3"]
        
        // Test compound noun with ordinal
        let variations = OrdinalMatcher.createVariations("third loading bay", contextItems: contextItems)
        XCTAssertTrue(variations.contains("loading bay 3"))
        
        // Test compound noun with written number
        let variations2 = OrdinalMatcher.createVariations("loading bay two", contextItems: contextItems)
        XCTAssertTrue(variations2.contains("loading bay 2"))
    }
    
    func testCreateVariations_SpecialNumberPattern() {
        // Test "noun number X" pattern
        let variations = OrdinalMatcher.createVariations("window number two")
        XCTAssertTrue(variations.contains("window 2"))
        
        // Test with ordinal
        let variations2 = OrdinalMatcher.createVariations("room number third")
        XCTAssertTrue(variations2.contains("room 3"))
    }
    
    func testCreateVariations_HomophoneHandling() {
        // Test that homophones are not converted when real numbers exist
        let variations1 = OrdinalMatcher.createVariations("bedroom 3. To be clear")
        XCTAssertTrue(variations1.contains("bedroom 3. To be clear"))
        XCTAssertFalse(variations1.contains("bedroom 3. 2 be clear"))
        
        // Test that homophones are converted when no numbers exist
        let variations2 = OrdinalMatcher.createVariations("bedroom too")
        XCTAssertTrue(variations2.contains("bedroom 2"))
    }
    
    func testCreateVariations_MultiplePatterns() {
        let contextItems = ["Conference Room", "Conference Room 2", "Conference Room 3"]
        
        // Test text with multiple patterns
        let variations = OrdinalMatcher.createVariations("from second conference room to third conference room", contextItems: contextItems)
        let hasSecondPattern = variations.contains(where: { $0.contains("conference room 2") })
        let hasThirdPattern = variations.contains(where: { $0.contains("conference room 3") })
        XCTAssertTrue(hasSecondPattern)
        XCTAssertTrue(hasThirdPattern)
    }
    
    func testCreateVariations_SpecialCharacters() {
        // Test with punctuation
        let variations1 = OrdinalMatcher.createVariations("bedroom #2")
        XCTAssertTrue(variations1.contains("bedroom #2"))
        
        // Test with parentheses
        let variations2 = OrdinalMatcher.createVariations("bedroom (3)")
        XCTAssertTrue(variations2.contains("bedroom (3)"))
        
        // Test with hyphens
        let variations3 = OrdinalMatcher.createVariations("bedroom-two")
        XCTAssertTrue(variations3.contains("bedroom-two"))
    }
    
    func testCreateVariations_EdgeCases() {
        // Very long string
        let longText = String(repeating: "test ", count: 100) + "second bedroom"
        let variations = OrdinalMatcher.createVariations(longText)
        XCTAssertTrue(variations.count > 0)
        
        // Numbers at start
        let variations2 = OrdinalMatcher.createVariations("3 bedroom house")
        XCTAssertTrue(variations2.contains("3 bedroom house"))
        
        // Numbers at end
        let variations3 = OrdinalMatcher.createVariations("house has bedrooms 3")
        XCTAssertTrue(variations3.contains("house has bedrooms 3"))
    }
    
    func testCreateVariations_ComplexCompoundNouns() {
        let contextItems = [
            "Emergency Exit Stairwell",
            "Emergency Exit Stairwell 2",
            "Emergency Exit Stairwell 3",
            "Fire Suppression System",
            "Fire Suppression System 2"
        ]
        
        // Test very long compound noun
        let variations = OrdinalMatcher.createVariations("second emergency exit stairwell", contextItems: contextItems)
        XCTAssertTrue(variations.contains("emergency exit stairwell 2"))
        
        // Test compound with hyphens
        let contextItems2 = ["Fire-Exit", "Fire-Exit 2", "Fire-Exit 3"]
        let variations2 = OrdinalMatcher.createVariations("third fire-exit", contextItems: contextItems2)
        // Should handle hyphenated compound nouns
        XCTAssertTrue(variations2.count > 0)
    }
    
    // MARK: - extractFirstNumber Tests
    
    func testExtractFirstNumber_BasicCases() {
        XCTAssertEqual(OrdinalMatcher.extractFirstNumber(from: "bedroom 2"), "2")
        XCTAssertEqual(OrdinalMatcher.extractFirstNumber(from: "123 main street"), "123")
        XCTAssertEqual(OrdinalMatcher.extractFirstNumber(from: "room number 45"), "45")
        XCTAssertNil(OrdinalMatcher.extractFirstNumber(from: "no numbers here"))
    }
    
    func testExtractFirstNumber_EdgeCases() {
        XCTAssertEqual(OrdinalMatcher.extractFirstNumber(from: "0 items"), "0")
        XCTAssertEqual(OrdinalMatcher.extractFirstNumber(from: "negative -5"), "5") // Only extracts positive part
        XCTAssertEqual(OrdinalMatcher.extractFirstNumber(from: "$100"), "100")
        XCTAssertEqual(OrdinalMatcher.extractFirstNumber(from: "3.14"), "3") // Only first number part
    }
    
    // MARK: - isNumberBasedMatch Tests
    
    func testIsNumberBasedMatch_BasicMatching() {
        // Direct number match
        XCTAssertTrue(OrdinalMatcher.isNumberBasedMatch(
            spokenText: "bedroom 2",
            variation: "bedroom 2",
            itemName: "Bedroom 2"
        ))
        
        // Ordinal to number match
        XCTAssertTrue(OrdinalMatcher.isNumberBasedMatch(
            spokenText: "second bedroom",
            variation: "bedroom 2",
            itemName: "Bedroom 2"
        ))
        
        // Written number match
        XCTAssertTrue(OrdinalMatcher.isNumberBasedMatch(
            spokenText: "bedroom two",
            variation: "bedroom 2",
            itemName: "Bedroom 2"
        ))
    }
    
    func testIsNumberBasedMatch_NoNumberIndicators() {
        // No numbers in spoken text
        XCTAssertFalse(OrdinalMatcher.isNumberBasedMatch(
            spokenText: "master bedroom",
            variation: "master bedroom",
            itemName: "Master Bedroom"
        ))
    }
    
    func testIsNumberBasedMatch_FirstItemSpecialCase() {
        // "first bedroom" should match base "Bedroom" (no number)
        XCTAssertTrue(OrdinalMatcher.isNumberBasedMatch(
            spokenText: "first bedroom",
            variation: "bedroom 1",
            itemName: "Bedroom"
        ))
        
        // "room one" should match base "Room"
        XCTAssertTrue(OrdinalMatcher.isNumberBasedMatch(
            spokenText: "room one",
            variation: "room 1",
            itemName: "Room"
        ))
    }
    
    // MARK: - Cache Management Tests
    
    func testClearCache() {
        // Create some variations to populate cache
        _ = OrdinalMatcher.createVariations("second bedroom")
        _ = OrdinalMatcher.createVariations("third floor")
        
        // Clear cache
        OrdinalMatcher.clearCache()
        
        // Cache should be cleared (we can't directly test this without exposing internals,
        // but we can verify the method doesn't crash)
        XCTAssertTrue(true)
    }
    
    // MARK: - Performance Tests
    
    func testCreateVariations_Performance() {
        let items = (1...100).map { "Room \($0)" }
        let text = "going from second room to third room then fourth room"
        
        measure {
            _ = OrdinalMatcher.createVariations(text, contextItems: items)
        }
    }
    
    func testCreateVariations_LargeContextPerformance() {
        // Create a large list of compound noun items
        let items = (1...500).flatMap { i in
            ["Conference Room \(i)", "Meeting Room \(i)", "Storage Area \(i)"]
        }
        
        let text = "third conference room needs inspection"
        
        measure {
            _ = OrdinalMatcher.createVariations(text, contextItems: items)
        }
    }
}
