//
//  AssociatedObject.swift
//  SnapInspect3
//
//  Created by <PERSON> on 2025/7/8.
//  Copyright © 2025 SnapInspect. All rights reserved.
//

import Foundation

// MARK: - Associatable Protocol

/// A protocol that provides clean, type-safe associated object functionality for Swift extensions.
/// This protocol eliminates the verbose getter/setter boilerplate code when extending existing classes
/// with new stored properties using Objective-C associated objects.
///
/// Usage:
/// ```swift
/// extension YourClass: Associatable {
///     private enum AssociatedKeys {
///         static var myProperty: UInt8 = 0
///     }
///
///     var myProperty: MyType? {
///         get { getAssociated(&AssociatedKeys.myProperty) }
///         set { setAssociated(newValue, for: &AssociatedKeys.myProperty) }
///     }
/// }
/// ```
public protocol Associatable: AnyObject {}

// MARK: - Associatable Protocol Implementation

public extension Associatable {
    /// Gets an associated object value for the given key.
    /// - Parameter key: The key for the associated object
    /// - Returns: The associated object value, or nil if not found
    func getAssociated<T>(_ key: UnsafeRawPointer) -> T? {
        return objc_getAssociatedObject(self, key) as? T
    }

    /// Sets an associated object value for the given key.
    /// - Parameters:
    ///   - value: The value to associate
    ///   - key: The key for the associated object
    ///   - policy: The association policy (defaults to RETAIN_NONATOMIC)
    func setAssociated<T>(_ value: T?, for key: UnsafeRawPointer, policy: objc_AssociationPolicy = .OBJC_ASSOCIATION_RETAIN_NONATOMIC) {
        objc_setAssociatedObject(self, key, value, policy)
    }

    /// Gets an associated object value, creating it with the provided closure if it doesn't exist.
    /// This is useful for lazy initialization of associated objects.
    /// - Parameters:
    ///   - key: The key for the associated object
    ///   - policy: The association policy (defaults to RETAIN_NONATOMIC)
    ///   - defaultValue: A closure that creates the default value if none exists
    /// - Returns: The associated object value
    func getOrSetAssociated<T>(_ key: UnsafeRawPointer, policy: objc_AssociationPolicy = .OBJC_ASSOCIATION_RETAIN_NONATOMIC, defaultValue: () -> T) -> T {
        if let existing: T = getAssociated(key) {
            return existing
        }
        let newValue = defaultValue()
        setAssociated(newValue, for: key, policy: policy)
        return newValue
    }

    /// Gets an associated object value with lazy initialization using autoclosure.
    /// This provides a more convenient syntax for simple default values.
    /// - Parameters:
    ///   - key: The key for the associated object
    ///   - policy: The association policy (defaults to RETAIN_NONATOMIC)
    ///   - defaultValue: An autoclosure that creates the default value if none exists
    /// - Returns: The associated object value
    func getOrSetAssociated<T>(_ key: UnsafeRawPointer, policy: objc_AssociationPolicy = .OBJC_ASSOCIATION_RETAIN_NONATOMIC, defaultValue: @autoclosure () -> T) -> T {
        if let existing: T = getAssociated(key) {
            return existing
        }
        let newValue = defaultValue()
        setAssociated(newValue, for: key, policy: policy)
        return newValue
    }
}

// MARK: - Convenience Extensions for Common Types

public extension Associatable {
    /// Creates a lazy computed property that initializes a new instance on first access.
    /// This is useful for properties that should be created once and reused.
    /// - Parameters:
    ///   - key: The key for the associated object
    ///   - type: The type to create (inferred from context)
    /// - Returns: The associated object instance
    func lazyAssociated<T>(_ key: UnsafeRawPointer, ofType type: T.Type) -> T where T: NSObject {
        return getOrSetAssociated(key, defaultValue: T())
    }
}

// MARK: - NSObject Automatic Conformance

/// Automatic conformance for all NSObject subclasses
extension NSObject: Associatable {}


// MARK: - Extension Helper for Key Generation

public extension NSObject {
    /// Creates a unique key for associated objects.
    static func makeAssociatedObjectKey() -> UnsafeMutablePointer<UInt8> {
        return UnsafeMutablePointer<UInt8>.allocate(capacity: 1)
    }
}
