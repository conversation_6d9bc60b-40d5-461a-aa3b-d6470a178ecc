import Foundation
import NaturalLanguage
#if canImport(UIKit)
    import UIKit
#endif

/// A comprehensive speech-to-text normalization and linguistic analysis utility.
///
/// SpeechNormalizer provides:
/// - **Text Normalization**: Converts spoken forms to written representations
///   - Numbers: "twenty three" → "23", "three million" → "3000000"
///   - Ordinals: "first" → "1st", "twenty first" → "21st"
///   - Symbols: "dollar sign" → "$", "at symbol" → "@"
///   - Math: "two plus two" → "2 + 2", "multiply by ten" → "× 10"
/// - **Linguistic Analysis**: NLP-based phrase extraction and matching
///   - Key phrase extraction using NLTagger
///   - Lemmatization for word normalization
///   - N-gram extraction for partial matching
/// - **Fuzzy Matching**: Multiple algorithms for flexible text matching
///   - Levenshtein distance for edit-based similarity
///   - Soundex algorithm for phonetic matching
///   - Context-aware scoring for inspection items
/// - **Performance Optimization**: Built-in caching for repeated operations
///
/// Example usage:
/// ```swift
/// // Basic normalization
/// let normalized = SpeechNormalizer.normalize("twenty three percent")
/// // Result: "23 %"
///
/// // Legacy matching approach
/// let items = ["Smoke Detector", "Fire Extinguisher", "Exit Signs"]
/// if let match = SpeechNormalizer.findBestMatch(
///     spokenText: "smoke detector checked",
///     in: items
/// ) {
///     print("Matched item \(match.index): \(items[match.index])")
/// }
///
/// // Modern semantic matching with embeddings (iOS 17+)
/// if let match = SpeechNormalizer.findBestMatchWithEmbedding(
///     spokenText: "water damage visible",
///     in: ["Fire Damage", "Moisture Intrusion", "Structural Issues"],
///     language: "en"
/// ) {
///     print("Semantic match: \(items[match.index])") // Would match "Moisture Intrusion"
/// }
/// ```
public enum SpeechNormalizer {
    // MARK: - Supporting Types

    /// Result of a speech-to-text matching operation with confidence and context information
    public struct MatchResult {
        public let index: Int
        public let score: Double
        public let confidence: MatchConfidence
        public let segmentInfo: String

        public init(index: Int, score: Double, confidence: MatchConfidence, segmentInfo: String) {
            self.index = index
            self.score = score
            self.confidence = confidence
            self.segmentInfo = segmentInfo
        }
    }

    /// Confidence level for speech matching results
    public enum MatchConfidence {
        case high, medium, low
    }

    /// Hierarchical item level for multi-level matching
    public enum ItemLevel {
        case child
        case parent
    }

    /// Determines the confidence level based on match score
    /// - Parameter score: The match score between 0 and 1
    /// - Returns: The corresponding confidence level
    public static func confidenceLevel(for score: Double) -> MatchConfidence {
        return score > 0.8 ? .high : score > 0.5 ? .medium : .low
    }

    /// Internal representation of a text segment for multi-sentence processing
    private struct TextSegment {
        let text: String
        let range: Range<String.Index>
        let originalIndex: Int
        let isContextProvider: Bool // Marks segments that provide context for others

        init(text: String, range: Range<String.Index>, originalIndex: Int, isContextProvider: Bool = false) {
            self.text = text
            self.range = range
            self.originalIndex = originalIndex
            self.isContextProvider = isContextProvider
        }
    }

    /// Internal representation of a matched segment with scoring information
    private struct SegmentMatch {
        let segment: TextSegment
        let matchIndex: Int
        let score: Double
        let originalScore: Double
        let contextBoost: Double

        init(segment: TextSegment, matchIndex: Int, score: Double, contextBoost: Double = 0.0) {
            self.segment = segment
            self.matchIndex = matchIndex
            self.score = score + contextBoost
            originalScore = score
            self.contextBoost = contextBoost
        }
    }

    // MARK: - Configuration

    /// Configuration for the speech normalizer
    private enum Configuration {
        /// Maximum number of cached normalization results
        static let cacheSize = 1000

        /// Weight for phonetic similarity in fuzzy matching (0.0-1.0)
        static let phoneticMatchWeight = 0.3

        /// Weight for key phrase matching in fuzzy matching (0.0-1.0)
        static let keyPhraseMatchWeight = 0.65

        /// Weight for substring matching in fuzzy matching (0.0-1.0)
        static let substringMatchWeight = 0.35

        /// Minimum phrase length for key phrase extraction
        static let minPhraseLength = 2
    }

    // MARK: - Cache

    /// Thread-safe cache for normalized text
    private static let normalizationCache: NSCache<NSString, NSString> = {
        let cache = NSCache<NSString, NSString>()
        cache.countLimit = Configuration.cacheSize
        return cache
    }()

    /// Thread-safe cache for linguistic analysis
    private static let linguisticCache: NSCache<NSString, NSArray> = {
        let cache = NSCache<NSString, NSArray>()
        cache.countLimit = Configuration.cacheSize
        return cache
    }()

    // MARK: - Shared Resources

    /// Shared NLTagger instances for better performance
    private static let lexicalTagger = NLTagger(tagSchemes: [.lexicalClass])
    private static let lemmaTagger = NLTagger(tagSchemes: [.lemma])

    /// Irregular words that can't be handled by pattern matching
    /// Focused on words commonly found in inspection contexts
    private static let irregularWords: [String: String] = [
        // Common irregular verbs
        "went": "go", "ran": "run", "was": "be", "were": "be", "had": "have",
        "did": "do", "said": "say", "came": "come", "got": "get", "saw": "see",
        "took": "take", "gave": "give", "found": "find", "told": "tell",

        // Irregular nouns relevant to inspections
        "children": "child", "feet": "foot", "mice": "mouse", "men": "man",
        "women": "woman", "people": "person", "teeth": "tooth",

        // Irregular comparatives/superlatives
        "better": "good", "best": "good", "worse": "bad", "worst": "bad",
        "more": "much", "most": "much", "less": "little", "least": "little",

        // Common inspection terms with irregular forms
        "data": "datum", "criteria": "criterion", "phenomena": "phenomenon",
    ]

    /// Words ending in 'er' that should NOT be treated as comparatives
    /// These are legitimate words where 'er' is not a suffix
    private static let commonErWords: Set<String> = [
        "computer", "water", "paper", "letter", "number", "center", "center",
        "matter", "power", "tower", "cover", "over", "under", "after",
        "other", "rather", "whether", "weather", "order", "corner",
    ]

    // MARK: - Number Dictionaries

    static let singleDigits: [String: String] = [
        "zero": "0", "oh": "0", "o": "0",
        "one": "1", "won": "1",
        "two": "2", "to": "2", "too": "2",
        "three": "3", "tree": "3",
        "four": "4", "for": "4", "fore": "4",
        "five": "5",
        "six": "6", "sicks": "6",
        "seven": "7",
        "eight": "8", "ate": "8",
        "nine": "9", "nein": "9",
    ]

    private static let teens: [String: String] = [
        "ten": "10",
        "eleven": "11",
        "twelve": "12",
        "thirteen": "13",
        "fourteen": "14",
        "fifteen": "15",
        "sixteen": "16",
        "seventeen": "17",
        "eighteen": "18",
        "nineteen": "19",
    ]

    private static let tens: [String: String] = [
        "twenty": "20",
        "thirty": "30",
        "forty": "40", "fourty": "40",
        "fifty": "50",
        "sixty": "60",
        "seventy": "70",
        "eighty": "80",
        "ninety": "90",
    ]

    private static let scales: [String: Int] = [
        "hundred": 100,
        "thousand": 1000,
        "million": 1000000,
        "billion": 1000000000,
    ]

    static let ordinals: [String: String] = [
        "first": "1st", "second": "2nd", "third": "3rd", "fourth": "4th",
        "fifth": "5th", "sixth": "6th", "seventh": "7th", "eighth": "8th",
        "ninth": "9th", "tenth": "10th", "eleventh": "11th", "twelfth": "12th",
        "thirteenth": "13th", "fourteenth": "14th", "fifteenth": "15th",
        "sixteenth": "16th", "seventeenth": "17th", "eighteenth": "18th",
        "nineteenth": "19th", "twentieth": "20th", "thirtieth": "30th",
        "fortieth": "40th", "fiftieth": "50th", "sixtieth": "60th",
        "seventieth": "70th", "eightieth": "80th", "ninetieth": "90th",
        "hundredth": "100th", "thousandth": "1000th",
    ]

    // MARK: - Symbol Dictionaries

    private static let symbols: [String: String] = [
        // Currency
        "dollar": "$", "dollars": "$", "dollar sign": "$",
        "cent": "¢", "cents": "¢",
        "pound": "£", "pounds": "£", "pound sterling": "£",
        "euro": "€", "euros": "€",
        "yen": "¥", "yuan": "¥",

        // Common symbols
        "percent": "%", "percentage": "%", "per cent": "%",
        "at": "@", "at sign": "@", "at symbol": "@",
        "hashtag": "#", "hash": "#", "pound sign": "#", "number sign": "#",
        "ampersand": "&", "and sign": "&", "and symbol": "&",

        // Punctuation
        "dash": "-", "hyphen": "-", "minus": "-",
        "underscore": "_", "underline": "_",
        "slash": "/", "forward slash": "/",
        "backslash": "\\", "back slash": "\\",
        "pipe": "|", "vertical bar": "|", "vertical line": "|",
        "colon": ":", "semicolon": ";",
        "period": ".", "full stop": ".",
        "comma": ",", "apostrophe": "'", "single quote": "'",
        "quote": "\"", "double quote": "\"", "quotation mark": "\"",
        "open parenthesis": "(", "close parenthesis": ")",
        "open bracket": "[", "close bracket": "]",
        "open brace": "{", "close brace": "}",

        // Math symbols
        "plus": "+", "plus sign": "+", "add": "+",
        "minus sign": "-", "subtract": "-",
        "times": "×", "multiply by": "×", "multiplication sign": "×", "multiplied by": "×",
        "divided by": "÷", "division sign": "÷", "divide by": "÷",
        "equals": "=", "equal sign": "=", "equal to": "=",
        "less than": "<", "greater than": ">",
        "less than or equal": "≤", "greater than or equal": "≥",
        "not equal": "≠", "approximately": "≈",

        // Other common conversions
        "degree": "°", "degrees": "°",
        "copyright": "©", "registered": "®", "trademark": "™",
        "bullet": "•", "bullet point": "•",
        "star": "*", "asterisk": "*",
        "arrow": "→", "right arrow": "→", "left arrow": "←",
        "up arrow": "↑", "down arrow": "↓",
    ]

    // MARK: - Contractions

    private static let contractions: [String: String] = [
        "can't": "cannot", "won't": "will not", "isn't": "is not",
        "aren't": "are not", "wasn't": "was not", "weren't": "were not",
        "hasn't": "has not", "haven't": "have not", "hadn't": "had not",
        "doesn't": "does not", "don't": "do not", "didn't": "did not",
        "wouldn't": "would not", "shouldn't": "should not", "couldn't": "could not",
        "mightn't": "might not", "mustn't": "must not", "needn't": "need not",
        "i'm": "i am", "you're": "you are", "he's": "he is",
        "she's": "she is", "it's": "it is", "we're": "we are",
        "they're": "they are", "i've": "i have", "you've": "you have",
        "we've": "we have", "they've": "they have", "i'd": "i would",
        "you'd": "you would", "he'd": "he would", "she'd": "she would",
        "we'd": "we would", "they'd": "they would", "i'll": "i will",
        "you'll": "you will", "he'll": "he will", "she'll": "she will",
        "we'll": "we will", "they'll": "they will", "let's": "let us",
    ]
}

// MARK: - Text Normalization

public extension SpeechNormalizer {
    /// Normalizes speech text by converting spoken forms to their intended representations
    /// - Parameter text: The input text from speech recognition
    /// - Returns: Normalized text with conversions applied
    static func normalize(_ text: String) -> String {
        // Check cache first
        let cacheKey = text as NSString
        if let cached = normalizationCache.object(forKey: cacheKey) {
            return cached as String
        }

        // Handle empty or whitespace-only strings
        let trimmed = text.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmed.isEmpty else { return "" }

        // Process the text
        let normalized = processText(trimmed)

        // Cache the result
        normalizationCache.setObject(normalized as NSString, forKey: cacheKey)

        return normalized
    }

    /// Returns multiple normalization variants for flexible matching
    /// - Parameter text: The input text from speech recognition
    /// - Returns: Array of possible normalizations
    static func normalizeWithVariants(_ text: String) -> [String] {
        let normalized = normalize(text)

        // Create variants
        var variants = [normalized]

        // Also create a version with individually spaced numbers
        let words = text.lowercased().components(separatedBy: .whitespacesAndNewlines)
        var spacedResult: [String] = []

        for word in words {
            if let digit = singleDigits[word] {
                spacedResult.append(digit)
            } else if let teen = teens[word] {
                spacedResult.append(teen)
            } else if let ten = tens[word] {
                spacedResult.append(ten)
            } else if let ordinal = ordinals[word] {
                spacedResult.append(ordinal)
            } else if let symbol = symbols[word] {
                spacedResult.append(symbol)
            } else {
                spacedResult.append(word)
            }
        }

        let spacedVersion = spacedResult.joined(separator: " ")
        if spacedVersion != normalized {
            variants.append(spacedVersion)
        }

        // Add a variant with expanded contractions
        let expandedContractions = expandContractions(text)
        if expandedContractions != text, expandedContractions != normalized {
            variants.append(normalize(expandedContractions))
        }

        // Return unique variants
        return Array(Set(variants)).filter { !$0.isEmpty }
    }
}

// MARK: - Text Processing Implementation

extension SpeechNormalizer {
    /// Main text processing logic
    private static func processText(_ text: String) -> String {
        // First expand contractions
        let expanded = expandContractions(text)

        let words = expanded.lowercased().components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty }
        var result: [String] = []
        var i = 0

        while i < words.count {
            // Check for compound ordinals like "twenty first"
            if i + 1 < words.count {
                let compound = words[i] + " " + words[i + 1]
                if compound == "twenty first" || compound == "twenty-first" {
                    result.append("21st")
                    i += 2
                    continue
                } else if compound == "twenty second" || compound == "twenty-second" {
                    result.append("22nd")
                    i += 2
                    continue
                } else if compound == "twenty third" || compound == "twenty-third" {
                    result.append("23rd")
                    i += 2
                    continue
                } else if compound == "thirty first" || compound == "thirty-first" {
                    result.append("31st")
                    i += 2
                    continue
                }
            }

            // Try to parse ordinal numbers first
            if let ordinal = ordinals[words[i]] {
                result.append(ordinal)
                i += 1
                continue
            }

            // Try to parse as a number sequence
            let (convertedNumber, wordsConsumed) = parseNumberSequence(from: words, startingAt: i)

            if let number = convertedNumber {
                result.append(number)
                i += wordsConsumed
            } else {
                // Check for symbol conversions
                let phrase = buildPhrase(from: words, startingAt: i, maxWords: 4)
                var symbolFound = false

                // Check phrases from longest to shortest
                for length in (1 ... phrase.count).reversed() {
                    let checkPhrase = phrase[0 ..< length].joined(separator: " ")
                    if let symbol = symbols[checkPhrase] {
                        result.append(symbol)
                        i += length
                        symbolFound = true
                        break
                    }
                }

                if !symbolFound {
                    result.append(words[i])
                    i += 1
                }
            }
        }

        return result.joined(separator: " ")
    }

    /// Expands contractions in the text
    private static func expandContractions(_ text: String) -> String {
        var result = text.lowercased()
        for (contraction, expansion) in contractions {
            result = result.replacingOccurrences(of: contraction, with: expansion)
        }
        return result
    }

    /// Builds a phrase from consecutive words for multi-word symbol matching
    private static func buildPhrase(from words: [String], startingAt index: Int, maxWords: Int) -> [String] {
        var phrase: [String] = []
        for i in 0 ..< maxWords {
            if index + i < words.count {
                phrase.append(words[index + i])
            }
        }
        return phrase
    }

    /// Attempts to parse a number sequence starting at the given index
    /// Returns the parsed number (if any) and how many words were consumed
    private static func parseNumberSequence(from words: [String], startingAt index: Int) -> (String?, Int) {
        guard index < words.count else { return (nil, 0) }

        var currentIndex = index
        var result = ""
        var lastWasTens = false

        // Special handling for sequences like "one twenty three" -> "123"
        var isSequentialDigits = true
        var tempIndex = index

        // Check if this is a sequence of number words that should be concatenated
        while tempIndex < words.count {
            let word = words[tempIndex]
            if singleDigits[word] != nil || teens[word] != nil || tens[word] != nil {
                tempIndex += 1
            } else if scales[word] != nil {
                isSequentialDigits = false
                break
            } else {
                break
            }
        }

        // If it's a sequential digit pattern (like room numbers), concatenate
        if isSequentialDigits, tempIndex > index + 1 {
            while currentIndex < tempIndex {
                let word = words[currentIndex]

                if let digit = singleDigits[word] {
                    result += digit
                    currentIndex += 1
                } else if let teenValue = teens[word] {
                    result += teenValue
                    currentIndex += 1
                } else if let tensValue = tens[word] {
                    result += tensValue
                    currentIndex += 1

                    // Check if next word is a single digit (like "twenty three")
                    if currentIndex < tempIndex,
                       let digit = singleDigits[words[currentIndex]]
                    {
                        // Remove the trailing 0 from tens and add the digit
                        result = String(result.dropLast()) + digit
                        currentIndex += 1
                    }
                }
            }

            if !result.isEmpty {
                return (result, currentIndex - index)
            }
        }

        // Otherwise, fall back to standard number parsing (for calculations)
        currentIndex = index
        var accumulator = 0
        var currentNumber = 0
        var hasNumber = false
        var lastScale = 0

        while currentIndex < words.count {
            let word = words[currentIndex]

            // Check for single digits
            if let digit = singleDigits[word] {
                if lastWasTens {
                    // After tens, add the digit
                    currentNumber += Int(digit)!
                } else {
                    // Otherwise, treat as a new digit position
                    currentNumber = currentNumber * 10 + Int(digit)!
                }
                hasNumber = true
                lastWasTens = false
                currentIndex += 1
                continue
            }

            // Check for teens
            if let teenValue = teens[word] {
                currentNumber = currentNumber * 10 + Int(teenValue)!
                hasNumber = true
                lastWasTens = false
                currentIndex += 1
                continue
            }

            // Check for tens
            if let tensValue = tens[word] {
                currentNumber += Int(tensValue)!
                hasNumber = true
                lastWasTens = true
                currentIndex += 1
                continue
            }

            // Check for scale words
            if let scale = scales[word] {
                if currentNumber == 0, !hasNumber {
                    // Handle cases like "a hundred" or just "hundred"
                    currentNumber = 1
                }

                // Handle compound numbers with multiple scales
                // Example: "three million five hundred thousand" → 3,500,000
                if scale == 100, lastScale >= 1000 {
                    // This is a hundred after thousand/million/billion
                    // Keep building the current number (e.g., "five hundred" in "three million five hundred thousand")
                    currentNumber = currentNumber * scale
                } else if scale == 100, currentNumber >= 10 {
                    // Handle special case "twenty-three hundred" = 2300
                    accumulator += currentNumber * scale
                    currentNumber = 0
                    lastScale = scale
                } else {
                    // Normal scale handling
                    accumulator += currentNumber * scale
                    currentNumber = 0
                    lastScale = scale
                }

                hasNumber = true
                lastWasTens = false
                currentIndex += 1

                // Handle "and" after hundred/thousand
                if currentIndex < words.count, words[currentIndex] == "and" {
                    currentIndex += 1
                }
                continue
            }

            // Check for "a" or "an" before hundred/thousand
            if word == "a" || word == "an",
               currentIndex + 1 < words.count,
               scales[words[currentIndex + 1]] != nil
            {
                currentIndex += 1
                continue
            }

            // If we hit a non-number word, stop parsing
            break
        }

        // Add any remaining current number to accumulator
        accumulator += currentNumber

        if hasNumber {
            return (String(accumulator), currentIndex - index)
        } else {
            return (nil, 0)
        }
    }
}

// MARK: - Linguistic Analysis

public extension SpeechNormalizer {
    /// Extracts key phrases from text using linguistic analysis.
    ///
    /// Uses NLTagger to identify nouns, verbs, and adjectives, then applies
    /// lemmatization to normalize word forms. Falls back to stop-word filtering
    /// when NLTagger is unavailable (e.g., in test environments).
    ///
    /// - Parameter text: The input text to analyze
    /// - Returns: Array of lemmatized key phrases suitable for matching
    ///
    /// Example:
    /// ```swift
    /// let phrases = SpeechNormalizer.extractKeyPhrases("Smoke detectors checked")
    /// // Result: ["smoke", "detector", "check"]
    /// ```
    static func extractKeyPhrases(_ text: String) -> [String] {
        // Check cache first
        let cacheKey = text as NSString
        if let cached = linguisticCache.object(forKey: cacheKey) as? [String] {
            return cached
        }

        var keyPhrases: [String] = []

        // First try NLTagger
        lexicalTagger.string = text.lowercased()

        let options: NLTagger.Options = [.omitPunctuation, .omitWhitespace]
        let tags: [NLTag] = [.noun, .verb, .adjective]

        var foundAnyTags = false
        lexicalTagger.enumerateTags(in: text.startIndex ..< text.endIndex, unit: .word, scheme: .lexicalClass, options: options) { tag, tokenRange in
            let word = String(text[tokenRange])
            // Include tagged words (nouns, verbs, adjectives)
            if let tag = tag, tags.contains(tag) {
                // If word contains a slash, split it and add both parts
                if word.contains("/") {
                    let parts = word.split(separator: "/").map { $0.lowercased() }
                    keyPhrases.append(contentsOf: parts)
                } else {
                    keyPhrases.append(word.lowercased())
                }
                foundAnyTags = true
            }
            // Also include numbers as key phrases, but filter out price/measurement numbers
            else if Int(word) != nil {
                // Use the same filtering logic as OrdinalMatcher to avoid price numbers
                let words = text.lowercased().components(separatedBy: .whitespacesAndNewlines)
                if let wordIndex = words.firstIndex(of: word.lowercased()) {
                    if !OrdinalMatcher.isNonItemNumber(word, in: text, at: wordIndex) {
                        keyPhrases.append(word.lowercased())
                        foundAnyTags = true
                    }
                } else {
                    // Fallback: include the number if we can't find its context
                    keyPhrases.append(word.lowercased())
                    foundAnyTags = true
                }
            }
            return true
        }

        // If NLTagger didn't find anything (e.g., in test environment), use simple fallback
        if !foundAnyTags {
            // Simple word extraction - filter out common stop words
            let stopWords = Set(["the", "is", "are", "was", "were", "been", "be", "have", "has", "had",
                                 "do", "does", "did", "will", "would", "could", "should", "may", "might",
                                 "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with",
                                 "by", "from", "up", "about", "into", "through", "during", "before", "after",
                                 "it", "its", "this", "that", "these", "those", "them", "they", "their"])

            // Split by whitespace and also handle slash-separated words
            let words = text.lowercased().components(separatedBy: .whitespacesAndNewlines)
            var allWords: [String] = []
            for word in words {
                // If word contains a slash, split it and add both parts
                if word.contains("/") {
                    let parts = word.split(separator: "/").map(String.init)
                    allWords.append(contentsOf: parts)
                } else {
                    allWords.append(word)
                }
            }

            keyPhrases = allWords.filter { word in
                if !stopWords.contains(word), !word.isEmpty, word.count > Configuration.minPhraseLength {
                    return true
                } else if Int(word) != nil {
                    // Filter out price/measurement numbers
                    if let wordIndex = words.firstIndex(of: word) {
                        return !OrdinalMatcher.isNonItemNumber(word, in: text, at: wordIndex)
                    }
                    return true // Fallback: include if context unknown
                }
                return false
            }
        }

        // Post-process to handle plurals and normalize similar words
        var processedPhrases: [String] = []
        for phrase in keyPhrases {
            // Don't lemmatize numbers
            if Int(phrase) != nil {
                processedPhrases.append(phrase)
            } else {
                let normalized = lemmatize(phrase)
                processedPhrases.append(normalized)
            }
        }

        let result = Array(Set(processedPhrases))

        // Cache the result
        linguisticCache.setObject(result as NSArray, forKey: cacheKey)

        return result
    }

    /// Extract words with their linguistic tags for more sophisticated analysis
    static func extractWordsWithTags(_ text: String) -> [(word: String, tag: NLTag?)] {
        var taggedWords: [(word: String, tag: NLTag?)] = []

        lexicalTagger.string = text.lowercased()
        let options: NLTagger.Options = [.omitPunctuation, .omitWhitespace]

        lexicalTagger.enumerateTags(in: text.startIndex ..< text.endIndex, unit: .word, scheme: .lexicalClass, options: options) { tag, tokenRange in
            let word = String(text[tokenRange]).lowercased()
            taggedWords.append((word: word, tag: tag))
            return true
        }

        // Fallback if tagger fails
        if taggedWords.isEmpty {
            let words = text.lowercased().components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty }
            taggedWords = words.map { (word: $0, tag: nil) }
        }

        return taggedWords
    }

    /// Helper function to check if a character is a vowel
    /// - Parameter char: The character to check
    /// - Returns: True if the character is a vowel
    private static func isVowel(_ char: Character) -> Bool {
        let vowels: Set<Character> = ["a", "e", "i", "o", "u", "y"]
        return vowels.contains(char.lowercased().first ?? char)
    }

    /// Validates if a stem is reasonable and not over-processed
    /// - Parameter stem: The potential stem to validate
    /// - Returns: True if the stem appears valid
    private static func isValidStem(_ stem: String) -> Bool {
        // Minimum length check - avoid single character stems
        guard stem.count >= 2 else { return false }

        // Should contain only letters
        guard stem.allSatisfy({ $0.isLetter }) else { return false }

        // For shorter stems (2-3 chars), be more permissive
        if stem.count <= 3 {
            return true
        }

        // For longer stems, apply basic validation
        // Allow most combinations but reject obvious over-stemming
        // Reject stems that end with certain problematic patterns
        let problematicEndings: Set<String> = ["qq", "xx", "zz"]
        if stem.count >= 2 {
            let lastTwo = String(stem.suffix(2))
            if problematicEndings.contains(lastTwo) {
                return false
            }
        }

        return true
    }

    /// Checks if a word ending in 'er' is likely a comparative or a standalone word
    /// - Parameter word: The word to check
    /// - Returns: True if it's likely a comparative form
    private static func isLikelyComparative(_ word: String) -> Bool {
        // Check against known non-comparative words
        if commonErWords.contains(word) {
            return false
        }

        // Be very conservative - only consider words that are clearly comparatives
        // For now, let's be more restrictive to avoid breaking existing functionality
        let root = String(word.dropLast(2))

        // Only consider as comparative if:
        // 1. The root is at least 4 characters
        // 2. The word is relatively short (comparative words are usually short)
        // 3. It's not in our common words list
        return root.count >= 4 && word.count <= 8 && isValidStem(root)
    }

    /// Lemmatizes a word to its base form (e.g., "detectors" → "detector", "checked" → "check")
    ///
    /// Uses a multi-layered approach:
    /// 1. NLTagger for sophisticated linguistic analysis
    /// 2. Irregular words dictionary for exceptions
    /// 3. Pattern-based fallback for systematic transformations
    /// 4. Validation to prevent over-stemming
    ///
    /// - Parameter word: The word to lemmatize
    /// - Returns: The lemmatized form of the word
    static func lemmatize(_ word: String) -> String {
        let lowercased = word.lowercased()

        // Early validation - avoid processing very short words or non-words
        guard lowercased.count > 2, lowercased.allSatisfy({ $0.isLetter }) else {
            return lowercased
        }

        // Step 1: Try NLTagger for sophisticated linguistic analysis
        lemmaTagger.string = lowercased
        let tagResult = lemmaTagger.tag(at: lowercased.startIndex, unit: .word, scheme: .lemma)
        if let lemma = tagResult.0?.rawValue,
           !lemma.isEmpty,
           lemma != lowercased, // Only use if result is actually different
           lemma.count >= 2 // Avoid single-character results
        {
            return lemma
        }

        // Step 2: Check irregular words dictionary for common exceptions
        if let irregular = irregularWords[lowercased] {
            return irregular
        }

        // Step 3: Use existing pattern-based fallback with minor improvements
        return applyPatternMatching(to: lowercased)
    }

    /// Applies pattern-based transformations for word lemmatization
    /// - Parameter word: The word to process
    /// - Returns: The lemmatized form based on pattern matching
    private static func applyPatternMatching(to word: String) -> String {
        // Process in order of specificity - most specific patterns first

        // Superlatives (-est)
        if word.hasSuffix("est") && word.count > 5 {
            let stem = String(word.dropLast(3))
            if isValidStem(stem) {
                return stem
            }
        }

        // Comparatives (-er) - with validation to avoid false positives
        if word.hasSuffix("er") && word.count > 4 && isLikelyComparative(word) {
            let stem = String(word.dropLast(2))
            if isValidStem(stem) {
                return stem
            }
        }

        // Adverbs (-ly)
        if word.hasSuffix("ly") && word.count > 4 {
            let stem = String(word.dropLast(2))
            // Handle special cases like "easily" -> "easy"
            if stem.hasSuffix("i") && word.count > 5 {
                let adjustedStem = String(stem.dropLast()) + "y"
                if isValidStem(adjustedStem) {
                    return adjustedStem
                }
            }
            if isValidStem(stem) {
                return stem
            }
        }

        // Past tense (-ed) - improved handling
        if word.hasSuffix("ed") && word.count > 4 {
            // Handle -ied -> -y (worried -> worry, carried -> carry)
            if word.hasSuffix("ied") && word.count > 5 {
                let stem = String(word.dropLast(3)) + "y"
                if isValidStem(stem) {
                    return stem
                }
            }

            // Handle doubled consonants (stopped -> stop, planned -> plan)
            if word.count > 5 {
                let beforeEd = String(word.dropLast(2))
                if beforeEd.count >= 2 {
                    let lastChar = beforeEd.last!
                    let secondLastChar = beforeEd[beforeEd.index(beforeEd.endIndex, offsetBy: -2)]

                    // If last two characters are the same consonant, remove one
                    if lastChar == secondLastChar && !isVowel(lastChar) {
                        let stem = String(beforeEd.dropLast())
                        if isValidStem(stem) {
                            return stem
                        }
                    }
                }
            }

            // Regular -ed removal
            let stem = String(word.dropLast(2))
            if isValidStem(stem) {
                return stem
            }
        }

        // Gerunds (-ing) - improved handling
        if word.hasSuffix("ing") && word.count > 5 {
            let baseStem = String(word.dropLast(3))

            // Handle doubled consonants (running -> run, getting -> get)
            if baseStem.count >= 2 {
                let lastChar = baseStem.last!
                let secondLastChar = baseStem[baseStem.index(baseStem.endIndex, offsetBy: -2)]

                if lastChar == secondLastChar && !isVowel(lastChar) {
                    let stem = String(baseStem.dropLast())
                    if isValidStem(stem) {
                        return stem
                    }
                }
            }

            // Regular -ing removal - try this first
            if isValidStem(baseStem) {
                return baseStem
            }

            // Handle -e dropping (making -> make, using -> use)
            // Only try adding 'e' if the base stem is invalid and ends with a consonant
            if baseStem.count >= 3 && !isVowel(baseStem.last!) {
                let stemWithE = baseStem + "e"
                if isValidStem(stemWithE) {
                    return stemWithE
                }
            }
        }

        // Plurals (-s, -es, -ies) - keep existing logic but add validation
        if word.hasSuffix("s") && word.count > 3 {
            if word.hasSuffix("ies") && word.count > 4 {
                let stem = String(word.dropLast(3)) + "y"
                if isValidStem(stem) {
                    return stem
                }
            } else if word.hasSuffix("es") && word.count > 3 {
                // Check for special cases like "gases" -> "gas"
                let stem = String(word.dropLast(2))
                if stem.hasSuffix("s") || stem.hasSuffix("x") || stem.hasSuffix("z") ||
                    stem.hasSuffix("ch") || stem.hasSuffix("sh")
                {
                    if isValidStem(stem) {
                        return stem
                    }
                }
                let singleSStem = String(word.dropLast())
                if isValidStem(singleSStem) {
                    return singleSStem
                }
            } else if !word.hasSuffix("ss") && !word.hasSuffix("us") {
                let stem = String(word.dropLast())
                if isValidStem(stem) {
                    return stem
                }
            }
        }

        // If no patterns match, return the original word
        return word
    }

    /// Extracts n-grams from text for partial matching.
    ///
    /// N-grams are consecutive word sequences useful for finding partial matches
    /// in longer texts. For example, bigrams (n=2) of "fire alarm panel" are
    /// ["fire alarm", "alarm panel"].
    ///
    /// - Parameters:
    ///   - text: The input text
    ///   - n: The size of n-grams (default: 2 for bigrams)
    /// - Returns: Array of n-gram strings
    ///
    /// Example:
    /// ```swift
    /// let bigrams = SpeechNormalizer.extractNGrams("smoke detector alarm", n: 2)
    /// // Result: ["smoke detector", "detector alarm"]
    /// ```
    static func extractNGrams(_ text: String, n: Int = 2) -> [String] {
        let words = text.lowercased().components(separatedBy: .whitespacesAndNewlines).filter { !$0.isEmpty }
        guard words.count >= n else { return [text.lowercased()] }

        var ngrams: [String] = []
        for i in 0 ... (words.count - n) {
            let ngram = words[i ..< (i + n)].joined(separator: " ")
            ngrams.append(ngram)
        }

        return ngrams
    }
}

// MARK: - Similarity Algorithms

public extension SpeechNormalizer {
    /// Calculates the Levenshtein distance between two strings
    /// - Parameters:
    ///   - source: The source string
    ///   - target: The target string
    /// - Returns: The Levenshtein distance (number of edits needed)
    static func levenshteinDistance(_ source: String, _ target: String) -> Int {
        let sourceChars = Array(source.lowercased())
        let targetChars = Array(target.lowercased())

        let sourceLength = sourceChars.count
        let targetLength = targetChars.count

        // Optimization for empty strings
        if sourceLength == 0 { return targetLength }
        if targetLength == 0 { return sourceLength }

        // Optimization for equal strings
        if source.lowercased() == target.lowercased() { return 0 }

        var matrix = Array(repeating: Array(repeating: 0, count: targetLength + 1), count: sourceLength + 1)

        // Initialize first column and row
        for i in 0 ... sourceLength {
            matrix[i][0] = i
        }
        for j in 0 ... targetLength {
            matrix[0][j] = j
        }

        // Calculate distances
        for i in 1 ... sourceLength {
            for j in 1 ... targetLength {
                let cost = sourceChars[i - 1] == targetChars[j - 1] ? 0 : 1
                matrix[i][j] = min(
                    matrix[i - 1][j] + 1, // deletion
                    matrix[i][j - 1] + 1, // insertion
                    matrix[i - 1][j - 1] + cost // substitution
                )
            }
        }

        return matrix[sourceLength][targetLength]
    }

    /// Calculates a similarity score between two strings (0-1, where 1 is identical)
    /// - Parameters:
    ///   - source: The source string
    ///   - target: The target string
    /// - Returns: Similarity score between 0 and 1
    static func similarityScore(_ source: String, _ target: String) -> Double {
        let distance = levenshteinDistance(source, target)
        let maxLength = max(source.count, target.count)
        guard maxLength > 0 else { return 1.0 }
        return 1.0 - (Double(distance) / Double(maxLength))
    }

    /// Calculates phonetic similarity between two strings using Soundex algorithm.
    ///
    /// Useful for matching words that sound similar but are spelled differently,
    /// common in speech recognition scenarios (e.g., "check" vs "cheque").
    ///
    /// - Parameters:
    ///   - source: The source string
    ///   - target: The target string
    /// - Returns: Phonetic similarity score between 0 and 1 (1.0 = phonetically identical)
    ///
    /// Example:
    /// ```swift
    /// let score = SpeechNormalizer.phoneticSimilarity("fire", "fyre")
    /// // Result: > 0.8 (high similarity)
    /// ```
    static func phoneticSimilarity(_ source: String, _ target: String) -> Double {
        let sourcePhonetic = soundex(source)
        let targetPhonetic = soundex(target)
        print("               🔊 Phonetic: '\(source)' → '\(sourcePhonetic)', '\(target)' → '\(targetPhonetic)'")
        return sourcePhonetic == targetPhonetic ? 1.0 : similarityScore(sourcePhonetic, targetPhonetic)
    }

    /// Implements Soundex algorithm for phonetic matching.
    ///
    /// Converts words to a 4-character phonetic code where similar-sounding
    /// consonants are grouped together. Vowels are ignored except for the
    /// first letter.
    ///
    /// - Parameter word: The word to encode
    /// - Returns: 4-character Soundex code (e.g., "Robert" → "R163")
    private static func soundex(_ word: String) -> String {
        let word = word.uppercased()
        guard !word.isEmpty else { return "" }

        var soundex = String(word.first!)

        let mappings: [Character: Character] = [
            "B": "1", "F": "1", "P": "1", "V": "1",
            "C": "2", "G": "2", "J": "2", "K": "2", "Q": "2", "S": "2", "X": "2", "Z": "2",
            "D": "3", "T": "3",
            "L": "4",
            "M": "5", "N": "5",
            "R": "6",
        ]

        var lastCode: Character = "0"

        for char in word.dropFirst() {
            if let code = mappings[char], code != lastCode {
                soundex.append(code)
                lastCode = code
                if soundex.count == 4 { break }
            } else if !mappings.keys.contains(char) && "AEIOUYHW".contains(char) {
                lastCode = "0"
            }
        }

        while soundex.count < 4 {
            soundex.append("0")
        }

        return String(soundex.prefix(4))
    }
}

// MARK: - Matching Algorithms

public extension SpeechNormalizer {
    /// Matches spoken text to a target item name using linguistic analysis and fuzzy matching.
    ///
    /// Combines multiple matching strategies:
    /// - Key phrase matching with lemmatization
    /// - Phonetic similarity for sound-alike words
    /// - Substring matching with length penalty
    /// - Word order independence for flexible matching
    ///
    /// - Parameters:
    ///   - spokenText: The text from speech recognition
    ///   - itemName: The target item name to match against
    /// - Returns: A match score between 0 and 1 (higher is better)
    ///
    /// Example:
    /// ```swift
    /// let score = SpeechNormalizer.matchScore(
    ///     spokenText: "smoke detectors checked",
    ///     itemName: "Smoke Detector"
    /// )
    /// // Result: > 0.9 (high match)
    /// ```
    static func matchScore(spokenText: String, itemName: String, configuration: DomainKeywordConfiguration? = nil) -> Double {
        print("\n      🔬 DEBUG: matchScore")
        print("         Spoken: '\(spokenText)'")
        print("         Item: '\(itemName)'")

        // Normalize both texts for comparison
        let spokenLower = spokenText.lowercased()
        let itemLower = itemName.lowercased()

        // Quick check for exact match (case-insensitive)
        if spokenLower == itemLower {
            print("         ✅ Exact match! Score: 1.0")
            return 1.0
        }

        // Check for phonetic equivalence of the full phrases
        let spokenWordList = spokenLower.split(separator: " ").map(String.init)
        let itemWordList = itemLower.split(separator: " ").map(String.init)

        if spokenWordList.count == itemWordList.count {
            var allWordsPhonetic = true
            for i in 0 ..< spokenWordList.count {
                let spokenSoundex = soundex(spokenWordList[i])
                let itemSoundex = soundex(itemWordList[i])
                if spokenSoundex != itemSoundex {
                    allWordsPhonetic = false
                    break
                }
                print("         🔊 Word phonetic match: '\(spokenWordList[i])' [\(spokenSoundex)] vs '\(itemWordList[i])' [\(itemSoundex)]")
            }

            if allWordsPhonetic {
                print("         ✅ All words phonetically match! Score: 0.95")
                return 0.95
            }
        }

        // Early check: if spoken text has numbers but item doesn't (or vice versa), reduce score
        // Use context-aware number extraction to avoid counting price/measurement numbers
        let spokenHasNumber = OrdinalMatcher.extractFirstItemNumber(from: spokenLower) != nil
        let itemHasNumber = OrdinalMatcher.extractFirstItemNumber(from: itemLower) != nil

        // Penalty for number mismatch
        let numberMismatchPenalty: Double = (spokenHasNumber != itemHasNumber) ? 0.7 : 1.0

        // Create compound word variations for both texts
        let compoundVariations = createCompoundWordVariations(spokenLower, configuration: configuration)
        let itemCompoundVariations = createCompoundWordVariations(itemLower, configuration: configuration)

        // Check if any compound variation matches better
        var bestCompoundScore = 0.0
        for spokenVariation in compoundVariations {
            for itemVariation in itemCompoundVariations {
                if spokenVariation == itemVariation {
                    bestCompoundScore = 1.0
                    break
                }
                // Check substring match with variations
                if spokenVariation.contains(itemVariation) {
                    let ratio = Double(itemVariation.count) / Double(spokenVariation.count)
                    bestCompoundScore = max(bestCompoundScore, ratio)
                }
            }
        }

        // If we have a perfect compound match, return high score
        if bestCompoundScore >= 0.9 {
            return bestCompoundScore
        }

        // Extract key phrases from both texts
        let spokenPhrases = extractKeyPhrases(spokenText)
        let itemPhrases = extractKeyPhrases(itemName)
        print("         Spoken phrases: \(spokenPhrases)")
        print("         Item phrases: \(itemPhrases)")

        // Also include the normalized versions
        let normalizedSpoken = normalize(spokenText).lowercased()
        let normalizedItem = normalize(itemName).lowercased()

        var totalScore = 0.0
        var matchCount = 0

        // Compare each item phrase with spoken phrases
        for itemPhrase in itemPhrases {
            let itemLemma = lemmatize(itemPhrase)
            var bestMatch = 0.0
            print("         📝 Checking item phrase: '\(itemPhrase)' → lemma: '\(itemLemma)'")

            // Find best match in spoken phrases
            for spokenPhrase in spokenPhrases {
                let spokenLemma = lemmatize(spokenPhrase)
                print("            vs spoken phrase: '\(spokenPhrase)' → lemma: '\(spokenLemma)'")

                // Check exact match of lemmas
                if itemLemma == spokenLemma {
                    bestMatch = 1.0
                    print("            ✅ Exact lemma match! Score: 1.0")
                } else {
                    // Calculate similarity score with phonetic matching
                    let textSimilarity = similarityScore(itemLemma, spokenLemma)
                    let phoneticScore = phoneticSimilarity(itemLemma, spokenLemma)
                    let combinedScore = textSimilarity * (1 - Configuration.phoneticMatchWeight) +
                        phoneticScore * Configuration.phoneticMatchWeight
                    bestMatch = max(bestMatch, combinedScore)
                    print("            📊 Similarity - Text: \(textSimilarity), Phonetic: \(phoneticScore), Combined: \(combinedScore)")
                }
            }

            totalScore += bestMatch
            matchCount += 1
            print("         Best match for '\(itemPhrase)': \(bestMatch)")
        }

        // NEW: Simple word matching for better accuracy on short phrases
        let spokenWordsLemmatized = spokenLower.split(separator: " ").map { lemmatize(String($0)) }.filter { $0.count >= 3 }
        let itemWordsLemmatized = itemLower.split(separator: " ").map { lemmatize(String($0)) }.filter { $0.count >= 3 }

        // Check for significant word matches
        var significantMatches = 0
        let totalPossibleMatches = spokenWordsLemmatized.count

        for spokenWord in spokenWordsLemmatized {
            for itemWord in itemWordsLemmatized {
                if spokenWord == itemWord {
                    significantMatches += 1
                    break
                } else if spokenWord.count >= 4 && itemWord.count >= 4 {
                    // Check if one word is a prefix/suffix variant of the other
                    // But avoid matching very short prefixes like "kit" with "kitchen"
                    let minPrefixLength = min(5, itemWord.count - 1) // Require at least 5 chars for prefix match
                    if spokenWord.count >= minPrefixLength {
                        if (spokenWord.hasPrefix(itemWord) && spokenWord.count <= itemWord.count + 3) ||
                            (itemWord.hasPrefix(spokenWord) && itemWord.count <= spokenWord.count + 3)
                        {
                            significantMatches += 1
                            break
                        }
                    }
                    // Check for common lemma forms
                    let spokenLemma = lemmatize(spokenWord)
                    let itemLemma = lemmatize(itemWord)
                    if spokenLemma == itemLemma && spokenLemma != spokenWord {
                        significantMatches += 1
                        break
                    }
                }
            }
        }

        // If most spoken words match item words, give high score
        if significantMatches > 0 && totalPossibleMatches > 0 {
            let matchRatio = Double(significantMatches) / Double(totalPossibleMatches)
            if matchRatio >= 0.4 {
                // Strong match - most of the spoken words are found in item
                // Boost score more aggressively for good word coverage
                totalScore = max(totalScore, matchRatio * 1.1)
                if matchCount == 0 {
                    matchCount = 1
                }
            }
        }

        // Check if all item words appear in spoken text (order doesn't matter)
        // Clean punctuation from words for better matching
        let cleanWord: (String) -> String = { word in
            word.trimmingCharacters(in: CharacterSet.punctuationCharacters.union(.symbols))
        }
        let itemWords = itemLower.split(separator: " ")
            .map { cleanWord(String($0)) }
            .filter { !$0.isEmpty && $0 != "-" }
        let spokenWords = Set(spokenLower.split(separator: " ")
            .map { cleanWord(String($0)) })
        let allWordsPresent = itemWords.allSatisfy { spokenWords.contains($0) }

        // Also check if the item name appears as a substring in the spoken text
        // But reduce bonus if spoken text is much longer (suggests item is only partial match)
        let spokenWordArray = spokenLower.split(separator: " ")
            .map { cleanWord(String($0)) }
            .filter { !$0.isEmpty && $0 != "-" }
        let itemWordArray = itemLower.split(separator: " ")
            .map { cleanWord(String($0)) }
            .filter { !$0.isEmpty && $0 != "-" }
        let spokenWordCount = spokenWordArray.count
        let itemWordCount = itemWordArray.count
        let lengthRatio = Double(itemWordCount) / Double(spokenWordCount)
        let lengthPenalty = lengthRatio < 0.3 ? 0.4 : 1.0 // Reduce bonus for very short items in long speech

        // Count how many spoken words match item words
        var matchedSpokenWordCount = 0
        for spokenWord in spokenWordArray {
            if itemWordArray.contains(where: { $0 == spokenWord }) {
                matchedSpokenWordCount += 1
            }
        }

        // Calculate coverage - how much of the item is covered by spoken words
        let itemCoverage = itemWordArray.filter { word in spokenWords.contains(word) }.count
        let coverageRatio = Double(itemCoverage) / Double(itemWordCount)

        // NEW: Calculate specificity bonus - reward longer items that have more matching words
        // This helps "Emergency Exit Sign - Illuminated" beat "Emergency Exit Sign" when spoken text includes "illuminated"
        let specificityBonus: Double

        // Count how many item words are also in the spoken text
        let matchingWordCount = itemWordArray.filter { word in
            spokenWords.contains(word) || spokenWords.contains(where: {
                $0.hasPrefix(word) && $0.count <= word.count + 3
            })
        }.count

        // Calculate match ratio for both directions
        let itemToSpokenRatio = Double(matchingWordCount) / Double(itemWordCount)
        let spokenToItemRatio = Double(matchingWordCount) / Double(spokenWordCount)

        // NEW: Check if this item has all words from a shorter variant plus additional words
        // This helps "Emergency Exit Sign - Illuminated" beat "Emergency Exit Sign"
        var hasAdditionalMatchingWords = false
        if itemWordCount >= 4 && matchingWordCount == itemWordCount {
            // Check if spoken text contains extra words that match this longer item
            let extraItemWords = itemWordArray.filter { word in
                // Skip common words like "the", "a", etc.
                word.count > 2 && !["and", "the", "for", "are", "was", "is"].contains(word)
            }

            // If all meaningful words from the item are in the spoken text, this is a perfect match
            let meaningfulMatches = extraItemWords.filter { word in spokenWords.contains(word) }.count
            if meaningfulMatches == extraItemWords.count {
                hasAdditionalMatchingWords = true
            }
        }

        // Special case: if all item words are in spoken text AND item has 4+ words,
        // give substantial bonus to prefer the more specific match
        if hasAdditionalMatchingWords {
            // Extremely strong bonus for items that match ALL their words including extras
            // This ensures "Emergency Exit Sign - Illuminated" beats shorter variants
            specificityBonus = 1.5 + (Double(itemWordCount) * 0.2)
        } else if itemToSpokenRatio >= 1.0 && itemWordCount >= 4 {
            // Very strong bonus for complete matches with 4+ words
            specificityBonus = 1.0 + (Double(itemWordCount) * 0.15)
        } else if itemToSpokenRatio >= 1.0 && itemWordCount >= 3 {
            // Strong bonus for complete matches with 3 words
            specificityBonus = 0.6 + (Double(itemWordCount) * 0.1)
        } else if matchingWordCount >= 3 && itemWordCount >= 3 {
            // Good bonus for items with many matching words
            let combinedRatio = (itemToSpokenRatio + spokenToItemRatio) / 2.0
            specificityBonus = combinedRatio * 0.5 * (1.0 + Double(itemWordCount) / 8.0)
        } else if matchingWordCount >= 2 && itemToSpokenRatio >= 0.66 {
            // Moderate bonus for good partial matches
            specificityBonus = itemToSpokenRatio * 0.3 * (1.0 + Double(matchingWordCount) / 8.0)
        } else {
            specificityBonus = 0.0
        }

        // Special handling for cases where spoken text contains extra words beyond item name
        let unmatchedItemWords = itemWordArray.filter { word in
            !spokenWords.contains(word)
        }
        let unmatchedRatio = Double(unmatchedItemWords.count) / Double(itemWordCount)

        let substringBonus: Double
        // For hierarchical items, reduce substring bonus for shorter items when longer variants exist
        let isHierarchicalCase = itemWordCount < 3 && spokenWordCount > 5
        
        if spokenLower.contains(itemLower) {
            // Perfect substring match - give VERY HIGH bonus as per user requirement
            // This is the highest priority match type
            print("            ✅ EXACT SUBSTRING MATCH FOUND!")
            
            // Check if any unmatched word is a number (for potential penalty)
            let hasUnmatchedNumber = spokenWordArray.contains { word in
                !itemWordArray.contains(word) && (Int(word) != nil || ["1st", "2nd", "3rd", "4th", "5th", "6th", "7th", "8th", "9th", "10th"].contains(word))
            }
            
            // For exact substring matches, give very high bonus (0.8-0.95)
            // But reduce bonus for very short items in long spoken text (hierarchical case)
            if isHierarchicalCase {
                // Short item in long text - reduce substring bonus to allow longer matches to compete
                substringBonus = hasUnmatchedNumber ? 0.4 : 0.5
            } else if hasUnmatchedNumber {
                substringBonus = 0.8  // Still very high even with unmatched number
            } else {
                substringBonus = 0.95  // Maximum bonus for perfect substring match
            }
        } else if allWordsPresent {
            // All item words present but not as exact substring
            // Give bonus based on coverage but apply gentler penalty for unmatched item words
            // Boost this case significantly since all item words are present
            // NEW: Reduce impact of length penalty when all words are present
            let adjustedLengthPenalty = lengthRatio < 0.3 ? 0.7 : 1.0 // Less harsh penalty

            // If spoken text has significant extra words beyond this item,
            // reduce the bonus to allow more specific variants to win
            let spokenHasExtraDescriptiveWords = spokenWordArray.count > itemWordArray.count + 2 &&
                spokenWordArray.contains { word in
                    !itemWordArray.contains(word) && word.count >= 4 &&
                        !["and", "the", "with", "for", "are", "was", "were", "been", "have", "has", "had", "is", "it"].contains(word)
                }

            // Special case: Short spoken phrases (2-3 words) that all match should get high scores
            // This handles cases like "mold exists" matching "No visible mold or biological growth exists?"
            if spokenWordCount <= 3 && allWordsPresent {
                // All spoken words found - give moderate score even with low coverage
                substringBonus = 0.4 * adjustedLengthPenalty
            } else if spokenHasExtraDescriptiveWords {
                // Reduce score to allow more specific variants to win
                substringBonus = 0.3 * adjustedLengthPenalty * coverageRatio
            } else {
                substringBonus = 0.35 * adjustedLengthPenalty * coverageRatio
            }
        } else if normalizedSpoken.contains(normalizedItem) {
            substringBonus = 0.25 * lengthPenalty * coverageRatio * (1.0 - unmatchedRatio * 0.2)
        } else if normalizedItem.split(separator: " ").allSatisfy({ word in
            normalizedSpoken.contains(String(word))
        }) {
            substringBonus = 0.2 * lengthPenalty * coverageRatio * (1.0 - unmatchedRatio * 0.2)
        } else if coverageRatio >= 0.5 {
            // NEW: If at least half the item words are covered, give partial credit
            // Increase bonus for better coverage in partial matches
            substringBonus = 0.15 * lengthPenalty * coverageRatio
        } else if itemLower.hasPrefix(spokenLower) && spokenLower.count >= 3 {
            // Handle prefix matches (like "kit" matching "kitchen")
            let prefixRatio = Double(spokenLower.count) / Double(itemLower.count)
            substringBonus = 0.2 + prefixRatio * 0.15 // Reduced bonus for prefix matches
        } else {
            substringBonus = 0.0
        }

        // Apply compound word bonus if we found a good match
        let compoundBonus = bestCompoundScore * 0.2

        // Calculate final score - heavily prioritize phrase matching over substring matching
        let phraseScore = matchCount > 0 ? totalScore / Double(matchCount) : 0.0

        // Boost phrase score based on quality and match count
        let boostedPhraseScore: Double
        if phraseScore >= 1.0 {
            // Perfect matches get significant boost
            boostedPhraseScore = phraseScore * 1.2
        } else if phraseScore > 0.8 {
            // Very good matches get moderate boost
            boostedPhraseScore = phraseScore * 1.1
        } else {
            boostedPhraseScore = phraseScore
        }

        // Add partial match bonus for prefix matching
        let partialMatchBonus = calculatePartialMatchBonus(spokenText: spokenLower, itemName: itemLower)

        // Check for number mismatch penalty
        let spokenNumber = OrdinalMatcher.extractFirstNumberIncludingWritten(from: spokenLower)
        let itemNumber = OrdinalMatcher.extractFirstNumberIncludingWritten(from: itemLower)
        var numberPenalty = 0.0

        // If both have numbers but they don't match, apply penalty
        if let sNum = spokenNumber, let iNum = itemNumber, sNum != iNum {
            // Calculate text similarity without numbers to determine penalty severity
            let spokenWithoutNumbers = spokenLower.replacingOccurrences(of: sNum, with: "").trimmingCharacters(in: .whitespacesAndNewlines)
            let itemWithoutNumbers = itemLower.replacingOccurrences(of: iNum, with: "").trimmingCharacters(in: .whitespacesAndNewlines)

            // If the text parts (without numbers) match very well, reduce the penalty
            if spokenWithoutNumbers.lowercased() == itemWithoutNumbers.lowercased() {
                // Perfect text match - only light penalty for number mismatch
                numberPenalty = 0.15
            } else if boostedPhraseScore > 0.9 {
                // Very high phrase similarity - moderate penalty
                numberPenalty = 0.25
            } else {
                // Standard heavy penalty for significant number mismatch
                numberPenalty = 0.5
            }
        }

        print("         📊 Final score calculation:")
        print("            - Phrase score: \(phraseScore) → boosted: \(boostedPhraseScore)")
        print("            - Substring bonus: \(substringBonus)")
        print("            - Compound bonus: \(compoundBonus)")
        print("            - Partial match bonus: \(partialMatchBonus)")
        print("            - Specificity bonus: \(specificityBonus)")
        print("            - Number penalty: \(numberPenalty)")
        print("            - Number mismatch penalty: \(numberMismatchPenalty)")

        // Weighted final score with compound and partial match bonuses
        var finalScore = (boostedPhraseScore * Configuration.keyPhraseMatchWeight +
            substringBonus * Configuration.substringMatchWeight +
            compoundBonus +
            partialMatchBonus +
            specificityBonus) * (1.0 - numberPenalty) * numberMismatchPenalty

        print("            - Raw final score: \(finalScore)")

        // Minimum score guarantee for short phrases where all words are found
        // This ensures cases like "signage damage" matching "Signage is secure, with no damage?" pass the threshold
        if spokenWordCount <= 3 && allWordsPresent && finalScore < 0.55 {
            finalScore = 0.55
            print("            - Applied minimum score guarantee (all words present)")
        }

        // Additional minimum score guarantee for complex matches with good coverage
        // This helps hierarchical matching where spoken phrases have many extra words
        if coverageRatio >= 0.6 && boostedPhraseScore >= 0.5 && finalScore < 0.45 {
            finalScore = 0.45
            print("            - Applied minimum score guarantee (good coverage)")
        }

        // NEW: When common phrases match half or more of item words AND there's good semantic alignment
        // This should apply when the matched phrases represent a significant portion of the item
        // but only when the phrase matching shows meaningful semantic connection
        if coverageRatio >= 0.5 && phraseScore >= 0.5 && finalScore < 0.7 {
            // Additional check: ensure this isn't just a number match causing high coverage
            let hasSignificantTextMatch = significantMatches > 0 || phraseScore > 0.6
            if hasSignificantTextMatch {
                finalScore = 0.7
                print("            - Applied minimum score guarantee (50%+ meaningful coverage → score >= 0.7)")
            }
        }

        // PRIORITY OVERRIDE: Exact substring matches should always score very high
        // This implements the user's requirement: substring contained > common phrases all contained > part contained
        if spokenLower.contains(itemLower) {
            // Ensure substring matches always score at least 0.85, typically higher
            if finalScore < 0.85 {
                finalScore = 0.85
                print("            - SUBSTRING PRIORITY OVERRIDE: Boosted score to \(finalScore)")
            }
        }

        print("         ✅ Final matchScore: \(finalScore)")
        return finalScore
    }

    /// Creates compound word variations by detecting adjacent words that might form compounds
    /// Uses configuration-based approach with optional hardcoded mappings
    private static func createCompoundWordVariations(_ text: String, configuration: DomainKeywordConfiguration? = nil) -> [String] {
        var variations = [text]
        let words = text.split(separator: " ").map(String.init)

        guard words.count >= 2 else { return variations }

        // Only apply compound word logic if configuration is explicitly provided
        guard let config = configuration else { return variations }
        _ = config.compoundWords

        // Check for known compounds first
        for i in 0 ..< (words.count - 1) {
            let word1 = words[i].lowercased()
            let word2 = words[i + 1].lowercased()

            // Check if this is a known compound using configuration
            if config.isCompound(word1, word2) {
                // Create variation with compound
                var compoundWords = words
                compoundWords[i] = words[i] + words[i + 1]
                compoundWords.remove(at: i + 1)
                let compoundVariation = compoundWords.joined(separator: " ")
                variations.append(compoundVariation)
            }
        }

        // Also check for potential compound words that should be split
        for (index, word) in words.enumerated() {
            // Check known compounds for splitting using configuration
            if let splits = config.splitCompound(word) {
                var splitWords = words
                splitWords[index] = splits.0
                splitWords.insert(splits.1, at: index + 1)
                let splitVariation = splitWords.joined(separator: " ")
                if !variations.contains(splitVariation) {
                    variations.append(splitVariation)
                }
            }
        }

        return variations
    }

    /// Determines if two adjacent words could form a compound word
    private static func couldBeCompoundWord(word1: String, word2: String) -> Bool {
        // 1. Check if the combined word length is reasonable for a compound
        let combined = word1 + word2
        guard combined.count >= 6 && combined.count <= 20 else { return false }

        // 2. Use linguistic analysis to determine if word2 could be a suffix
        // Check if word2 is a common English word that forms compounds
        if word2.count >= 3 && word2.count <= 8 && isLikelyWord(word2) {
            // Most 3-8 letter words can potentially form compounds
            return true
        }

        // 3. Check if first word appears to have derivational suffixes that make compounding unlikely
        // Use morphological analysis to detect these patterns
        if hasDerivationalSuffix(word1) {
            return false
        }

        // 4. Check if first word could reasonably modify the second
        if word1.count >= 3 && isLikelyWord(word1) {
            return true
        }

        return false
    }

    /// Attempts to split a potential compound word using morphological analysis
    private static func splitPotentialCompound(_ word: String) -> (String, String)? {
        // Only consider words of reasonable length
        guard word.count >= 6, word.count <= 20 else { return nil }

        // Try to find natural split points based on morphology
        if let splitPoint = findNaturalSplitPoint(in: word) {
            let part1 = String(word.prefix(splitPoint))
            let part2 = String(word.suffix(word.count - splitPoint))
            if isLikelyWord(part1), isLikelyWord(part2) {
                return (part1, part2)
            }
        }

        return nil
    }

    /// Checks if a word has derivational suffixes that make compounding unlikely
    private static func hasDerivationalSuffix(_ word: String) -> Bool {
        let chars = Array(word.lowercased())
        let length = chars.count

        // Check for common suffix patterns without hardcoding specific suffixes
        // Look for patterns: consonant + "ing", consonant + vowel + "r", etc.
        if length >= 4 {
            let _ = String(chars.suffix(3))
            let _ = String(chars.suffix(2))

            // Use pattern analysis instead of hardcoded suffixes
            // Check if the last 2-3 characters form a common suffix pattern
            if length >= 5 {
                let stem = String(chars.prefix(length - 2))
                let suffix = String(chars.suffix(2))

                // If removing 2 chars gives a valid word, it's likely a suffix
                if isLikelyWord(stem) && suffix.allSatisfy({ $0.isLetter }) {
                    return true
                }
            }

            if length >= 6 {
                let stem = String(chars.prefix(length - 3))
                let suffix = String(chars.suffix(3))

                // If removing 3 chars gives a valid word, it's likely a suffix
                if isLikelyWord(stem) && suffix.allSatisfy({ $0.isLetter }) {
                    return true
                }
            }
        }

        return false
    }

    /// Checks if a string is likely to be a valid English word
    private static func isLikelyWord(_ text: String) -> Bool {
        // Basic validation
        guard text.count >= 3 else { return false }
        guard text.allSatisfy({ $0.isLetter }) else { return false }

        // Check for reasonable vowel/consonant distribution
        let vowelCount = text.lowercased().filter { isVowel($0) }.count

        // Most English words have at least one vowel and reasonable distribution
        guard vowelCount > 0 else { return false }
        guard Double(vowelCount) / Double(text.count) >= 0.2 else { return false }

        return true
    }

    /// Finds a natural split point in a potential compound word
    private static func findNaturalSplitPoint(in word: String) -> Int? {
        // Look for doubled consonants as potential split points
        let chars = Array(word.lowercased())
        for i in 3 ..< (chars.count - 3) { // Ensure both parts have minimum length
            if chars[i] == chars[i - 1], !isVowel(chars[i]) {
                return i
            }
        }

        // Look for vowel-consonant-consonant patterns
        for i in 3 ..< (chars.count - 3) {
            if i >= 2, i < chars.count - 1 {
                if isVowel(chars[i - 1]), !isVowel(chars[i]), !isVowel(chars[i + 1]) {
                    return i + 1
                }
            }
        }

        return nil
    }

    /// Calculates a partial match bonus for substring matches
    private static func calculatePartialMatchBonus(spokenText: String, itemName: String) -> Double {
        // Check if item name is a prefix of spoken text
        if spokenText.hasPrefix(itemName) {
            return OrdinalMatcher.ScoringConstants.partialMatchBonus
        }

        // Check if spoken text is a prefix of item name (more common case)
        if itemName.hasPrefix(spokenText) && spokenText.count >= 3 {
            // Give a stronger bonus for clear prefix matches
            let ratio = Double(spokenText.count) / Double(itemName.count)
            return OrdinalMatcher.ScoringConstants.partialMatchBonus * (0.8 + ratio * 0.4)
        }

        // Check if any word in item name is the start of spoken text
        let itemWords = itemName.split(separator: " ").map { lemmatize(String($0)) }
        let spokenWords = spokenText.split(separator: " ").map { lemmatize(String($0)) }

        // Check first word match
        if let spokenStart = spokenWords.first {
            for itemWord in itemWords {
                if spokenStart == itemWord && itemWord.count >= 3 {
                    return OrdinalMatcher.ScoringConstants.partialMatchBonus * 0.8
                } else if spokenStart.hasPrefix(itemWord) && itemWord.count >= 4 {
                    return OrdinalMatcher.ScoringConstants.partialMatchBonus * 0.5
                }
            }
        }

        // NEW: Check if significant words from item appear at start of spoken text
        if spokenWords.count >= 2 && itemWords.count >= 2 {
            // Check if first two spoken words match any two consecutive item words
            let firstTwoSpoken = spokenWords.prefix(2).joined(separator: " ")
            for i in 0 ..< max(1, itemWords.count - 1) {
                let twoItemWords = itemWords[i ..< min(i + 2, itemWords.count)].joined(separator: " ")
                if firstTwoSpoken == twoItemWords {
                    return OrdinalMatcher.ScoringConstants.partialMatchBonus * 0.7
                }
            }
        }

        return 0.0
    }

    /// Finds the best matching item using the most appropriate algorithm for the current platform.
    ///
    /// This function automatically selects the best available matching approach:
    /// - **iOS 17+**: Uses advanced multi-sentence processing with semantic embeddings
    /// - **iOS 16 and earlier**: Falls back to traditional string matching with linguistic analysis
    ///
    /// The multi-sentence approach:
    /// - Segments complex spoken text into meaningful phrases
    /// - Uses NLEmbedding for semantic similarity understanding
    /// - Applies contextual boosting for better accuracy
    /// - Handles coordinating conjunctions ("and", "also", "then")
    ///
    /// The phrase-based approach:
    /// - Extracts key phrases using NLTagger
    /// - Calculates string similarity with fuzzy matching
    /// - Applies bonuses for matching key phrases and specificity
    ///
    /// - Parameters:
    ///   - spokenText: The text from speech recognition
    ///   - items: Array of item names to match against
    ///   - threshold: Minimum score threshold (default: 0.7)
    /// - Returns: Tuple of (index, score) for the best match, or nil if no match above threshold
    ///
    /// Example:
    /// ```swift
    /// let items = ["Fire Extinguisher", "Smoke Detector", "Exit Signs"]
    /// if let match = SpeechNormalizer.findBestMatch(
    ///     spokenText: "smoke detector checked the condition is good",
    ///     in: items
    /// ) {
    ///     // Automatically uses the best available algorithm
    ///     print("Matched: \(items[match.index])")
    /// }
    /// ```
    static func findBestMatch(
        spokenText: String,
        in items: [String],
        threshold: Double = 0.7,
        domainConfiguration: DomainKeywordConfiguration? = .inspectionDefaults
    ) -> (index: Int, score: Double)? {
        // Call both matching methods to find the best possible match
        var multiSentenceMatch: (index: Int, score: Double)?
        var phraseMatch: (index: Int, score: Double)?

        // Try multi-sentence approach if available (iOS 17+)
        if #available(iOS 17.0, *) {
            multiSentenceMatch = findBestMatchWithMultiSentence(
                spokenText: spokenText,
                in: items,
                threshold: threshold,
                domainConfiguration: domainConfiguration
            )
        }

        // Always try phrase-based approach
        phraseMatch = findBestMatchWithPhrases(
            spokenText: spokenText,
            in: items,
            threshold: threshold,
            domainConfiguration: domainConfiguration
        )

        // Compare scores and return the best match
        if let multi = multiSentenceMatch, let phrase = phraseMatch {
            // Both methods found matches - return the one with higher score
            return multi.score >= phrase.score ? multi : phrase
        } else if let multi = multiSentenceMatch {
            // Only multi-sentence found a match
            return multi
        } else if let phrase = phraseMatch {
            // Only phrase-based found a match
            return phrase
        } else {
            // Neither method found a match
            return nil
        }
    }

    /// **Phrase-based approach**: Finds the best matching item using linguistic phrase analysis.
    ///
    /// This function uses key phrase extraction and matching:
    /// - Extracts key phrases using NLTagger
    /// - Calculates base similarity using string matching
    /// - Applies bonuses for matching key phrases
    /// - Rewards specificity (longer, more detailed items)
    /// - Applies bonuses for complex matches (3+ common phrases)
    /// - Normalizes final scores to 0-1 range
    /// - **Enhanced with ordinal number recognition** for room variations
    ///
    /// - Parameters:
    ///   - spokenText: The text from speech recognition
    ///   - items: Array of item names to match against
    ///   - threshold: Minimum score threshold (default: 0.7)
    /// - Returns: Tuple of (index, score) for the best match, or nil if no match above threshold
    static func findBestMatchWithPhrases(
        spokenText: String,
        in items: [String],
        threshold: Double = 0.7,
        domainConfiguration: DomainKeywordConfiguration? = nil
    ) -> (index: Int, score: Double)? {
        var bestIndex = -1
        var bestScore = 0.0

        // DEBUG: Print initial input
        print("🔍 DEBUG: findBestMatchWithPhrases")
        print("   Spoken text: '\(spokenText)'")
        print("   Items count: \(items.count)")
        print("   Threshold: \(threshold)")

        // Use the default configuration if none provided
        let config = domainConfiguration ?? DomainKeywordConfiguration.inspectionDefaults

        // Create ordinal-enhanced variations of the spoken text with context awareness
        let spokenVariations = OrdinalMatcher.createVariations(spokenText, contextItems: items)
        print("   Spoken variations: \(spokenVariations)")

        // First pass: Check if we have any number-based matches in the list
        // This includes ordinal matches ("second" → "2") and direct number matches ("bedroom 2")
        var hasNumberMatch = false
        for item in items {
            if OrdinalMatcher.extractFirstItemNumber(from: item.lowercased()) != nil {
                hasNumberMatch = true
                break
            }
        }

        // CRITICAL: Detect if spoken text contains ordinal indicators
        let spokenWordsForOrdinal = spokenText.lowercased().split(separator: " ").map(String.init)
        let containsOrdinalIndicator = spokenWordsForOrdinal.contains { word in
            // Check for ordinal words (first, second, third, etc.)
            ordinals.keys.contains(word) ||
                // Check for numeric ordinals (1st, 2nd, 3rd, etc.)
                (word.count > 2 && (word.hasSuffix("st") || word.hasSuffix("nd") || word.hasSuffix("rd") || word.hasSuffix("th")) && Int(String(word.dropLast(2))) != nil) ||
                // Check for written numbers (one, two, three, etc.)
                singleDigits.keys.contains(word) || teens.keys.contains(word) || tens.keys.contains(word)
        }

        // Also check if spoken text contains plain numbers (not ordinals)
        let spokenItemNumber = OrdinalMatcher.extractFirstItemNumber(from: spokenText.lowercased())
        let containsPlainNumber = spokenItemNumber != nil

        for (index, item) in items.enumerated() {
            var score = 0.0
            var isOrdinalMatch = false
            var bestVariation = spokenText
            var matchedNumber: String?

            print("\n   🎯 Evaluating item [\(index)]: '\(item)'")

            // Test each variation of the spoken text against the item
            for variation in spokenVariations {
                let variationScore = matchScore(spokenText: variation, itemName: item, configuration: config)

                if variationScore > score {
                    score = variationScore
                    bestVariation = variation

                    // Check if this is a number-based match
                    isOrdinalMatch = OrdinalMatcher.isNumberBasedMatch(spokenText: spokenText, variation: variation, itemName: item)

                    // Extract the number from the variation
                    matchedNumber = OrdinalMatcher.extractFirstItemNumber(from: variation.lowercased())

                    print("      New best variation: '\(variation)' with score: \(variationScore)")
                    print("      Is ordinal match: \(isOrdinalMatch), matched number: \(matchedNumber ?? "nil")")
                }
            }

            // Guard against meaningless text - require minimum semantic similarity
            if score < 0.05 {
                print("      ❌ Score too low (\(score)), skipping")
                continue
            }

            // CRITICAL ORDINAL LOGIC REDESIGN
            let itemNumber = OrdinalMatcher.extractFirstItemNumber(from: item.lowercased())
            print("      Item number: \(itemNumber ?? "nil")")
            print("      Contains ordinal indicator: \(containsOrdinalIndicator)")
            print("      Has number match in list: \(hasNumberMatch)")
            
            // PHRASE COVERAGE PROTECTION SYSTEM
            // Track score before ordinal penalties for recovery logic
            let scoreBeforeOrdinalPenalties = score

            if containsOrdinalIndicator && hasNumberMatch {
                // Spoken text has ordinal indicators and there are numbered items in the list
                print("      📊 Ordinal logic branch")

                if itemNumber != nil, isOrdinalMatch {
                    // This is a numbered item and we have an ordinal match
                    if let spokenNumber = matchedNumber ?? OrdinalMatcher.extractFirstNumberIncludingWritten(from: bestVariation) {
                        if spokenNumber == itemNumber {
                            // EXACT NUMBER MATCH - Give very high score
                            let oldScore = score
                            score = max(score, 0.95)
                            print("      ✅ EXACT NUMBER MATCH! Score: \(oldScore) → \(score)")
                        } else {
                            // Wrong number - don't add bonus (keep score as is)
                            let oldScore = score
                            // No bonus for wrong number, but don't reduce score
                            print("      ❌ Wrong number! Spoken: '\(spokenNumber)' vs Item: '\(itemNumber!)'. Score remains: \(score)")
                        }
                    } else {
                        // Ordinal match but couldn't extract specific number
                        let oldScore = score
                        score = max(score, 0.88)
                        print("      ⚠️ Ordinal match but no specific number. Score: \(oldScore) → \(score)")
                    }
                } else if itemNumber == nil {
                    // This is a base item (no number) but spoken text has ordinal
                    // Only allow base item to win if spoken text explicitly says "first" or "one"
                    let isFirstOrOne = spokenWordsForOrdinal.contains("first") ||
                        (spokenWordsForOrdinal.contains("one") && !spokenWordsForOrdinal.contains(where: { $0.contains("one") && $0 != "one" }))

                    if !isFirstOrOne {
                        // Base item with non-first ordinal - no bonus
                        let oldScore = score
                        // Keep score as is, don't reduce
                        print("      🚫 Base item with non-first ordinal. Score remains: \(score)")
                    } else {
                        print("      ✅ Base item with 'first' or 'one' - no penalty")
                    }
                }
            } else if hasNumberMatch && isOrdinalMatch && score > 0.05 {
                // Original ordinal bonus logic for backward compatibility
                score = score + (1.0 - score) * OrdinalMatcher.ScoringConstants.ordinalBonus

                if let spokenNumber = OrdinalMatcher.extractFirstNumberIncludingWritten(from: bestVariation),
                   let itemNum = itemNumber
                {
                    if spokenNumber == itemNum {
                        score = score + (1.0 - score) * OrdinalMatcher.ScoringConstants.pureOrdinalMatchBonus
                        score = max(score, 0.88)
                    }
                }
            }

            // Handle plain number cases (e.g., "storage 3" rather than "third storage")
            if containsPlainNumber && !containsOrdinalIndicator && hasNumberMatch {
                if let itemNum = itemNumber, let spokenNum = spokenItemNumber {
                    if itemNum == spokenNum {
                        // Exact number match - boost score significantly
                        score = max(score, 0.92)
                    } else {
                        // Wrong number - apply penalty, but consider text similarity
                        // If the text parts match very well, reduce the penalty
                        let spokenWithoutNum = spokenText.lowercased().replacingOccurrences(of: spokenNum, with: "").trimmingCharacters(in: .whitespacesAndNewlines)
                        let itemWithoutNum = item.lowercased().replacingOccurrences(of: itemNum, with: "").trimmingCharacters(in: .whitespacesAndNewlines)

                        if spokenWithoutNum == itemWithoutNum {
                            // Perfect text match, add small bonus despite number mismatch
                            score = min(1.0, score + 0.1)  // Add small bonus for text match
                        } else if score > 0.7 {
                            // High similarity, keep score as is
                            // No change - already has good score
                        } else {
                            // Low similarity, no bonus
                            // Keep score as is
                        }
                    }
                } else if itemNumber == nil, spokenItemNumber != nil {
                    // Base item when number is spoken - no bonus
                    // Exception: if spoken number is "1", base item might be acceptable
                    if spokenItemNumber != "1" {
                        // Keep score as is, no bonus for mismatch
                    }
                }
            }

            // Skip the additional exact name bonus logic since matchScore already handles it
            // The matchScore function already includes proper scoring for partial and exact matches

            // Need these for the logic below
            let itemLower = item.lowercased()
            let spokenLower = spokenText.lowercased()
            let spokenWords = spokenLower.split(separator: " ").map(String.init)

            // Always apply key phrase matching logic
            // Extract key words from spoken text for better matching
            let spokenKeyPhrases = Set(extractKeyPhrases(spokenText))

            // Bonus for items that contain more of the spoken key phrases
            let itemKeyPhrases = Set(extractKeyPhrases(item))
            var commonKeyPhrases = itemKeyPhrases.intersection(spokenKeyPhrases)

            // DOMAIN KEYWORD MATCHING: Check for semantic matches using domain configuration
            var domainBonus = 0.0
            var hasSubjectMatch = false
            var domainMatchCount: [String: Int] = [:] // Track matches per domain

            if !commonKeyPhrases.isEmpty || config.keywords.isEmpty == false {
                // First, count how many domain keywords from each category are present in spoken text
                for (domainName, domainWords) in config.keywords {
                    let spokenDomainMatches = spokenKeyPhrases.filter { domainWords.contains($0) }
                    let itemDomainMatches = itemKeyPhrases.filter { domainWords.contains($0) }

                    if !spokenDomainMatches.isEmpty, !itemDomainMatches.isEmpty {
                        // Both spoken and item have words from this domain
                        domainMatchCount[domainName] = spokenDomainMatches.count

                        // Add semantic matches to common phrases
                        for spokenPhrase in spokenDomainMatches {
                            commonKeyPhrases.insert(spokenPhrase)
                        }
                    }
                }

                // Give bonus based on domain matches, with extra weight for multiple matches in same domain
                for (domainName, matchCount) in domainMatchCount {
                    if domainName != "status", domainName != "security" {
                        // Subject-specific domain (e.g., fire, lighting, plumbing)
                        // Give exponentially stronger bonus for multiple domain matches
                        domainBonus += 0.4 * Double(matchCount) // e.g., "fire" + "suppression" = 0.8
                        hasSubjectMatch = true
                    } else {
                        // Generic domains get smaller bonus
                        domainBonus += 0.1 * Double(matchCount)
                    }
                }
            }

            if !commonKeyPhrases.isEmpty {
                // Give very high bonus based on how many key phrases match (including domain matches)
                // Strongly prioritize items with more matching key phrases
                let matchRatio = Double(commonKeyPhrases.count) / Double(spokenKeyPhrases.count)
                let countBonus = Double(commonKeyPhrases.count) * OrdinalMatcher.ScoringConstants.keyPhraseMultiplier

                // Additional bonus for items that match more of their own key phrases
                let itemPhraseRatio = Double(commonKeyPhrases.count) / Double(itemKeyPhrases.count)
                let completenessBonus = itemPhraseRatio * OrdinalMatcher.ScoringConstants.keyPhraseMultiplier

                // Extra bonus for having 3 or more matching phrases (indicates complex match)
                let complexMatchBonus = commonKeyPhrases.count >= 3 ? OrdinalMatcher.ScoringConstants.complexMatchBonus : 0.0

                let keyPhraseBonus = (matchRatio * 0.3) + countBonus + completenessBonus + complexMatchBonus + domainBonus

                // For subject matches, apply bonus more decisively to ensure correct item wins
                if hasSubjectMatch {
                    // Strong subject match - give it a much higher score to beat generic matches
                    let subjectScore = max(score, 0.75) + (keyPhraseBonus * 0.2)
                    score = subjectScore // No cap
                } else {
                    // Apply bonus multiplicatively to avoid exceeding 1.0
                    score = score + (1.0 - score) * min(keyPhraseBonus, 0.4) // Increased cap to 0.4 to accommodate domain bonus
                    // No cap needed
                }
            }
            
            // PHRASE COVERAGE RECOVERY LOGIC
            // If ordinal penalties reduced a good match too much, recover based on phrase coverage
            let phraseCoverageRatio: Double = itemKeyPhrases.isEmpty ? 0.0 : Double(commonKeyPhrases.count) / Double(itemKeyPhrases.count)
            
            if scoreBeforeOrdinalPenalties >= 0.5 && score < 0.7 && phraseCoverageRatio >= 0.5 {
                let recoveredScore = 0.7
                print("      🔄 Phrase Coverage Recovery: Score \(score) → \(recoveredScore)")
                print("         Reason: Good initial score (\(scoreBeforeOrdinalPenalties)) with high phrase coverage (\(phraseCoverageRatio))")
                score = recoveredScore
            }

            // Special bonus for longer items that contain more specific information
            // Check if this item has more words than other items and shares core concepts
            // (reuse existing itemKeyPhrases and spokenKeyPhrases variables)
            if !commonKeyPhrases.isEmpty {
                // Check if spoken text contains "illuminated" or similar specific terms
                let itemWords = item.lowercased().split(separator: " ").map(String.init)
                let hasSpecificTerms = itemWords.contains { word in
                    spokenKeyPhrases.contains(lemmatize(word))
                }

                // Count unmatched item words, filtering out punctuation and short words
                let unmatchedItemWords = itemWords.filter { word in
                    // Skip punctuation, numbers, and very short words
                    guard word.count > 1, word.rangeOfCharacter(from: CharacterSet.alphanumerics) != nil else {
                        return false
                    }
                    return !spokenKeyPhrases.contains(lemmatize(word)) && !spokenWords.contains(word)
                }

                // Penalize items with unmatched words unless those words are in the spoken text
                // BUT skip this penalty if we have a valid ordinal match (to avoid penalizing numbers)
                if unmatchedItemWords.count > 0, !isOrdinalMatch {
                    let penaltyMultiplier = Double(unmatchedItemWords.count) / Double(itemWords.count)
                    // Stronger penalty to ensure shorter items win when spoken text doesn't include extra words
                    score -= OrdinalMatcher.ScoringConstants.keyPhraseMultiplier * penaltyMultiplier * 2.0
                }

                // Give bonus to longer, more specific items when they match additional specific terms
                // This ensures "Emergency Exit Sign - Illuminated" beats shorter matches when spoken text includes "illuminated"
                if hasSpecificTerms, itemWords.count >= 3, !commonKeyPhrases.isEmpty, unmatchedItemWords.isEmpty {
                    let specificityMultiplier = Double(commonKeyPhrases.count) / Double(itemKeyPhrases.count)
                    // Increased bonus for longer, more specific items, with extra bonus for 4+ words
                    let lengthMultiplier = itemWords.count >= 4 ? 2.5 : 1.5
                    let bonusAmount = OrdinalMatcher.ScoringConstants.specificityBonus * specificityMultiplier * lengthMultiplier
                    score = score + (1.0 - score) * bonusAmount
                }

                // Special handling for hierarchical items where one is an extension of another
                // This helps "Emergency Exit Sign - Illuminated" beat "Emergency Exit Sign"
                // Only apply when there's actual competition between hierarchical items
                var hasHierarchicalCompetitor = false
                var hierarchicalExtraPhrases = 0

                for (otherIndex, otherItem) in items.enumerated() {
                    if otherIndex != index {
                        let otherLower = otherItem.lowercased()
                        let thisLower = item.lowercased()

                        // Check if this item starts with the other item (hierarchical relationship)
                        // Remove dashes and extra spaces for comparison
                        let thisNormalized = thisLower.replacingOccurrences(of: " - ", with: " ").replacingOccurrences(of: "  ", with: " ")
                        let otherNormalized = otherLower.replacingOccurrences(of: " - ", with: " ").replacingOccurrences(of: "  ", with: " ")

                        if thisNormalized.hasPrefix(otherNormalized), thisNormalized != otherNormalized {
                            // This item is a more specific version of the other item
                            let otherKeyPhrases = Set(extractKeyPhrases(otherLower))
                            let otherCommonPhrases = otherKeyPhrases.intersection(spokenKeyPhrases)

                            // Check if both items have good matches with spoken text
                            if otherCommonPhrases.count >= 2, commonKeyPhrases.count > otherCommonPhrases.count {
                                // Both items match well, but this one matches more
                                hasHierarchicalCompetitor = true
                                hierarchicalExtraPhrases = commonKeyPhrases.count - otherCommonPhrases.count
                            }
                        }
                    }
                }

                // Only apply hierarchical bonus when competing with a shorter variant
                if hasHierarchicalCompetitor, hierarchicalExtraPhrases > 0, score >= 0.8 {
                    // Moderate bonus to win over hierarchical competitors
                    let hierarchicalScore = score + (1.0 - score) * 0.5 + Double(hierarchicalExtraPhrases) * 0.05
                    // Cap at 1.0 to ensure normalized scores
                    score = hierarchicalScore
                }

                // Strong bonus for comprehensive matches - items that match all or most spoken key phrases
                let matchRatio = Double(commonKeyPhrases.count) / Double(spokenKeyPhrases.count)
                if matchRatio >= 1.0, spokenKeyPhrases.count > 1 {
                    // Perfect comprehensive match - ensure this wins
                    // When spoken text contains extra words, prefer the more specific item
                    let itemWords = item.lowercased().split(separator: " ").filter { !$0.isEmpty && $0 != "-" }
                    let spokenHasExtraWords = spokenWords.count > itemWords.count + 2 // Allow for some filler words

                    if spokenHasExtraWords, itemWords.count >= 4 {
                        // Prefer longer, more specific items when spoken text has extra descriptive words
                        let specificityBonus = Double(itemWords.count) * 0.02 // Bonus for more words
                        score = max(score, 0.95)
                        // Apply bonus but always cap at 1.0
                        score = score + specificityBonus
                    } else {
                        // For shorter spoken text, just ensure a high score
                        score = max(score, 0.95)
                    }
                } else if matchRatio >= 0.8, spokenKeyPhrases.count > 1 {
                    let comprehensiveBonus = matchRatio * 0.4
                    score = score + (1.0 - score) * comprehensiveBonus
                    // No cap needed
                }

                // Penalize items that miss key phrases when spoken text has multiple important words
                // This ensures "Emergency Exit Sign" beats "Exit" for "exit sign"
                let missedKeyPhrases = spokenKeyPhrases.subtracting(itemKeyPhrases)
                if !missedKeyPhrases.isEmpty, spokenKeyPhrases.count > 1 {
                    let missedRatio = Double(missedKeyPhrases.count) / Double(spokenKeyPhrases.count)
                    let penalty = missedRatio * 0.3 // Moderate penalty for missing key phrases
                    score = score * (1.0 - penalty)
                }

                // Also penalize items that have extra unmatched words when spoken text is short
                // This ensures "Emergency Exit Sign" beats "Emergency Exit Sign - Illuminated" for "exit sign"
                let unmatchedItemPhrases = itemKeyPhrases.subtracting(spokenKeyPhrases)
                if !unmatchedItemPhrases.isEmpty, spokenKeyPhrases.count <= 3 {
                    let unmatchedRatio = Double(unmatchedItemPhrases.count) / Double(itemKeyPhrases.count)
                    let penalty = unmatchedRatio * 0.2 // Penalty for unmatched item words
                    score = score * (1.0 - penalty)
                }

                for (otherIndex, otherItem) in items.enumerated() {
                    if otherIndex != index {
                        let otherKeyPhrases = Set(extractKeyPhrases(otherItem))
                        let sharedPhrases = itemKeyPhrases.intersection(otherKeyPhrases)

                        // If this item contains all phrases from another item plus more
                        if !sharedPhrases.isEmpty,
                           sharedPhrases == otherKeyPhrases,
                           itemKeyPhrases.count > otherKeyPhrases.count
                        {
                            // Give strong bonus for being more specific
                            // This ensures "Emergency Exit Sign - Illuminated" beats "Emergency Exit Sign"
                            let hierarchicalBonus = 0.3 + (Double(itemKeyPhrases.count - otherKeyPhrases.count) * 0.1)
                            score = score + (1.0 - score) * hierarchicalBonus
                            // No cap needed
                        }
                    }
                }
            }

            // Give bonus to shorter, more generic item names when appropriate
            // This helps "Bedroom" beat "Master Bedroom" for generic "bedroom" queries
            let itemHasNumber = OrdinalMatcher.extractFirstItemNumber(from: itemLower) != nil
            // Check if spoken text contains numbers (with word boundary checking)
            // Also check variations for numbers
            let spokenHasNumber = OrdinalMatcher.extractFirstItemNumber(from: spokenLower) != nil ||
                ordinals.keys.contains { ordinal in spokenWords.contains(ordinal) } ||
                singleDigits.keys.contains { digit in spokenWords.contains(digit) } ||
                spokenVariations.contains { variation in OrdinalMatcher.extractFirstItemNumber(from: variation.lowercased()) != nil }

            // Apply single-word bonus only in very specific cases:
            // 1. The item is a single word without numbers
            // 2. There's already a strong match (score > 0.6)
            // 3. The spoken text either has no numbers OR explicitly says "first"/"one"
            // This helps "Bedroom" beat "Bedroom 2" when user says "bedroom" or "first bedroom"
            if itemLower.split(separator: " ").count == 1,
               !itemHasNumber,
               score > 0.5
            { // Only apply to already good matches
                // Check if this single word item has numbered variants in the list
                let itemBase = itemLower
                let hasNumberedVariants = items.contains { otherItem in
                    let otherLower = otherItem.lowercased()
                    return otherLower != itemLower &&
                        otherLower.hasPrefix(itemBase) &&
                        OrdinalMatcher.extractFirstItemNumber(from: otherLower) != nil
                }

                // Only apply bonus if there are numbered variants to disambiguate from
                if hasNumberedVariants {
                    let spokenLower = spokenText.lowercased()
                    let hasFirstOrOne = spokenLower.contains("first") || spokenLower.contains(" one") || spokenLower.contains("one ")

                    if !spokenHasNumber || hasFirstOrOne {
                        score = score + (1.0 - score) * OrdinalMatcher.ScoringConstants.singleWordBonus
                        // No cap needed
                    }
                }
            }

            // CRITICAL: Enhanced scoring based on matching word count
            // Apply word count boosting to favor items with more matching words
            if score > 0.0 && !isOrdinalMatch {
                let itemWords = item.lowercased().split(separator: " ")
                    .map(String.init)
                    .filter { $0.count >= 2 && !["the", "and", "or", "is", "are", "was", "were", "has", "have", "-"].contains($0) }
                
                let spokenWords = Set(spokenText.lowercased().split(separator: " ").map(String.init))
                
                // Count how many item words are found in spoken text
                let matchingWordCount = itemWords.filter { word in
                    spokenWords.contains(word)
                }.count
                
                let allItemWordsFound = matchingWordCount == itemWords.count
                
                // Apply word count boosting based on matching words
                let spokenWordCount = spokenWords.count
                
                if matchingWordCount >= 2 {
                    // Strong boost for multiple matching words
                    let wordCountBoost = Double(matchingWordCount) * 0.3
                    score = score + (1.0 - score) * wordCountBoost
                    
                    // Additional bonus for complete matches
                    if allItemWordsFound {
                        let completeMatchBonus = 0.25
                        score = score + (1.0 - score) * completeMatchBonus
                        print("      ✅ Complete match bonus with \(matchingWordCount) words")
                    }
                    
                    print("      📊 Matching words: \(matchingWordCount)/\(itemWords.count), word count boost applied")
                } else if matchingWordCount == 1 && allItemWordsFound && itemWords.count == 1 {
                    // For single word items, apply penalty if spoken text has multiple words
                    // This helps "Emergency Exit Sign" beat "Exit" for "exit sign"
                    if spokenWordCount > 1 {
                        // Penalty for being too simple when spoken text is complex
                        let simplicityPenalty = 0.2
                        score = score * (1.0 - simplicityPenalty)
                        print("      ⚠️ Single word item penalty when spoken has \(spokenWordCount) words")
                    } else {
                        // Small bonus for single word complete matches
                        let singleWordBonus = 0.1
                        score = score + (1.0 - score) * singleWordBonus
                        print("      ✅ Single word complete match bonus")
                    }
                }
            }
            
            // Score can exceed 1.0 for better differentiation
            // No artificial cap applied

            // IMPORTANT: Apply minimum score guarantee AFTER all penalties
            // This ensures that when coverage ratio is >= 50% with meaningful phrase matching,
            // the final score is at least 0.7, even after penalties are applied
            let itemWordsList = itemLower.split(separator: " ").filter { !$0.isEmpty }
            let spokenWordsList = spokenLower.split(separator: " ").filter { !$0.isEmpty }
            let matchedWords = itemWordsList.filter { itemWord in
                spokenWordsList.contains { spokenWord in
                    spokenWord == itemWord || lemmatize(String(spokenWord)) == lemmatize(String(itemWord))
                }
            }
            let itemCoverageRatio = itemWordsList.isEmpty ? 0.0 : Double(matchedWords.count) / Double(itemWordsList.count)

            // Check if we have meaningful phrase matching (not just number matching)
            let hasSignificantPhraseMatch = !commonKeyPhrases.isEmpty && commonKeyPhrases.count >= max(1, itemKeyPhrases.count / 2)
            
            // Also check phrase-level coverage from the matchScore calculation
            // If matchScore found good matches for most item phrases, ensure minimum score
            let phraseCoverageFromScore = score > 0.0 && bestVariation == spokenText && isOrdinalMatch == false
            
            print("            - Debug FINAL guarantee check:")
            print("              - itemCoverageRatio: \(itemCoverageRatio)")
            print("              - hasSignificantPhraseMatch: \(hasSignificantPhraseMatch)")
            print("              - commonKeyPhrases.count: \(commonKeyPhrases.count)")
            print("              - itemKeyPhrases.count: \(itemKeyPhrases.count)")
            print("              - current score: \(score)")
            print("              - phraseCoverageFromScore: \(phraseCoverageFromScore)")

            // Apply guarantee if we have good coverage OR if the initial matchScore was high
            // This handles cases where matchScore found good phrase matches but penalties reduced the score
            // Special case: if initial score from matchScore was >= 0.6 and we have phrase matches, ensure minimum 0.7
            let hadHighInitialScore = score >= 0.5 && !commonKeyPhrases.isEmpty
            
            if (itemCoverageRatio >= 0.5 || hadHighInitialScore) && hasSignificantPhraseMatch && score < 0.7 {
                score = 0.7
                print("            - Applied FINAL minimum score guarantee (50%+ meaningful coverage → score >= 0.7)")
            }

            print("      📈 Final score for this item: \(score)")

            if score > bestScore {
                print("      🏆 NEW BEST! Previous best: \(bestScore) → New best: \(score)")
                bestScore = score
                bestIndex = index
            }
        }

        print("\n📊 FINAL RESULTS:")
        print("   Best index: \(bestIndex)")
        print("   Best score: \(bestScore)")
        print("   Threshold: \(threshold)")

        // Always return the best match regardless of threshold
        return (bestIndex > -1 && bestScore >= threshold) ? (bestIndex, bestScore) : nil
    }
}

// MARK: - Modern Semantic Matching (iOS 17+)

extension SpeechNormalizer {
    /// **Modern approach**: Finds the best matching item using NLEmbedding sentence embeddings (iOS 17+).
    ///
    /// This function uses advanced semantic similarity that:
    /// - **Primary**: Semantic similarity using NLEmbedding sentence embeddings (70% weight)
    /// - **Secondary**: Traditional string matching for coverage (20% weight)
    /// - **Tertiary**: Key phrase matching for technical terms (10% weight)
    /// - **Bonus**: Phonetic similarity for speech recognition errors (5% weight)
    /// - Automatically falls back to legacy approach if embeddings unavailable
    ///
    /// **Benefits over legacy approach**:
    /// - Understands semantic meaning: "water damage" matches "moisture intrusion"
    /// - Better context understanding: considers full sentence meaning
    /// - Multi-language support with explicit language control
    /// - More accurate matching for complex inspection phrases
    ///
    /// - Parameters:
    ///   - spokenText: The text from speech recognition
    ///   - items: Array of item names to match against
    ///   - threshold: Minimum score threshold (default: 0.7)
    ///   - language: Language code (e.g., "en", "fr", "es"). Defaults to "en"
    /// - Returns: Tuple of (index, score) for the best match, or nil if no match above threshold
    ///
    /// Example:
    /// ```swift
    /// let items = ["Fire Extinguisher", "Smoke Detector", "Exit Signs"]
    ///
    /// // Basic usage with English (default)
    /// if let match = SpeechNormalizer.findBestMatchWithEmbedding(
    ///     spokenText: "smoke detector checked",
    ///     in: items
    /// ) {
    ///     print("Matched: \(items[match.index])") // "Smoke Detector"
    /// }
    ///
    /// // Multi-language support
    /// if let match = SpeechNormalizer.findBestMatchWithEmbedding(
    ///     spokenText: "détecteur de fumée",
    ///     in: items,
    ///     language: "fr"
    /// ) {
    ///     print("French match: \(items[match.index])")
    /// }
    ///
    /// // Semantic understanding - these would match semantically similar items:
    /// // "water damage" → "moisture intrusion"
    /// // "fire alarm" → "fire suppression system"
    /// // "broken window" → "window damage"
    /// ```
    @available(iOS 17.0, *)
    static func findBestMatchWithEmbedding(
        spokenText: String,
        in items: [String],
        threshold: Double = 0.7,
        language: String = "en"
    ) -> (index: Int, score: Double)? {
        // Use provided language (defaults to English)
        let nlLanguage = NLLanguage(rawValue: language)

        guard let embedding = NLEmbedding.sentenceEmbedding(for: nlLanguage) else {
            // Embedding unavailable for current language
            return nil
        }

        var bestIndex = -1
        var bestScore = 0.0

        // Get embedding for spoken text
        guard let spokenEmbedding = embedding.vector(for: spokenText) else {
            // Embedding extraction failed
            return nil
        }

        for (index, item) in items.enumerated() {
            guard let itemEmbedding = embedding.vector(for: item) else {
                continue // Skip items without embeddings
            }

            let semanticScore = cosineSimilarity(spokenEmbedding, itemEmbedding)
            if semanticScore > bestScore {
                bestScore = semanticScore
                bestIndex = index
            }
        }

        // Always return the best match regardless of threshold
        return (bestIndex > -1 && bestScore >= threshold) ? (bestIndex, bestScore) : nil
    }

    // Calculates cosine similarity between two vectors
    /// - Parameters:
    ///   - vectorA: First vector
    ///   - vectorB: Second vector
    /// - Returns: Cosine similarity score between 0 and 1
    private static func cosineSimilarity(_ vectorA: [Double], _ vectorB: [Double]) -> Double {
        guard vectorA.count == vectorB.count, !vectorA.isEmpty else {
            return 0.0
        }

        var dotProduct = 0.0
        var magnitudeA = 0.0
        var magnitudeB = 0.0

        for i in 0 ..< vectorA.count {
            dotProduct += vectorA[i] * vectorB[i]
            magnitudeA += vectorA[i] * vectorA[i]
            magnitudeB += vectorB[i] * vectorB[i]
        }

        magnitudeA = sqrt(magnitudeA)
        magnitudeB = sqrt(magnitudeB)

        guard magnitudeA > 0 && magnitudeB > 0 else {
            return 0.0
        }

        // Cosine similarity normalized to 0-1 range
        let similarity = dotProduct / (magnitudeA * magnitudeB)
        return max(0.0, min(1.0, similarity))
    }
}

// MARK: - Multi-Sentence Processing (iOS 17+)

public extension SpeechNormalizer {
    /// Enhanced multi-sentence matching with back-to-front processing for complex spoken text
    /// Processes longer utterances by segmenting into sentences and analyzing from back to front
    /// to prioritize context that often appears at the end of natural speech patterns.
    ///
    /// Example usage:
    /// ```swift
    /// let items = ["Smoke Detector", "Fire Suppression", "HVAC System"]
    /// let result = SpeechNormalizer.findBestMatchMultiSentence(
    ///     spokenText: "check the detectors and alarms in the fire suppression system",
    ///     in: items
    /// )
    /// // Processes "fire suppression system" context first, then "detectors and alarms"
    /// ```
    @available(iOS 17.0, *)
    static func findBestMatchMultiSentence(
        spokenText: String,
        in items: [String],
        maxMatches: Int = 3,
        threshold: Double = 0.3,
        language: String = "en",
        domainConfiguration: DomainKeywordConfiguration? = nil
    ) -> [MatchResult] {
        // Step 1: Intelligent text segmentation
        let segments = segmentSpokenText(spokenText, language: language)

        // For short or simple text, use single-sentence approach
        if segments.count == 1 {
            // Try embedding-based matching first, fall back to phrase-based if unavailable
            let match: (index: Int, score: Double)?
            if let embeddingMatch = findBestMatchWithEmbedding(
                spokenText: spokenText,
                in: items,
                threshold: threshold,
                language: language
            ) {
                match = embeddingMatch
            } else {
                // Fall back to phrase-based matching when embeddings are unavailable
                match = findBestMatchWithPhrases(
                    spokenText: spokenText,
                    in: items,
                    threshold: threshold,
                    domainConfiguration: domainConfiguration
                )
            }

            if let match = match {
                return [MatchResult(
                    index: match.index,
                    score: match.score,
                    confidence: confidenceLevel(for: match.score),
                    segmentInfo: "Single segment"
                )]
            }
            return []
        }

        // Step 2: Back-to-front processing - Start from the end where context is often clearest
        let domainKeywords = domainConfiguration?.keywords ?? [:]
        let backToFrontMatches = processSegmentsBackToFront(segments, in: items, threshold: threshold, language: language, domainKeywords: domainKeywords)

        // Step 3: Apply contextual boosting based on back-to-front analysis
        let contextEnhancedMatches = applyContextualBoosting(backToFrontMatches, originalText: spokenText, items: items)

        // Step 4: Return top matches up to maxMatches, sorted by enhanced score
        return Array(contextEnhancedMatches.prefix(maxMatches))
    }

    /// Single-best match using multi-sentence processing when beneficial
    /// Automatically determines whether to use segmentation based on text complexity
    @available(iOS 17.0, *)
    static func findBestMatchWithMultiSentence(
        spokenText: String,
        in items: [String],
        threshold: Double = 0.3,
        language: String = "en",
        domainConfiguration: DomainKeywordConfiguration? = nil
    ) -> (index: Int, score: Double)? {
        // Analyze text complexity to decide approach
        let shouldUseSegmentation = shouldUseMultiSentenceProcessing(spokenText)

        if shouldUseSegmentation {
            let matches = findBestMatchMultiSentence(
                spokenText: spokenText,
                in: items,
                maxMatches: 1,
                threshold: threshold,
                language: language,
                domainConfiguration: domainConfiguration
            )

            if let bestMatch = matches.first {
                return (index: bestMatch.index, score: bestMatch.score)
            }
        }

        // Fall back to single-sentence approach
        if let embeddingMatch = findBestMatchWithEmbedding(
            spokenText: spokenText,
            in: items,
            threshold: threshold,
            language: language
        ) {
            return embeddingMatch
        }

        // Fall back to phrase-based matching when embeddings are unavailable
        return findBestMatchWithPhrases(
            spokenText: spokenText,
            in: items,
            threshold: threshold,
            domainConfiguration: domainConfiguration
        )
    }
}

// MARK: - Multi-Sentence Processing Implementation

extension SpeechNormalizer {
    /// Determines whether text complexity warrants multi-sentence processing
    private static func shouldUseMultiSentenceProcessing(_ text: String) -> Bool {
        let trimmed = text.trimmingCharacters(in: .whitespacesAndNewlines)

        // Use segmentation for longer text or text with coordinating conjunctions
        let conjunctionPatterns = [" and ", " also ", " then ", " plus ", " or ", " but "]
        let hasCoordination = conjunctionPatterns.contains { trimmed.lowercased().contains($0) }

        return trimmed.count > 30 || hasCoordination
    }

    /// Segments spoken text into meaningful phrases for analysis
    private static func segmentSpokenText(_ text: String, language _: String) -> [TextSegment] {
        let trimmed = text.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmed.isEmpty else { return [] }

        var segments: [TextSegment] = []

        // Use NLTokenizer for sentence boundary detection
        let tokenizer = NLTokenizer(unit: .sentence)
        tokenizer.string = trimmed

        var sentenceSegments: [String] = []
        tokenizer.enumerateTokens(in: trimmed.startIndex ..< trimmed.endIndex) { range, _ in
            let sentence = String(trimmed[range]).trimmingCharacters(in: .whitespacesAndNewlines)
            if !sentence.isEmpty {
                sentenceSegments.append(sentence)
            }
            return true
        }

        // If no sentence boundaries found, try coordination splitting
        if sentenceSegments.count <= 1 {
            sentenceSegments = splitOnCoordination(trimmed)
        }

        // Create TextSegment objects with proper indexing
        for (index, segment) in sentenceSegments.enumerated() {
            let range = trimmed.startIndex ..< trimmed.endIndex // Simplified range

            // Mark later segments as potential context providers
            let isContextProvider = index >= sentenceSegments.count / 2

            segments.append(TextSegment(
                text: segment,
                range: range,
                originalIndex: index,
                isContextProvider: isContextProvider
            ))
        }

        return segments.isEmpty ? [TextSegment(text: trimmed, range: trimmed.startIndex ..< trimmed.endIndex, originalIndex: 0)] : segments
    }

    /// Splits text on coordination patterns when sentence boundaries aren't clear
    private static func splitOnCoordination(_ text: String) -> [String] {
        let coordinationPatterns = [" and ", " also ", " then ", " plus "]

        for pattern in coordinationPatterns {
            if text.lowercased().contains(pattern) {
                let components = text.components(separatedBy: pattern)
                return components.map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
                    .filter { !$0.isEmpty }
            }
        }

        return [text]
    }

    /// Processes segments from back to front, giving priority to contextual information
    @available(iOS 17.0, *)
    private static func processSegmentsBackToFront(
        _ segments: [TextSegment],
        in items: [String],
        threshold: Double,
        language: String,
        domainKeywords: [String: [String]]
    ) -> [SegmentMatch] {
        var matches: [SegmentMatch] = []
        var contextMap: [String: Double] = [:] // Track context themes and their importance

        // Process segments in reverse order (back to front)
        for segment in segments.reversed() {
            // Try embedding-based matching first, fall back to phrase-based if unavailable
            let match: (index: Int, score: Double)?
            if let embeddingMatch = findBestMatchWithEmbedding(
                spokenText: segment.text,
                in: items,
                threshold: threshold,
                language: language
            ) {
                match = embeddingMatch
            } else {
                // Fall back to phrase-based matching when embeddings are unavailable
                match = findBestMatchWithPhrases(
                    spokenText: segment.text,
                    in: items,
                    threshold: threshold
                )
            }

            if let match = match {
                // Extract domain context from this segment for future boosting
                let segmentContext = extractDomainContext(segment.text, domainKeywords: domainKeywords)

                // If this is a context provider, it boosts future matches
                if segment.isContextProvider {
                    for context in segmentContext {
                        contextMap[context] = (contextMap[context] ?? 0.0) + 0.2
                    }
                }

                // Apply any existing context boost to this match
                let contextBoost = segmentContext.reduce(0.0) { result, context in
                    result + (contextMap[context] ?? 0.0)
                }

                matches.append(SegmentMatch(
                    segment: segment,
                    matchIndex: match.index,
                    score: match.score,
                    contextBoost: contextBoost
                ))
            }
        }

        // Return matches in original order but with back-to-front computed scores
        return matches.sorted { $0.segment.originalIndex < $1.segment.originalIndex }
    }

    /// Extracts domain context keywords that can boost related matches
    private static func extractDomainContext(_ text: String, domainKeywords: [String: [String]]) -> [String] {
        let lowercased = text.lowercased()
        var contexts: [String] = []

        for (domain, keywords) in domainKeywords {
            if keywords.contains(where: { lowercased.contains($0) }) {
                contexts.append(domain)
            }
        }

        return contexts
    }

    /// Applies final contextual boosting and creates ranked match results
    private static func applyContextualBoosting(
        _ segmentMatches: [SegmentMatch],
        originalText _: String,
        items _: [String]
    ) -> [MatchResult] {
        var results: [MatchResult] = []
        var seenIndices: Set<Int> = []

        // Group matches by item index to handle multiple segments matching the same item
        let groupedMatches = Dictionary(grouping: segmentMatches) { $0.matchIndex }

        for (itemIndex, matches) in groupedMatches {
            // Skip duplicates
            guard !seenIndices.contains(itemIndex) else { continue }
            seenIndices.insert(itemIndex)

            // Calculate combined score for this item
            let combinedScore = matches.reduce(0.0) { $0 + $1.score }
            let averageScore = combinedScore / Double(matches.count)

            // Determine confidence based on score and number of supporting segments
            let confidence: MatchConfidence = confidenceLevel(for: averageScore)

            // Create segment info
            let segmentTexts = matches.map { $0.segment.text }
            let segmentInfo = "Segments: \(segmentTexts.joined(separator: " | "))"

            results.append(MatchResult(
                index: itemIndex,
                score: averageScore,
                confidence: confidence,
                segmentInfo: segmentInfo
            ))
        }

        // Sort by score (highest first)
        return results.sorted { $0.score > $1.score }
    }
}

// MARK: - Hierarchical Matching

public extension SpeechNormalizer {
    /// Finds the best match across mixed hierarchical item lists (all items searched together)
    /// - Parameters:
    ///   - spokenText: The text from speech recognition to match
    ///   - childItems: Array of child item names to include in search
    ///   - parentItems: Array of parent item names to include in search
    ///   - threshold: Minimum score threshold (default: 0.7)
    /// - Returns: Tuple containing the matched level, original index within that level, and score, or nil if no match above threshold
    static func findBestMatchInHierarchy(
        spokenText: String,
        childItems: [String],
        parentItems: [String],
        threshold: Double = 0.7,
        domainConfiguration: DomainKeywordConfiguration? = nil
    ) -> (level: ItemLevel, index: Int, score: Double)? {
        // Validate input - trim whitespace first
        let trimmedText = spokenText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedText.isEmpty,
              trimmedText.count >= 3 else { return nil }

        // Additional validation: reject pure numeric input
        if trimmedText.allSatisfy({ $0.isNumber }) {
            return nil
        }

        // Additional validation: reject input that's mostly special characters or non-ASCII
        let asciiAlphanumericCount = trimmedText.filter {
            ($0.isLetter && $0.isASCII) || $0.isNumber
        }.count
        let totalCount = trimmedText.count
        if asciiAlphanumericCount == 0 || Double(asciiAlphanumericCount) / Double(totalCount) < 0.3 {
            return nil
        }

        // Create item metadata to track origins
        struct ItemMetadata {
            let text: String
            let level: ItemLevel
            let originalIndex: Int
        }

        var allItems: [ItemMetadata] = []

        // Add child items with metadata
        for (index, item) in childItems.enumerated() {
            allItems.append(ItemMetadata(text: item, level: .child, originalIndex: index))
        }

        // Add parent items with metadata
        for (index, item) in parentItems.enumerated() {
            allItems.append(ItemMetadata(text: item, level: .parent, originalIndex: index))
        }

        // Early return if no items to search
        guard !allItems.isEmpty else { return nil }

        // Find best match among child items
        var bestChildMatch: (index: Int, score: Double)?
        if !childItems.isEmpty {
            bestChildMatch = findBestMatch(
                spokenText: trimmedText,
                in: childItems,
                threshold: threshold,
                domainConfiguration: domainConfiguration
            )
        }

        // Find best match among parent items
        var bestParentMatch: (index: Int, score: Double)?
        if !parentItems.isEmpty {
            bestParentMatch = findBestMatch(
                spokenText: trimmedText,
                in: parentItems,
                threshold: threshold,
                domainConfiguration: domainConfiguration
            )
        }

        // Determine which match to return
        var finalMatch: (level: ItemLevel, index: Int, score: Double)?

        if let childMatch = bestChildMatch, let parentMatch = bestParentMatch {
            // Both matches exist - choose the one with the highest score
            // In mixed hierarchy matching, we want the best overall match regardless of level
            if childMatch.score >= parentMatch.score {
                finalMatch = (level: .child, index: childMatch.index, score: childMatch.score)
            } else {
                finalMatch = (level: .parent, index: parentMatch.index, score: parentMatch.score)
            }
        } else if let childMatch = bestChildMatch {
            // Only child match exists
            finalMatch = (level: .child, index: childMatch.index, score: childMatch.score)
        } else if let parentMatch = bestParentMatch {
            // Only parent match exists
            finalMatch = (level: .parent, index: parentMatch.index, score: parentMatch.score)
        }

        #if DEBUG
            if let match = finalMatch {
                print("=== findBestMatchInMixedHierarchy Debug ===")
                print("Spoken text: '\(spokenText)'")
                print("Best child match: \(bestChildMatch.map { "index=\($0.index), score=\($0.score)" } ?? "none")")
                print("Best parent match: \(bestParentMatch.map { "index=\($0.index), score=\($0.score)" } ?? "none")")
                print("Final match: level=\(match.level), index=\(match.index), score=\(match.score)")
                print("=========================================")
            }
        #endif

        return finalMatch
    }
}
