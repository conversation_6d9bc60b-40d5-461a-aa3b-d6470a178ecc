import Foundation
import NaturalLanguage

/// Handles ordinal number matching for speech recognition
/// Converts ordinal words (first, second), written numbers (one, two), and numeric ordinals (1st, 2nd)
/// to their numeric equivalents for better matching with item names
public struct OrdinalMatcher {
    
    // MARK: - Constants
    
    /// Scoring constants for ordinal number matching
    public struct ScoringConstants {
        public static let ordinalBonus: Double = 0.35  // Strong preference for number matches
        public static let pureOrdinalMatchBonus: Double = 0.45  // Very strong bonus for pure ordinal matches like "second" → "Bedroom 2"
        public static let singleWordBonus: Double = 0.05  // Small preference for base items
        public static let exactNameBonus: Double = 0.25  // Strong bonus for exact name match
        public static let keyPhraseMultiplier: Double = 0.20  // Higher weight for key phrases
        public static let specificityBonus: Double = 0.12  // Moderate specificity bonus
        public static let complexMatchBonus: Double = 0.18  // Strong bonus for complex matches
        public static let partialMatchBonus: Double = 0.15  // Moderate bonus for substring matches
    }
    
    // MARK: - Properties
    
    /// Cached regex for number extraction
    private static let numberRegex: NSRegularExpression? = {
        return try? NSRegularExpression(pattern: "\\d+", options: [])
    }()
    
    /// Cache for ordinal variations to improve performance
    private static var variationCache = [String: [String]]()
    
    /// Common acoustic confusions in speech recognition for numbers
    private static let phoneticConfusions: [String: [String]] = [
        "three": ["tree", "free", "thee", "sri", "3"],
        "four": ["for", "fore", "far", "4"],
        "eight": ["ate", "ache", "8"],
        "one": ["won", "wan", "1"],
        "two": ["to", "too", "do", "2"],
        "five": ["hive", "dive", "5"],
        "six": ["sex", "sick", "6"],
        "seven": ["heaven", "sever", "7"],
        "nine": ["wine", "mine", "sign", "9"],
        "ten": ["den", "tan", "10"]
    ]
    private static let cacheQueue = DispatchQueue(label: "com.snapinspect.ordinalMatcher.cache", attributes: .concurrent)
    
    // MARK: - Public Methods
    
    /// Creates multiple variations of spoken text using pattern-based noun+number extraction
    /// - Parameters:
    ///   - spokenText: The original spoken text
    ///   - contextItems: Optional list of items being matched against for pattern normalization
    /// - Returns: Array of text variations focusing on noun+number patterns
    public static func createVariations(_ spokenText: String, contextItems: [String] = []) -> [String] {
        guard !spokenText.isEmpty else { return [] }
        
        // Check cache first
        var cachedResult: [String]?
        cacheQueue.sync {
            cachedResult = variationCache[spokenText]
        }
        
        if let cached = cachedResult {
            return cached
        }
        
        var variations = [spokenText] // Always include the original
        
        // Add compound word variations first (e.g., "bath room" -> "bathroom")
        let compoundVariations = createCompoundWordVariations(spokenText)
        variations.append(contentsOf: compoundVariations)
        
        // Create ordinal-to-number mappings
        let ordinalToNumber = createOrdinalToNumberMapping()
        let writtenToNumber = SpeechNormalizer.singleDigits
        
        // Process all variations (original + compound variations) for number patterns
        let allVariationsToProcess = variations
        for baseVariation in allVariationsToProcess {
            // Extract noun+number patterns from spoken text
            let nounNumberPatterns = extractNounNumberPatterns(from: baseVariation, ordinalToNumber: ordinalToNumber, writtenToNumber: writtenToNumber, contextItems: contextItems)
            
            // Generate variations based on extracted patterns
            for pattern in nounNumberPatterns {
                // Replace the original pattern in the full text with normalized versions
                let patternVariations = createVariationsForPattern(pattern)
                for patternVariation in patternVariations {
                    // Replace the original pattern text with the new variation
                    let newVariation = baseVariation.replacingOccurrences(of: pattern.originalText, with: patternVariation)
                    if !variations.contains(newVariation) {
                        variations.append(newVariation)
                    }
                }
            }
            
            // Also create variations for standalone written numbers (e.g., "three" -> "3")
            let lowercased = baseVariation.lowercased()
            let itemTypes = extractItemTypes(from: contextItems)
            
            // Find ALL existing numbers in the text, not just item-associated ones
            var allExistingNumbers = Set<String>()
            let words = lowercased.components(separatedBy: .whitespacesAndNewlines)
            for (index, word) in words.enumerated() {
                // Skip words that contain $ (price context)
                if word.contains("$") {
                    continue
                }
                
                let cleanWord = word.trimmingCharacters(in: .punctuationCharacters)
                if let number = Int(cleanWord), number > 0 {
                    // Check if this number is in a non-item context
                    if !isNonItemNumber(String(number), in: lowercased, at: index) {
                        allExistingNumbers.insert(String(number))
                    }
                }
            }
            
            // Add variations for ordinals
            variations.append(contentsOf: createVariationsForOrdinals(lowercased, ordinalToNumber, existingNumbers: allExistingNumbers, itemTypes: itemTypes))
            
            // Add variations for written numbers
            variations.append(contentsOf: createVariationsForWrittenNumbers(lowercased, writtenToNumber, existingNumbers: allExistingNumbers, itemTypes: itemTypes))
            
            // Add variations for phonetic confusions (e.g., "for" -> "four" -> "4")
            // Always create phonetic variations for comprehensive matching
            variations.append(contentsOf: createVariationsForPhoneticConfusions(lowercased, writtenToNumber: writtenToNumber, itemTypes: itemTypes))
        }
        
        // Remove duplicates and empty strings
        let result = Array(Set(variations)).filter { !$0.isEmpty }
        
        // Cache the result
        cacheQueue.async(flags: .barrier) {
            variationCache[spokenText] = result
        }
        
        return result
    }
    
    /// Checks if a variation represents a number-based match
    /// - Parameters:
    ///   - spokenText: The original spoken text
    ///   - variation: The variation being tested
    ///   - itemName: The item name being matched against
    /// - Returns: True if this is a number-based match (ordinal or direct number)
    public static func isNumberBasedMatch(spokenText: String, variation: String, itemName: String) -> Bool {
        let spokenLower = spokenText.lowercased()
        let variationLower = variation.lowercased()
        let itemLower = itemName.lowercased()
        
        // Check if the original spoken text contains ordinal indicators
        let hasOrdinalWord = SpeechNormalizer.ordinals.keys.contains { spokenLower.contains($0) }
        let hasWrittenNumber = SpeechNormalizer.singleDigits.keys.contains { word in
            // Check if it's actually used as a number word, not part of another word
            let pattern = "\\b\(word)\\b"
            if let regex = try? NSRegularExpression(pattern: pattern, options: [.caseInsensitive]) {
                let range = NSRange(location: 0, length: spokenLower.utf16.count)
                return regex.firstMatch(in: spokenLower, options: [], range: range) != nil
            }
            return false
        }
        let hasNumericOrdinal = ["1st", "2nd", "3rd", "4th", "5th", "6th", "7th", "8th", "9th", "10th",
                                  "11th", "12th", "13th", "14th", "15th", "16th", "17th", "18th", "19th", "20th",
                                  "21st", "22nd", "23rd", "24th", "25th", "30th", "31st", "40th", "50th"].contains { spokenLower.contains($0) }
        
        // Direct number match (e.g., "bedroom 2")
        let hasDirectNumber = extractFirstNumber(from: spokenLower) != nil
        
        // If no number indicators at all, not a number-based match
        if !hasOrdinalWord && !hasWrittenNumber && !hasNumericOrdinal && !hasDirectNumber {
            return false
        }
        
        // Extract the base item type (e.g., "bedroom", "floor", "room", "level", etc.)
        let itemWords = itemLower.split(separator: " ").map(String.init)
        let variationWords = variationLower.split(separator: " ").map(String.init)
        
        // Find common base words between variation and item
        let commonBaseWords = Set(itemWords).intersection(Set(variationWords))
        
        // If no common words, can't be a match
        if commonBaseWords.isEmpty {
            return false
        }
        
        // Extract numbers from both variation and item
        if let variationNumber = extractFirstItemNumber(from: variationLower),
           let itemNumber = extractFirstItemNumber(from: itemLower) {
            // Both have numbers, they should match
            return variationNumber == itemNumber
        }
        
        // Special case: "item 1" or "first item" should match base "Item" (without number)
        if let variationNumber = extractFirstItemNumber(from: variationLower),
           variationNumber == "1" {
            // Check if item has no number (is the base item)
            let itemHasNumber = extractFirstItemNumber(from: itemLower) != nil
            if !itemHasNumber {
                // Check if the item is a simple base item that could logically be "item 1"
                if itemWords.count == 1 && commonBaseWords.contains(itemLower) {
                    return true
                }
                
                // Also handle cases where the item might have a simple prefix
                if itemWords.count <= 2 && commonBaseWords.count > 0 {
                    // Check if removing common modifiers leaves us with a base word
                    let modifiers = Set(["main", "primary", "base", "ground", "first"])
                    let filteredWords = itemWords.filter { !modifiers.contains($0) }
                    if filteredWords.count == 1 && commonBaseWords.contains(filteredWords[0]) {
                        return true
                    }
                }
            }
        }
        
        return false
    }
    
    /// Extracts the first number from a string (numeric digits only)
    public static func extractFirstNumber(from text: String) -> String? {
        guard let regex = numberRegex else { return nil }
        
        let matches = regex.matches(in: text, options: [], range: NSRange(text.startIndex..., in: text))
        if let match = matches.first {
            return String(text[Range(match.range, in: text)!])
        }
        return nil
    }
    
    /// Extracts the first number from a string that's likely an item reference (not a price/measurement)
    public static func extractFirstItemNumber(from text: String) -> String? {
        guard let regex = numberRegex else { return nil }
        
        let words = text.lowercased().split(separator: " ").map(String.init)
        let matches = regex.matches(in: text, options: [], range: NSRange(text.startIndex..., in: text))
        
        for match in matches {
            let numberRange = Range(match.range, in: text)!
            let number = String(text[numberRange])
            
            // Get the position of this number in the text
            let startIndex = text.distance(from: text.startIndex, to: numberRange.lowerBound)
            
            // Find which word contains this number
            var cumulativeLength = 0
            var wordIndex = -1
            for (idx, word) in words.enumerated() {
                if startIndex >= cumulativeLength && startIndex < cumulativeLength + word.count {
                    wordIndex = idx
                    break
                }
                cumulativeLength += word.count + 1 // +1 for space
            }
            
            if wordIndex >= 0 {
                // Use the existing isNonItemNumber logic to check context
                if !isNonItemNumber(number, in: text, at: wordIndex) {
                    return number
                }
            }
        }
        
        return nil
    }
    
    /// Extracts the first number from a string, including written forms
    public static func extractFirstNumberIncludingWritten(from text: String) -> String? {
        // First check for numeric digits
        if let numericResult = extractFirstNumber(from: text) {
            return numericResult
        }
        
        // Then check for written numbers
        let words = text.lowercased().components(separatedBy: .whitespacesAndNewlines)
        let writtenNumbers = SpeechNormalizer.singleDigits
        let ordinalNumbers = SpeechNormalizer.ordinals
        
        for word in words {
            let cleanWord = word.trimmingCharacters(in: .punctuationCharacters)
            
            // Check written numbers
            if let number = writtenNumbers[cleanWord] {
                return number
            }
            
            // Check ordinal numbers and extract the numeric part
            if let ordinal = ordinalNumbers[cleanWord] {
                // Extract just the number part (e.g., "1st" → "1")
                return ordinal.trimmingCharacters(in: .letters)
            }
        }
        
        return nil
    }
    
    /// Clears the variation cache (useful for testing or memory management)
    public static func clearCache() {
        cacheQueue.async(flags: .barrier) {
            variationCache.removeAll()
        }
    }
    
    /// Creates compound word variations (e.g., "bath room" -> "bathroom")
    private static func createCompoundWordVariations(_ text: String) -> [String] {
        var variations: [String] = []
        let words = text.lowercased().split(separator: " ").map(String.init)
        
        guard words.count >= 2 else { return variations }
        
        // Use the default inspection domain configuration for compound words
        let config = DomainKeywordConfiguration.inspectionDefaults
        
        // Check for known compounds that should be joined
        for i in 0..<(words.count - 1) {
            let word1 = words[i]
            let word2 = words[i + 1]
            
            // Check if this is a known compound
            if config.isCompound(word1, word2) {
                // Create variation with compound
                var compoundWords = words
                compoundWords[i] = word1 + word2
                compoundWords.remove(at: i + 1)
                let compoundVariation = compoundWords.joined(separator: " ")
                variations.append(compoundVariation)
            }
        }
        
        // Also check for compound words that should be split
        for (index, word) in words.enumerated() {
            if let splits = config.splitCompound(word) {
                var splitWords = words
                splitWords[index] = splits.0
                splitWords.insert(splits.1, at: index + 1)
                let splitVariation = splitWords.joined(separator: " ")
                variations.append(splitVariation)
            }
        }
        
        // Try joining consecutive pairs of words to form compounds
        // This helps with cases like "bath room" -> "bathroom" even if not in config
        for i in 0..<(words.count - 1) {
            let word1 = words[i]
            let word2 = words[i + 1]
            let joined = word1 + word2
            
            // Only add if it forms a reasonable compound (4-20 characters)
            if joined.count >= 4 && joined.count <= 20 {
                var compoundWords = words
                compoundWords[i] = joined
                compoundWords.remove(at: i + 1)
                let compoundVariation = compoundWords.joined(separator: " ")
                if !variations.contains(compoundVariation) {
                    variations.append(compoundVariation)
                }
            }
        }
        
        return variations
    }
    
    // MARK: - Pattern Data Structure
    
    /// Represents a noun+number pattern extracted from speech
    private struct NounNumberPattern {
        let noun: String           // Base noun (e.g., "bedroom", "window")
        let number: String         // Normalized number (e.g., "2", "3")
        let originalText: String   // Original text span (e.g., "second bedroom")
        let range: Range<String.Index> // Position in original text
    }
    
    // MARK: - Private Methods
    
    /// Extracts compound nouns from context items
    /// - Parameter items: List of items to analyze
    /// - Returns: Set of compound nouns found in items
    private static func extractCompoundNouns(from items: [String]) -> Set<String> {
        var compounds = Set<String>()
        
        for item in items {
            let words = item.lowercased().components(separatedBy: .whitespacesAndNewlines)
            
            // ENHANCED: Handle complex patterns like "Building 2 Floor 3"
            // Extract all possible compound patterns from the item
            for i in 0..<words.count {
                for j in i..<words.count {
                    let compound = words[i...j].joined(separator: " ")
                    compounds.insert(compound)
                }
            }
            
            // CRITICAL FIX: Enhanced handling for multi-number patterns
            // Handle patterns like "Building 2 Floor 3" by extracting sub-patterns
            for i in 0..<words.count {
                if let number = Int(words[i].trimmingCharacters(in: .punctuationCharacters)) {
                    // Found a number, extract the pattern before it
                    if i > 0 {
                        let beforeNumber = words[0..<i].joined(separator: " ")
                        compounds.insert(beforeNumber)
                        
                        // Also extract patterns after this number
                        if i < words.count - 1 {
                            let afterNumber = words[(i+1)...].joined(separator: " ")
                            compounds.insert(afterNumber)
                            
                            // And the combination without the number
                            let withoutNumber = words[0..<i] + words[(i+1)...]
                            if !withoutNumber.isEmpty {
                                compounds.insert(withoutNumber.joined(separator: " "))
                            }
                        }
                    }
                }
            }
            
            // If item has a number at the end, everything before it might be a compound noun
            if words.count >= 2 {
                let lastWord = words.last?.trimmingCharacters(in: .punctuationCharacters) ?? ""
                if Int(lastWord) != nil {
                    // Join all words except the number
                    let compound = words.dropLast().joined(separator: " ")
                    compounds.insert(compound)
                }
            }
            
            // Also look for items without numbers that might be compound nouns
            if words.count >= 2 && Int(words.last?.trimmingCharacters(in: .punctuationCharacters) ?? "") == nil {
                let compound = words.joined(separator: " ").lowercased()
                // Only add if it's a meaningful compound (not just articles)
                let firstWord = words[0]
                if !["the", "a", "an"].contains(firstWord) {
                    compounds.insert(compound)
                    
                    // Also add shorter versions of multi-word compounds
                    // This helps match "fire extinguisher" when the full item is "fire extinguisher station"
                    if words.count > 2 {
                        // Add all possible sub-compounds (from start)
                        for length in 2..<words.count {
                            let subCompound = words.prefix(length).joined(separator: " ").lowercased()
                            compounds.insert(subCompound)
                        }
                    }
                }
            }
        }
        
        return compounds
    }
    
    /// Extracts noun+number patterns from spoken text
    /// - Parameters:
    ///   - text: The spoken text to analyze
    ///   - ordinalToNumber: Dictionary mapping ordinals to numbers
    ///   - writtenToNumber: Dictionary mapping written numbers to digits
    ///   - contextItems: Optional list of items for compound noun detection
    /// - Returns: Array of extracted noun+number patterns
    private static func extractNounNumberPatterns(from text: String, ordinalToNumber: [String: String], writtenToNumber: [String: String], contextItems: [String] = []) -> [NounNumberPattern] {
        var patterns: [NounNumberPattern] = []
        let lowercased = text.lowercased()
        let words = lowercased.components(separatedBy: .whitespacesAndNewlines)
        
        // Extract compound nouns from context items
        let compoundNouns = extractCompoundNouns(from: contextItems)
        
        // First, look for compound noun patterns
        for compound in compoundNouns {
            let compoundWords = compound.split(separator: " ").map(String.init)
            if compoundWords.count >= 2 {
                // Look for this compound noun in the text
                for i in 0..<max(1, words.count - compoundWords.count + 1) {
                    var matches = true
                    for j in 0..<compoundWords.count {
                        if i + j >= words.count || words[i + j].trimmingCharacters(in: .punctuationCharacters) != compoundWords[j] {
                            matches = false
                            break
                        }
                    }
                    
                    if matches {
                        // Found the compound noun, now look for adjacent numbers
                        let compoundEndIndex = i + compoundWords.count - 1
                        
                        // Check before the compound noun (e.g., "third loading bay")
                        if i > 0 {
                            let prevWord = words[i - 1].trimmingCharacters(in: .punctuationCharacters)
                            var numberCandidate: String? = nil
                            
                            if let ordinalNumber = ordinalToNumber[prevWord] {
                                numberCandidate = ordinalNumber.trimmingCharacters(in: .letters)
                            } else if let writtenNumber = writtenToNumber[prevWord] {
                                // Check if this is a homophone that shouldn't be treated as a number
                                if !isLikelyHomophoneNotNumber(prevWord, in: text) {
                                    numberCandidate = writtenNumber
                                }
                            }
                            
                            if let number = numberCandidate, !number.isEmpty {
                                let originalText = "\(words[i - 1]) \(compoundWords.joined(separator: " "))"
                                let pattern = NounNumberPattern(
                                    noun: compound,
                                    number: number,
                                    originalText: originalText,
                                    range: text.startIndex..<text.endIndex
                                )
                                patterns.append(pattern)
                            }
                        }
                        
                        // Check after the compound noun (e.g., "loading bay 3")
                        if compoundEndIndex < words.count - 1 {
                            let nextWordRaw = words[compoundEndIndex + 1]
                            let nextWord = nextWordRaw.trimmingCharacters(in: .punctuationCharacters)
                            var numberCandidate: String? = nil
                            
                            // Check if the raw word contains $ (price context)
                            if nextWordRaw.contains("$") {
                                // Skip price numbers entirely
                                numberCandidate = nil
                            } else if let digitNumber = Int(nextWord), digitNumber > 0 {
                                // Check if this number is in a non-item context
                                if !isNonItemNumber(String(digitNumber), in: text, at: compoundEndIndex + 1) {
                                    numberCandidate = String(digitNumber)
                                }
                            } else if let ordinalNumber = ordinalToNumber[nextWord] {
                                numberCandidate = ordinalNumber.trimmingCharacters(in: .letters)
                            } else if let writtenNumber = writtenToNumber[nextWord] {
                                // Check if this is a homophone that shouldn't be treated as a number
                                if !isLikelyHomophoneNotNumber(nextWord, in: text) {
                                    numberCandidate = writtenNumber
                                }
                            }
                            
                            if let number = numberCandidate, !number.isEmpty {
                                let originalText = "\(compoundWords.joined(separator: " ")) \(words[compoundEndIndex + 1])"
                                let pattern = NounNumberPattern(
                                    noun: compound,
                                    number: number,
                                    originalText: originalText,
                                    range: text.startIndex..<text.endIndex
                                )
                                patterns.append(pattern)
                            }
                        }
                    }
                }
            }
        }
        
        // Look for "noun number X" pattern (e.g., "window number two")
        if words.count >= 3 {
            for i in 0..<words.count - 2 {
            let word1 = words[i].trimmingCharacters(in: .punctuationCharacters)
            let word2 = words[i + 1].trimmingCharacters(in: .punctuationCharacters)
            let word3 = words[i + 2].trimmingCharacters(in: .punctuationCharacters)
            
            if word2 == "number" && isLikelyNoun(word1) {
                var numberCandidate: String? = nil
                
                // Check if the raw word contains $ (price context)
                let word3Raw = words[i + 2]
                if word3Raw.contains("$") {
                    // Skip price numbers entirely
                    numberCandidate = nil
                } else if let digitNumber = Int(word3), digitNumber > 0 {
                    // Check if this number is in a non-item context
                    if !isNonItemNumber(String(digitNumber), in: text, at: i + 2) {
                        numberCandidate = String(digitNumber)
                    }
                } else if let ordinalNumber = ordinalToNumber[word3] {
                    numberCandidate = ordinalNumber.trimmingCharacters(in: .letters)
                } else if let writtenNumber = writtenToNumber[word3] {
                    // Check if this is a homophone that shouldn't be treated as a number
                    if !isLikelyHomophoneNotNumber(word3, in: text) {
                        numberCandidate = writtenNumber
                    }
                }
                
                if let number = numberCandidate, !number.isEmpty {
                    let originalText = "\(words[i]) \(words[i + 1]) \(words[i + 2])"
                    let pattern = NounNumberPattern(
                        noun: word1,
                        number: number,
                        originalText: originalText,
                        range: text.startIndex..<text.endIndex
                    )
                    patterns.append(pattern)
                }
            }
        }
        }
        
        // Look for ordinal+noun patterns (e.g., "second bedroom")
        for i in 0..<words.count - 1 {
            let word = words[i].trimmingCharacters(in: .punctuationCharacters)
            let nextWord = words[i + 1].trimmingCharacters(in: .punctuationCharacters)
            
            // Check if current word is an ordinal/written number
            var numberCandidate: String? = nil
            if let ordinalNumber = ordinalToNumber[word] {
                numberCandidate = ordinalNumber.trimmingCharacters(in: .letters)
            } else if let writtenNumber = writtenToNumber[word] {
                // Check if this is a homophone that shouldn't be treated as a number
                if !isLikelyHomophoneNotNumber(word, in: text) {
                    numberCandidate = writtenNumber
                }
            }
            
            // If we found a number and next word could be a noun
            if let number = numberCandidate, !number.isEmpty, isLikelyNoun(nextWord) {
                let originalText = "\(words[i]) \(words[i + 1])"
                let pattern = NounNumberPattern(
                    noun: nextWord,
                    number: number,
                    originalText: originalText,
                    range: text.startIndex..<text.endIndex
                )
                patterns.append(pattern)
            }
        }
        
        // Look for noun+number patterns (e.g., "bedroom 3", "bedroom three")
        for i in 0..<words.count - 1 {
            let word = words[i].trimmingCharacters(in: .punctuationCharacters)
            let nextWord = words[i + 1].trimmingCharacters(in: .punctuationCharacters)
            
            // Check if next word is a number in any form
            var numberCandidate: String? = nil
            let nextWordRaw = words[i + 1]
            if nextWordRaw.contains("$") {
                // Skip price numbers entirely
                numberCandidate = nil
            } else if let digitNumber = Int(nextWord), digitNumber > 0 {
                // Check if this number is in a non-item context
                if !isNonItemNumber(String(digitNumber), in: text, at: i + 1) {
                    numberCandidate = String(digitNumber)
                }
            } else if let ordinalNumber = ordinalToNumber[nextWord] {
                numberCandidate = ordinalNumber.trimmingCharacters(in: .letters)
            } else if let writtenNumber = writtenToNumber[nextWord] {
                // Check if this is a homophone that shouldn't be treated as a number
                if !isLikelyHomophoneNotNumber(nextWord, in: text) {
                    numberCandidate = writtenNumber
                }
            }
            
            // If we found a number and current word could be a noun
            if let number = numberCandidate, !number.isEmpty, isLikelyNoun(word) {
                let originalText = "\(words[i]) \(words[i + 1])"
                let pattern = NounNumberPattern(
                    noun: word,
                    number: number,
                    originalText: originalText,
                    range: text.startIndex..<text.endIndex
                )
                patterns.append(pattern)
            }
        }
        
        return patterns
    }
    
    /// Determines if a word is likely to be a noun (simple heuristic)
    /// - Parameter word: The word to check
    /// - Returns: True if the word seems like it could be a noun
    private static func isLikelyNoun(_ word: String) -> Bool {
        // Simple heuristic: avoid obvious non-nouns
        let nonNouns = Set(["the", "a", "an", "and", "or", "but", "to", "from", "with", "has", "have", "had", "is", "are", "was", "were", "will", "would", "could", "should", "can", "may", "might"])
        return !nonNouns.contains(word.lowercased()) && word.count > 1
    }
    
    /// Checks if a number is in a non-item context (price, time, measurement, etc.)
    /// - Parameters:
    ///   - numberText: The number text to check
    ///   - fullText: The full text for context
    ///   - wordIndex: Optional specific index of the number in the text
    /// - Returns: True if this number is likely not an item reference
    public static func isNonItemNumber(_ numberText: String, in fullText: String, at wordIndex: Int? = nil) -> Bool {
        let words = fullText.lowercased().components(separatedBy: .whitespacesAndNewlines)
        
        // Find the index of the number if not provided
        let numberIndex = wordIndex ?? words.firstIndex { word in
            word.trimmingCharacters(in: .punctuationCharacters) == numberText
        } ?? -1
        
        guard numberIndex >= 0 && numberIndex < words.count else { return false }
        
        // Check if the current word itself contains $ (e.g., "$2")
        let currentWord = words[numberIndex]
        if currentWord.contains("$") {
            return true
        }
        
        // Check for price context (e.g., "$2", "costs 2", "price 2")
        if numberIndex > 0 {
            let prevWord = words[numberIndex - 1]
            let priceIndicators = ["$", "dollar", "dollars", "costs", "price", "priced", "pay", "charge", "charged", "fee"]
            if priceIndicators.contains(where: { prevWord.contains($0) }) {
                return true
            }
        }
        
        // Check for time context (e.g., "3 pm", "2 o'clock", "at 3")
        if numberIndex < words.count - 1 {
            let nextWord = words[numberIndex + 1]
            let timeIndicators = ["am", "pm", "o'clock", "oclock", "hour", "hours", "minute", "minutes"]
            if timeIndicators.contains(nextWord) {
                return true
            }
        }
        
        // Check for "at TIME" pattern
        if numberIndex > 0 && words[numberIndex - 1] == "at" {
            // Check if this is likely a time reference
            if let number = Int(numberText), number >= 1 && number <= 12 {
                // Could be "at 3" meaning "at 3 o'clock"
                // But only if not followed by an item noun
                if numberIndex == words.count - 1 || !isLikelyNoun(words[numberIndex + 1]) {
                    return true
                }
            }
        }
        
        // Check for measurement context (e.g., "2 inches", "3 feet")
        if numberIndex < words.count - 1 {
            let nextWord = words[numberIndex + 1]
            let measurementUnits = ["inch", "inches", "foot", "feet", "meter", "meters", "cm", "mm", "kg", "gram", "grams", "pound", "pounds", "mile", "miles", "kilometer", "kilometers"]
            if measurementUnits.contains(nextWord) {
                return true
            }
        }
        
        // Check for quantity context that's not an item reference (e.g., "2 times", "3 people")
        if numberIndex < words.count - 1 {
            let nextWord = words[numberIndex + 1]
            let quantityNouns = ["times", "people", "persons", "days", "weeks", "months", "years", "percent", "%"]
            if quantityNouns.contains(nextWord) {
                return true
            }
        }
        
        return false
    }
    
    /// Checks if a word is a likely homophone that shouldn't be treated as a number
    /// - Parameters:
    ///   - word: The word to check
    ///   - fullText: The full text for context
    ///   - wordIndex: Optional specific index of the word in the text
    /// - Returns: True if this is likely a homophone that shouldn't be a number
    private static func isLikelyHomophoneNotNumber(_ word: String, in fullText: String, at wordIndex: Int? = nil) -> Bool {
        let commonHomophones = ["to", "too", "for", "won", "ate"]
        
        // If the word is NOT a common homophone, it's definitely not a homophone issue
        guard commonHomophones.contains(word.lowercased()) else {
            return false
        }
        
        let words = fullText.lowercased().components(separatedBy: .whitespacesAndNewlines)
        
        // Special handling for "to" - it's usually a preposition, rarely "two"
        if word.lowercased() == "to" {
            // Use provided index or find first occurrence
            let toIndex = wordIndex ?? words.firstIndex(of: "to") ?? -1
            
            if toIndex >= 0 && toIndex < words.count {
                // Check if "to" is followed by "the" or other determiners (preposition use)
                if toIndex + 1 < words.count {
                    let nextWord = words[toIndex + 1]
                    let prepositionFollowers = ["the", "a", "an", "this", "that", "be", "go", "get", "have", "do"]
                    if prepositionFollowers.contains(nextWord) {
                        return true // It's used as a preposition, not "two"
                    }
                }
                
                // Check if "to" is preceded by verbs (e.g., "going to", "want to")
                if toIndex > 0 {
                    let prevWord = words[toIndex - 1]
                    let verbPrecursors = ["going", "go", "went", "want", "need", "have", "has", "had", 
                                         "trying", "try", "tried", "used", "about", "how", "where", 
                                         "when", "why", "what", "who", "seems", "appears", "looks"]
                    if verbPrecursors.contains(prevWord) {
                        return true // It's used as a preposition/infinitive marker
                    }
                }
                
                // If "to" appears between a verb and a noun, it's likely a preposition
                // e.g., "going to the floor"
                if toIndex > 0 && toIndex + 1 < words.count {
                    let prevWord = words[toIndex - 1]
                    let nextWord = words[toIndex + 1]
                    // If previous word is verb-like and next word is noun-like or determiner
                    if !isLikelyNoun(prevWord) && (isLikelyNoun(nextWord) || ["the", "a", "an"].contains(nextWord)) {
                        return true
                    }
                }
                
                // Only treat "to" as "two" if it's clearly in a numeric context
                // e.g., "bedroom to" or "item to" (where "to" might mean "two")
                if toIndex > 0 {
                    let prevWord = words[toIndex - 1]
                    if isLikelyNoun(prevWord) && toIndex == words.count - 1 {
                        return false // Could be "bedroom two" spoken as "bedroom to"
                    }
                }
            }
            
            // Default: "to" is almost always a preposition
            return true
        }
        
        // Special handling for "for" - it's often used as "four" in speech
        if word.lowercased() == "for" {
            // Check if it's used in a prepositional context (e.g., "for inspection", "for testing")
            if let forIndex = words.firstIndex(of: "for") {
                // Check the word before "for" to see if it's likely an item name
                if forIndex > 0 {
                    let prevWord = words[forIndex - 1]
                    // If previous word looks like an item name (like "bedroom"), "for" likely means "four"
                    // even if followed by "inspection" or similar words
                    if isLikelyNoun(prevWord) {
                        return false // Likely means "four"
                    }
                }
                
                // Check the word after "for"
                if forIndex + 1 < words.count {
                    let nextWord = words[forIndex + 1]
                    // Only treat as preposition if NOT preceded by a noun
                    let prepositionFollowers = ["the", "a", "an", "this", "that"]
                    if prepositionFollowers.contains(nextWord) {
                        return true // It's used as a preposition, not a number
                    }
                }
            }
        }
        
        // For other homophones, be more conservative
        // Only treat as non-number if there's clear context suggesting it's not a number
        return false
    }
    
    /// Creates variations for a single noun+number pattern
    /// - Parameter pattern: The pattern to create variations for
    /// - Returns: Array of text variations for this pattern
    private static func createVariationsForPattern(_ pattern: NounNumberPattern) -> [String] {
        var variations: [String] = []
        
        // Create different formats for the pattern
        variations.append("\(pattern.noun) \(pattern.number)")          // "bedroom 2"
        
        // Only add reversed pattern for certain numbers (not common in English)
        // variations.append("\(pattern.number) \(pattern.noun)")       // Removed - causes issues
        
        // Don't convert numbers back to written forms in variations
        // This prevents homophone confusion issues
        
        return variations
    }
    
    /// Extracts item types from a list of context items for dynamic keyword detection
    /// - Parameter items: List of item names to analyze
    /// - Returns: Set of detected item type keywords
    private static func extractItemTypes(from items: [String]) -> Set<String> {
        var itemTypes = Set<String>()
        
        for item in items {
            let words = item.lowercased().components(separatedBy: .whitespacesAndNewlines)
            
            // Pattern: "word number" or "word1 word2 number" (e.g., "Bedroom 2", "Office Area 3")
            if words.count >= 2 {
                let lastWord = words.last?.trimmingCharacters(in: .punctuationCharacters) ?? ""
                if Int(lastWord) != nil {
                    // Everything except the last word forms the item type
                    let itemType = words.dropLast().joined(separator: " ")
                    itemTypes.insert(itemType)
                    
                    // Also add individual words for partial matching
                    words.dropLast().forEach { itemTypes.insert($0) }
                }
            }
            
            // Single word items (could be base items like "Bedroom" implying "Bedroom 1")
            if words.count == 1 {
                itemTypes.insert(words[0].lowercased())
            }
            
            // Multi-word items without numbers (e.g., "Main Office", "Storage Area")
            if words.count > 1 && Int(words.last?.trimmingCharacters(in: .punctuationCharacters) ?? "") == nil {
                let itemType = words.joined(separator: " ").lowercased()
                itemTypes.insert(itemType)
                // Also add individual significant words
                words.forEach { word in
                    let clean = word.lowercased()
                    // Skip common articles and prepositions
                    if !["a", "an", "the", "of", "in", "on", "at", "to", "for", "with"].contains(clean) {
                        itemTypes.insert(clean)
                    }
                }
            }
        }
        
        return itemTypes
    }
    
    /// Finds item-associated explicit numbers in the text (not incidental numbers)
    /// - Parameters:
    ///   - text: The text to search
    ///   - ordinalToNumber: Dictionary mapping ordinals to numbers
    ///   - writtenToNumber: Dictionary mapping written numbers to digits
    ///   - itemTypes: Item types for context-aware detection
    /// - Returns: Set of normalized numbers that are directly associated with item types
    private static func findItemAssociatedNumbers(in text: String, ordinalToNumber: [String: String], writtenToNumber: [String: String], itemTypes: Set<String>) -> Set<String> {
        var foundNumbers = Set<String>()
        let words = text.lowercased().components(separatedBy: .whitespacesAndNewlines)
        
        // Only consider numbers that are near item types (within 2 words)
        for (index, word) in words.enumerated() {
            let cleanWord = word.trimmingCharacters(in: .punctuationCharacters)
            var numberCandidate: String? = nil
            
            // Check if this word is a number in any form
            if word.contains("$") {
                // Skip price numbers entirely
                numberCandidate = nil
            } else if let digitNumber = Int(cleanWord), digitNumber > 0 {
                // Check if this number is in a non-item context
                if !isNonItemNumber(String(digitNumber), in: text, at: index) {
                    numberCandidate = String(digitNumber)
                }
            } else if let numberOrdinal = ordinalToNumber[cleanWord] {
                // Extract number from ordinal form (e.g., "1st" -> "1")
                let number = numberOrdinal.trimmingCharacters(in: .letters)
                if !number.isEmpty {
                    numberCandidate = number
                }
            } else if let number = writtenToNumber[cleanWord] {
                numberCandidate = number
            }
            
            // If we found a number candidate, check if it's directly adjacent to an item type
            if let candidate = numberCandidate {
                // Only check immediately adjacent positions (±1) for direct association
                let adjacentRange = max(0, index - 1)..<min(words.count, index + 2)
                var directlyAssociated = false
                
                for i in adjacentRange where i != index {
                    let contextWord = words[i].lowercased()
                    for itemType in itemTypes {
                        if contextWord.contains(itemType) || itemType.contains(contextWord) {
                            directlyAssociated = true
                            break
                        }
                    }
                    if directlyAssociated { break }
                }
                
                if directlyAssociated {
                    foundNumbers.insert(candidate)
                }
            }
        }
        
        // 3. Find phonetically confused numbers (context-aware)
        if !itemTypes.isEmpty {
            for (index, word) in words.enumerated() {
                let cleanWord = word.trimmingCharacters(in: .punctuationCharacters).lowercased()
                
                // Check if this word might be a phonetically confused number
                for (correctNumber, confusions) in phoneticConfusions {
                    if confusions.contains(cleanWord) {
                        // Found a potential phonetic confusion, check if it's near an item type
                        let searchRange = max(0, index - 2)..<min(words.count, index + 3)
                        
                        for i in searchRange where i != index {
                            let contextWord = words[i].lowercased()
                            for itemType in itemTypes {
                                if contextWord.contains(itemType) || itemType.contains(contextWord) {
                                    // This confusion is near an item type, treat it as the intended number
                                    if let number = writtenToNumber[correctNumber] {
                                        foundNumbers.insert(number)
                                    }
                                    break
                                }
                            }
                        }
                    }
                }
            }
        }
        
        return foundNumbers
    }
    
    /// Checks if a word at a given position should be replaced based on item-associated numbers
    /// - Parameters:
    ///   - index: Position of the word being considered for replacement
    ///   - words: Array of words in the text
    ///   - existingNumbers: Set of numbers that are directly associated with item types
    ///   - itemTypes: Dynamically extracted item type keywords
    /// - Returns: True if the word should be replaced, false otherwise
    private static func shouldReplaceWord(at index: Int, in words: [String], existingNumbers: Set<String>, itemTypes: Set<String>) -> Bool {
        // Check if this word is near an item type that already has a number
        let searchRange = max(0, index - 2)..<min(words.count, index + 3)
        var nearItemWithNumber = false
        
        for i in searchRange where i != index {
            let contextWord = words[i].lowercased()
            // Check if this word is an item type
            for itemType in itemTypes {
                if contextWord.contains(itemType) || itemType.contains(contextWord) {
                    // Found an item type near this position
                    // Now check if there's already a number associated with this item type
                    for j in max(0, i - 1)..<min(words.count, i + 2) {
                        if existingNumbers.contains(words[j].trimmingCharacters(in: .punctuationCharacters)) {
                            nearItemWithNumber = true
                            break
                        }
                    }
                    if nearItemWithNumber { break }
                }
            }
            if nearItemWithNumber { break }
        }
        
        // Only replace if NOT near an item that already has a number
        return !nearItemWithNumber
    }
    
    /// Creates ordinal-to-number mappings from existing ordinals dictionary
    private static func createOrdinalToNumberMapping() -> [String: String] {
        var mapping: [String: String] = [:]
        for (ordinal, ordinalForm) in SpeechNormalizer.ordinals {
            let numberString = ordinalForm.replacingOccurrences(of: "st", with: "")
                .replacingOccurrences(of: "nd", with: "")
                .replacingOccurrences(of: "rd", with: "")
                .replacingOccurrences(of: "th", with: "")
            mapping[ordinal] = numberString
        }
        return mapping
    }
    
    /// Creates numeric ordinal-to-number mappings
    private static func createNumericOrdinalMapping() -> [String: String] {
        var mapping: [String: String] = [:]
        for (_, ordinalForm) in SpeechNormalizer.ordinals {
            let numberString = ordinalForm.replacingOccurrences(of: "st", with: "")
                .replacingOccurrences(of: "nd", with: "")
                .replacingOccurrences(of: "rd", with: "")
                .replacingOccurrences(of: "th", with: "")
            mapping[ordinalForm] = numberString
        }
        return mapping
    }
    
    /// Creates variations for ordinal patterns (first, second, etc.)
    private static func createVariationsForOrdinals(_ lowercased: String, _ ordinalToNumber: [String: String], existingNumbers: Set<String>, itemTypes: Set<String>) -> [String] {
        var variations: [String] = []
        let words = lowercased.components(separatedBy: .whitespacesAndNewlines)
        
        for (ordinal, number) in ordinalToNumber {
            if lowercased.contains(ordinal) {
                // Handle word-by-word replacement with context awareness
                var newWords = words
                var shouldCreateVariation = false
                
                for i in 0..<newWords.count {
                    if newWords[i] == ordinal && shouldReplaceWord(at: i, in: words, existingNumbers: existingNumbers, itemTypes: itemTypes) {
                        newWords[i] = number
                        shouldCreateVariation = true
                    }
                }
                
                if shouldCreateVariation {
                    variations.append(newWords.joined(separator: " "))
                    
                    // Special handling for item patterns - convert "ordinal item" to "item number"
                    variations.append(contentsOf: createItemNumberVariations(lowercased, ordinal: ordinal, number: number))
                }
            }
        }
        return variations
    }
    
    /// Creates variations for written numbers (one, two, etc.)
    private static func createVariationsForWrittenNumbers(_ lowercased: String, _ writtenToNumber: [String: String], existingNumbers: Set<String>, itemTypes: Set<String>) -> [String] {
        var variations: [String] = []
        let words = lowercased.components(separatedBy: .whitespacesAndNewlines)
        
        for (written, number) in writtenToNumber {
            if lowercased.contains(written) {
                // Handle word-by-word replacement with context awareness
                var newWords = words
                var shouldCreateVariation = false
                
                for i in 0..<newWords.count {
                    if newWords[i] == written {
                        // Check if this specific instance should be replaced
                        if !isLikelyHomophoneNotNumber(written, in: words.joined(separator: " "), at: i) &&
                           shouldReplaceWord(at: i, in: words, existingNumbers: existingNumbers, itemTypes: itemTypes) {
                            newWords[i] = number
                            shouldCreateVariation = true
                        }
                    }
                }
                
                if shouldCreateVariation {
                    variations.append(newWords.joined(separator: " "))
                    
                    // Special handling for item patterns - convert "item written" to "item number"
                    // Only do this if the written word is actually likely to be a number
                    if !isLikelyHomophoneNotNumber(written, in: lowercased) {
                        variations.append(contentsOf: createItemNumberVariations(lowercased, ordinal: written, number: number))
                    }
                }
            }
        }
        return variations
    }
    
    /// Creates variations for numeric ordinals (1st, 2nd, etc.)
    private static func createVariationsForNumericOrdinals(_ lowercased: String, _ numericOrdinalToNumber: [String: String], existingNumbers: Set<String>, itemTypes: Set<String>) -> [String] {
        var variations: [String] = []
        let words = lowercased.components(separatedBy: .whitespacesAndNewlines)
        
        for (numOrdinal, number) in numericOrdinalToNumber {
            if lowercased.contains(numOrdinal) {
                // Handle word-by-word replacement with context awareness
                var newWords = words
                var shouldCreateVariation = false
                
                for i in 0..<newWords.count {
                    if newWords[i] == numOrdinal && shouldReplaceWord(at: i, in: words, existingNumbers: existingNumbers, itemTypes: itemTypes) {
                        newWords[i] = number
                        shouldCreateVariation = true
                    }
                }
                
                if shouldCreateVariation {
                    variations.append(newWords.joined(separator: " "))
                    
                    // Special handling for item patterns - convert "numOrdinal item" to "item number"
                    variations.append(contentsOf: createItemNumberVariations(lowercased, ordinal: numOrdinal, number: number))
                }
            }
        }
        return variations
    }
    
    /// Creates variations for phonetic confusions using linguistic analysis
    /// - Parameters:
    ///   - lowercased: The lowercased text
    ///   - writtenToNumber: Dictionary mapping written numbers to digits
    ///   - itemTypes: Set of item types for context awareness
    /// - Returns: Array of variations with phonetic confusions corrected
    private static func createVariationsForPhoneticConfusions(_ lowercased: String, writtenToNumber: [String: String], itemTypes: Set<String>) -> [String] {
        var variations: [String] = []
        let words = lowercased.components(separatedBy: .whitespacesAndNewlines)
        
        // Analyze the entire phrase for linguistic context
        let phraseContext = analyzePhraseContext(lowercased)
        
        // Process each potential homophone
        for (correctNumber, confusions) in phoneticConfusions {
            for confusion in confusions {
                // Find all occurrences of this confusion word
                for (index, word) in words.enumerated() {
                    let cleanWord = word.trimmingCharacters(in: .punctuationCharacters)
                    
                    guard cleanWord == confusion else { continue }
                    
                    // Determine if this position should have a number based on linguistic context
                    if shouldConvertHomophoneAtPosition(index, in: words, phraseContext: phraseContext) {
                        variations.append(contentsOf: createHomophoneVariations(
                            at: index,
                            withNumber: correctNumber,
                            in: words,
                            writtenToNumber: writtenToNumber
                        ))
                    }
                }
            }
        }
        
        return Array(Set(variations)) // Remove duplicates
    }
    
    /// Analyzes phrase context using NLP to understand the linguistic structure
    private static func analyzePhraseContext(_ text: String) -> PhraseContext {
        let tagger = NLTagger(tagSchemes: [.lexicalClass, .nameType])
        tagger.string = text
        
        var context = PhraseContext()
        let options: NLTagger.Options = [.omitWhitespace, .omitPunctuation]
        
        tagger.enumerateTags(in: text.startIndex..<text.endIndex, unit: .word, scheme: .lexicalClass, options: options) { tag, tokenRange in
            if let tag = tag {
                let word = String(text[tokenRange])
                context.wordTags[word] = tag
                
                // Track sequences of noun + potential number position
                if tag == .noun {
                    context.nounPositions.append(text.distance(from: text.startIndex, to: tokenRange.lowerBound))
                }
            }
            return true
        }
        
        return context
    }
    
    /// Determines if a homophone at a given position should be converted to a number
    private static func shouldConvertHomophoneAtPosition(_ index: Int, in words: [String], phraseContext: PhraseContext) -> Bool {
        guard index > 0 else { return false }
        
        let prevWord = words[index - 1].trimmingCharacters(in: .punctuationCharacters)
        
        // ENHANCED LOGIC: Check if there are clear numbers in the text that should take precedence
        let text = words.joined(separator: " ")
        let clearNumbers = extractAllClearNumbers(from: text)
        
        // If there are clear numbers, be more conservative about homophone conversion
        if !clearNumbers.isEmpty {
            // Only convert homophone if it's clearly in a number context
            return isInClearNumberContext(index: index, in: words, clearNumbers: clearNumbers)
        }
        
        // Check if previous word is a countable noun
        if let tag = phraseContext.wordTags[prevWord], tag == .noun {
            // Additional check: is this noun typically enumerated?
            return isEnumerableNoun(prevWord, in: phraseContext)
        }
        
        // Check for patterns like "number two", "item three"
        if index > 1 {
            let twoWordsBack = words[index - 2].trimmingCharacters(in: .punctuationCharacters).lowercased()
            if ["number", "item", "unit", "section", "part"].contains(twoWordsBack) {
                return true
            }
        }
        
        return false
    }
    
    /// Extracts all clear numbers from text (not homophones)
    private static func extractAllClearNumbers(from text: String) -> [String] {
        var numbers: [String] = []
        
        // Extract numeric digits
        if let regex = numberRegex {
            let matches = regex.matches(in: text, options: [], range: NSRange(text.startIndex..., in: text))
            for match in matches {
                let number = String(text[Range(match.range, in: text)!])
                numbers.append(number)
            }
        }
        
        // Extract written numbers
        let writtenNumbers = SpeechNormalizer.singleDigits
        for (word, number) in writtenNumbers {
            if text.contains(word) {
                numbers.append(number)
            }
        }
        
        return numbers
    }
    
    /// Checks if a homophone is in a clear number context (should be converted)
    private static func isInClearNumberContext(index: Int, in words: [String], clearNumbers: [String]) -> Bool {
        // If there are clear numbers, only convert homophone if it's in a very clear number context
        // This prevents "bedroom 3. To" from becoming "bedroom 3. Two"
        
        let currentWord = words[index].trimmingCharacters(in: .punctuationCharacters).lowercased()
        
        // Check if this homophone appears right after a clear number
        if index > 0 {
            let prevWord = words[index - 1].trimmingCharacters(in: .punctuationCharacters)
            if clearNumbers.contains(prevWord) {
                // Clear number followed by homophone - don't convert the homophone
                return false
            }
        }
        
        // Check if this homophone appears right before a clear number
        if index < words.count - 1 {
            let nextWord = words[index + 1].trimmingCharacters(in: .punctuationCharacters)
            if clearNumbers.contains(nextWord) {
                // Homophone followed by clear number - don't convert the homophone
                return false
            }
        }
        
        // Check if this homophone is separated from clear numbers by punctuation
        let text = words.joined(separator: " ")
        let homophonePatterns = ["to", "too", "for", "won", "ate"]
        
        for homophone in homophonePatterns {
            if currentWord == homophone {
                // Look for patterns like "3. to" or "3, to" where we shouldn't convert
                let patterns = ["\(clearNumbers.joined(separator: "|")). [.,] \(homophone)"]
                for pattern in patterns {
                    if let regex = try? NSRegularExpression(pattern: pattern, options: [.caseInsensitive]) {
                        let range = NSRange(location: 0, length: text.utf16.count)
                        if regex.firstMatch(in: text, options: [], range: range) != nil {
                            return false
                        }
                    }
                }
            }
        }
        
        // Default: allow conversion if in clear number context
        return true
    }
    
    /// Checks if a noun is typically enumerable in inspection contexts
    private static func isEnumerableNoun(_ noun: String, in context: PhraseContext) -> Bool {
        // Use linguistic heuristics to determine if this noun is countable and typically enumerated
        
        // Check if it's a proper noun (less likely to be enumerated)
        let tagger = NLTagger(tagSchemes: [.nameType])
        tagger.string = noun
        var isProperNoun = false
        
        tagger.enumerateTags(in: noun.startIndex..<noun.endIndex, unit: .word, scheme: .nameType) { tag, _ in
            if tag != nil {
                isProperNoun = true
            }
            return false
        }
        
        if isProperNoun { return false }
        
        // Check if the noun appears multiple times in the context (suggesting enumeration)
        let nounCount = context.wordTags.filter { $0.key.lowercased() == noun.lowercased() }.count
        if nounCount > 1 { return true }
        
        // Check morphology - plural forms suggest countability
        let lemmatizer = NLTagger(tagSchemes: [.lemma])
        lemmatizer.string = noun
        var isCountable = true
        
        lemmatizer.enumerateTags(in: noun.startIndex..<noun.endIndex, unit: .word, scheme: .lemma) { tag, _ in
            if let lemma = tag?.rawValue {
                // If lemma is different from original, it might be plural
                isCountable = lemma != noun.lowercased()
            }
            return false
        }
        
        return isCountable
    }
    
    /// Creates variations with homophone replacements
    private static func createHomophoneVariations(at index: Int, withNumber correctNumber: String, in words: [String], writtenToNumber: [String: String]) -> [String] {
        var variations: [String] = []
        var correctedWords = words
        
        // Create variation with written number
        correctedWords[index] = correctNumber
        variations.append(correctedWords.joined(separator: " "))
        
        // Create variation with digit if available
        if let digitNumber = writtenToNumber[correctNumber] {
            correctedWords[index] = digitNumber
            variations.append(correctedWords.joined(separator: " "))
        }
        
        return variations
    }
    
    /// Context information for phrase analysis
    private struct PhraseContext {
        var wordTags: [String: NLTag] = [:]
        var nounPositions: [Int] = []
    }
    
    /// Creates variations for item patterns like "second bedroom" → "bedroom 2"
    private static func createItemNumberVariations(_ text: String, ordinal: String, number: String) -> [String] {
        var variations: [String] = []
        let words = text.split(separator: " ").map(String.init)
        
        // Find all occurrences of the ordinal in the text
        for (index, word) in words.enumerated() {
            if word == ordinal {
                // Pattern 1: "ordinal item" → "item number"
                if index + 1 < words.count {
                    let itemType = words[index + 1]
                    let pattern = "\(ordinal) \(itemType)"
                    let replacement = "\(itemType) \(number)"
                    if text.contains(pattern) {
                        let variation = text.replacingOccurrences(of: pattern, with: replacement)
                        if !variations.contains(variation) {
                            variations.append(variation)
                        }
                    }
                }
                
                // Pattern 2: "ordinal item subtype" → "item subtype number"
                if index + 2 < words.count {
                    let itemType = "\(words[index + 1]) \(words[index + 2])"
                    let pattern = "\(ordinal) \(itemType)"
                    let replacement = "\(itemType) \(number)"
                    if text.contains(pattern) {
                        let variation = text.replacingOccurrences(of: pattern, with: replacement)
                        if !variations.contains(variation) {
                            variations.append(variation)
                        }
                    }
                }
                
                // Pattern 3: "item ordinal" → "item number"
                if index > 0 {
                    let itemType = words[index - 1]
                    let pattern = "\(itemType) \(ordinal)"
                    let replacement = "\(itemType) \(number)"
                    if text.contains(pattern) {
                        let variation = text.replacingOccurrences(of: pattern, with: replacement)
                        if !variations.contains(variation) {
                            variations.append(variation)
                        }
                    }
                }
                
                // Pattern 4: "subtype item ordinal" → "subtype item number"
                if index > 1 {
                    let itemType = "\(words[index - 2]) \(words[index - 1])"
                    let pattern = "\(itemType) \(ordinal)"
                    let replacement = "\(itemType) \(number)"
                    if text.contains(pattern) {
                        let variation = text.replacingOccurrences(of: pattern, with: replacement)
                        if !variations.contains(variation) {
                            variations.append(variation)
                        }
                    }
                }
            }
        }
        
        return variations
    }
}
