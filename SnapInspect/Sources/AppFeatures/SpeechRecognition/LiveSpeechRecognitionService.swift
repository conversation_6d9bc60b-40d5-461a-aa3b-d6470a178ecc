import AVFoundation
import Foundation
import Speech
#if canImport(UIKit)
import UIKit
#endif

// MARK: - Live Speech Recognition Service

public struct LiveSpeechRecognitionService {
    public var requestAuthorization: @Sendable () async -> SFSpeechRecognizerAuthorizationStatus
    public var startContinuousRecognition: @Sendable (_ language: String?) async throws -> AsyncStream<LiveSpeechUpdate>
    public var stopContinuousRecognition: @Sendable () async -> Void
    public var isAvailable: @Sendable () -> Bool
    public var generateSRTContent: @Sendable () async -> String
}

public enum LiveSpeechUpdate: Equatable {
    case partialText(String, timestamp: TimeInterval)
    case finalText(String, timestamp: TimeInterval)
    case error(String)
    case recognitionRestarted
    case availabilityChanged(Bool)
}

// MARK: - Static Service Instance

public extension LiveSpeechRecognitionService {
    static let shared = LiveSpeechRecognitionService(
        requestAuthorization: {
            await withCheckedContinuation { continuation in
                SFSpeechRecognizer.requestAuthorization { status in
                    continuation.resume(returning: status)
                }
            }
        },
        startContinuousRecognition: { language in
            try await LiveSpeechRecognitionManager.shared.startContinuousRecognition(language: language)
        },
        stopContinuousRecognition: {
            await LiveSpeechRecognitionManager.shared.stopContinuousRecognition()
        },
        isAvailable: {
            SFSpeechRecognizer()?.isAvailable ?? false
        },
        generateSRTContent: {
            await LiveSpeechRecognitionManager.shared.generateSRTContent()
        }
    )
}

// MARK: - Live Speech Recognition Manager

@MainActor
final class LiveSpeechRecognitionManager: NSObject {
    static let shared = LiveSpeechRecognitionManager()
    
    private var recognizer: SFSpeechRecognizer?
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private var audioEngine: AVAudioEngine?
    private var inputNode: AVAudioInputNode?
    private var continuations: [AsyncStream<LiveSpeechUpdate>.Continuation] = []
    
    // Continuous recognition state
    private var isRecognizing = false
    private var sessionStartTime: Date?
    private var transcriptionSegments: [TranscriptionSegment] = []
    private var currentSegmentStartTime: TimeInterval = 0
    
    // Configuration
    private let segmentDuration: TimeInterval = 5.0
    
    override private init() {
        super.init()
        recognizer = SFSpeechRecognizer(locale: Locale.current)
        recognizer?.delegate = self
    }
    
    func startContinuousRecognition(language: String? = nil) async throws -> AsyncStream<LiveSpeechUpdate> {
        await stopContinuousRecognition()
        
        if let language = language {
            let locale = Locale(identifier: language)
            recognizer = SFSpeechRecognizer(locale: locale)
            recognizer?.delegate = self
        }
        
        guard let recognizer = recognizer, recognizer.isAvailable else {
            throw LiveSpeechRecognitionError.recognizerNotAvailable
        }
        
        isRecognizing = true
        sessionStartTime = Date()
        currentSegmentStartTime = 0
        transcriptionSegments.removeAll()
        
        try await configureAudioSession()
        
        return AsyncStream { continuation in
            self.continuations.append(continuation)
            
            Task { @MainActor in
                do {
                    try await self.startRecognitionSession()
                } catch {
                    for cont in self.continuations {
                        cont.yield(.error("Failed to start recognition: \(error.localizedDescription)"))
                    }
                }
            }
        }
    }
    
    func stopContinuousRecognition() async {
        isRecognizing = false
        
        await stopCurrentRecognitionSession()
        
        for continuation in continuations {
            continuation.finish()
        }
        continuations.removeAll()
        
        await deactivateAudioSession()
    }
    
    func generateSRTContent() -> String {
        var srtContent = ""
        
        for (index, segment) in transcriptionSegments.enumerated() {
            let sequenceNumber = index + 1
            let startTime = SRTFileWriter.formatSRTTime(segment.startTime)
            let endTime = SRTFileWriter.formatSRTTime(segment.endTime)
            
            srtContent += "\(sequenceNumber)\n"
            srtContent += "\(startTime) --> \(endTime)\n"
            srtContent += "\(segment.text)\n\n"
        }
        
        return srtContent
    }
    
    // MARK: - Private Methods
    
    private func configureAudioSession() async throws {
        #if canImport(UIKit)
        let audioSession = AVAudioSession.sharedInstance()
        
        do {
            // CRITICAL: Check audio input availability first
            guard audioSession.isInputAvailable else {
                throw LiveSpeechRecognitionError.audioInputNotAvailable
            }
            
            // Check microphone permission
            let permission = audioSession.recordPermission
            if permission == .undetermined {
                // Request permission
                let granted = await withCheckedContinuation { continuation in
                    audioSession.requestRecordPermission { granted in
                        continuation.resume(returning: granted)
                    }
                }
                guard granted else {
                    throw LiveSpeechRecognitionError.microphonePermissionDenied
                }
            } else if permission == .denied {
                throw LiveSpeechRecognitionError.microphonePermissionDenied
            }
            
            // Check if audio session is already active (e.g., by camera)
            if audioSession.isOtherAudioPlaying || audioSession.secondaryAudioShouldBeSilencedHint {
                // Other audio is active, using compatible settings
            }
            
            // Use playAndRecord for camera compatibility with proper options
            try audioSession.setCategory(
                .playAndRecord,
                mode: .default,
                options: [.defaultToSpeaker, .mixWithOthers, .allowBluetooth]
            )
            
            // Set preferred settings but don't force them
            try audioSession.setPreferredSampleRate(48000)
            try audioSession.setPreferredIOBufferDuration(0.005)
            
            // Activate with notification to other apps
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
            
            // CRITICAL: Verify activation was successful
            guard audioSession.isInputAvailable else {
                throw LiveSpeechRecognitionError.audioInputNotAvailable
            }
        } catch {
            // Try a more basic configuration as fallback
            do {
                try audioSession.setCategory(.record, options: [.mixWithOthers])
                try audioSession.setActive(true)
                
                // Verify even the fallback worked
                guard audioSession.isInputAvailable else {
                    throw LiveSpeechRecognitionError.audioInputNotAvailable
                }
            } catch {
                throw error
            }
        }
        #endif
    }
    
    private func deactivateAudioSession() async {
        #if canImport(UIKit)
        do {
            try AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
        } catch {
            // Failed to deactivate audio session
        }
        #endif
    }
    
    private func startRecognitionSession() async throws {
        await stopCurrentRecognitionSession()
        
        guard isRecognizing else { return }
        
        // CRITICAL: First ensure the audio session is completely ready
        #if canImport(UIKit)
        let audioSession = AVAudioSession.sharedInstance()
        
        // Check permissions first
        let permissionStatus = audioSession.recordPermission
        guard permissionStatus == .granted else {
            throw LiveSpeechRecognitionError.microphonePermissionDenied
        }
        
        // Check if audio input is available
        guard audioSession.isInputAvailable else {
            throw LiveSpeechRecognitionError.audioInputNotAvailable
        }
        
        // Check app state
        let appState = await MainActor.run { UIApplication.shared.applicationState }
        guard appState == .active else {
            throw LiveSpeechRecognitionError.audioEngineCreationFailed
        }
        
        // IMPORTANT: Deactivate and reactivate audio session to ensure clean state
        do {
            // First deactivate to reset state
            try audioSession.setActive(false, options: .notifyOthersOnDeactivation)
            
            // Wait a moment
            try? await Task.sleep(nanoseconds: 100_000_000) // 100ms
            
            // Configure category
            try audioSession.setCategory(.playAndRecord, 
                                       mode: .default,
                                       options: [.defaultToSpeaker, .mixWithOthers, .allowBluetooth])
            
            // Activate audio session
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
            
            // Verify it's actually active and has input
            guard audioSession.isInputAvailable else {
                throw LiveSpeechRecognitionError.audioInputNotAvailable
            }
            
        } catch {
            throw LiveSpeechRecognitionError.audioEngineCreationFailed
        }
        
        // Wait for audio system to stabilize
        try? await Task.sleep(nanoseconds: 200_000_000) // 200ms
        #endif
        
        // Create recognition request
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        guard let recognitionRequest = recognitionRequest else {
            throw LiveSpeechRecognitionError.requestCreationFailed
        }
        
        recognitionRequest.shouldReportPartialResults = true
        recognitionRequest.requiresOnDeviceRecognition = recognizer?.supportsOnDeviceRecognition ?? false
        
        // ONLY NOW create the audio engine when we're certain audio is ready
        // Check one more time that audio is available
        #if canImport(UIKit)
        guard audioSession.isInputAvailable && audioSession.inputNumberOfChannels > 0 else {
            throw LiveSpeechRecognitionError.audioInputNotAvailable
        }
        #endif
        
        // SAFETY: Create engine in a deferred way to handle crash
        var newEngine: AVAudioEngine?
        var inputNode: AVAudioInputNode?
        
        // Multiple attempts with increasing delays
        for attempt in 0..<3 {
            if attempt > 0 {
                try? await Task.sleep(nanoseconds: UInt64(500_000_000 * attempt)) // Progressive delay
            }
            
            // Try to create the engine
            let engine = AVAudioEngine()
            
            // If we got here without crashing, try to access the input node
            let node = engine.inputNode
            
            // Verify it's valid
            let format = node.inputFormat(forBus: 0)
            
            if format.sampleRate > 0 && format.channelCount > 0 {
                newEngine = engine
                inputNode = node
                break
            } else {
                // Don't use this engine
                engine.stop()
                engine.reset()
            }
        }
        
        guard let audioEngine = newEngine, let validInputNode = inputNode else {
            throw LiveSpeechRecognitionError.audioEngineCreationFailed
        }
        
        self.audioEngine = audioEngine
        self.inputNode = validInputNode
        
        // Verify the input format
        let inputFormat = validInputNode.inputFormat(forBus: 0)
        guard inputFormat.sampleRate > 0 && inputFormat.channelCount > 0 else {
            throw LiveSpeechRecognitionError.audioEngineCreationFailed
        }
        
        // Remove any existing tap first
        validInputNode.removeTap(onBus: 0)
        
        // Wait a moment for audio engine to stabilize
        try? await Task.sleep(nanoseconds: 50_000_000) // 50ms
        
        // Start recognition task BEFORE starting audio engine
        recognitionTask = recognizer?.recognitionTask(with: recognitionRequest) { [weak self] result, error in
            guard let self = self else { return }
            
            Task { @MainActor in
                await self.handleRecognitionResult(result, error: error)
            }
        }
        
        guard recognitionTask != nil else {
            throw LiveSpeechRecognitionError.requestCreationFailed
        }
        
        // Get recording format and validate it BEFORE installing tap
        let recordingFormat = validInputNode.outputFormat(forBus: 0)
        
        // Validate audio format to prevent crash
        guard recordingFormat.sampleRate > 0 && recordingFormat.channelCount > 0 else {
            // Use a standard format instead
            let standardFormat = AVAudioFormat(standardFormatWithSampleRate: 48000, channels: 1)!
            
            // Try to install tap with standard format
            validInputNode.installTap(onBus: 0, bufferSize: 1024, format: standardFormat) { buffer, _ in
                recognitionRequest.append(buffer)
            }
            
            // Continue to audio engine startup
            audioEngine.prepare()
            
            do {
                try audioEngine.start()
            } catch {
                // Clean up on failure
                validInputNode.removeTap(onBus: 0)
                throw LiveSpeechRecognitionError.audioEngineCreationFailed
            }
            return // Exit the function here since we've handled the invalid format case
        }
        
        // Normal path for valid format
        validInputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
            recognitionRequest.append(buffer)
        }
        
        // Prepare and start audio engine AFTER tap is installed
        audioEngine.prepare()
        
        do {
            try audioEngine.start()
        } catch {
            // Clean up on failure
            validInputNode.removeTap(onBus: 0)
            throw LiveSpeechRecognitionError.audioEngineCreationFailed
        }
    }
    
    private func stopCurrentRecognitionSession() async {
        // Step 1: Cancel recognition task first
        recognitionTask?.cancel()
        recognitionTask = nil
        
        // Step 2: End audio on the request
        recognitionRequest?.endAudio()
        recognitionRequest = nil
        
        // Step 3: Stop audio engine with verification
        if let audioEngine = audioEngine {
            if audioEngine.isRunning {
                audioEngine.stop()
                
                // Wait for engine to fully stop
                var attempts = 0
                while audioEngine.isRunning && attempts < 10 {
                    try? await Task.sleep(nanoseconds: 50_000_000) // 50ms
                    attempts += 1
                }
                
                if audioEngine.isRunning {
                    // Warning: Audio engine still running after stop attempts
                }
            }
            
            // Reset the engine
            audioEngine.reset()
        }
        
        // Step 4: Remove audio tap safely
        if let inputNode = inputNode {
            inputNode.removeTap(onBus: 0)
        }
        
        // Step 5: Extended cleanup delay for full resource deallocation
        try? await Task.sleep(nanoseconds: 200_000_000) // 200ms instead of 5ms
        
        // Step 6: Nil out references
        audioEngine = nil
        inputNode = nil
    }
    
    private func handleRecognitionResult(_ result: SFSpeechRecognitionResult?, error: Error?) async {
        if let error = error {
            let nsError = error as NSError
            
            // Check for specific error conditions
            if nsError.domain == "kAFAssistantErrorDomain" {
                switch nsError.code {
                case 203: // Timeout waiting for command
                    // Perform enhanced cleanup for timeout
                    await stopCurrentRecognitionSession()
                    
                    // Longer delay for timeout recovery
                    try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
                    
                    await restartRecognitionSession()
                    return
                    
                case 1101: // No speech detected
                    await restartRecognitionSession()
                    return
                    
                default:
                    break
                }
            } else if nsError.domain == "com.apple.speech.localspeechrecognition" || nsError.code == 216 {
                await restartRecognitionSession()
                return
            } else if nsError.domain == "AVAEInternal" {
                for continuation in continuations {
                    continuation.yield(.error("Audio system temporarily unavailable"))
                }
                
                try? await Task.sleep(nanoseconds: 500_000_000)
                await restartRecognitionSession()
                return
            } else {
                for continuation in continuations {
                    continuation.yield(.error("Recognition error: \(error.localizedDescription)"))
                }
                
                await restartRecognitionSession()
                return
            }
        }
        
        if let result = result {
            let transcription = result.bestTranscription.formattedString
            let currentTime = Date().timeIntervalSince(sessionStartTime ?? Date())
            
            if result.isFinal {
                for continuation in continuations {
                    continuation.yield(.finalText(transcription, timestamp: currentTime))
                }
                
                if !transcription.isEmpty {
                    createTranscriptionSegment(text: transcription, endTime: currentTime)
                }
                
                // For continuous recognition: Keep running, only restart on actual errors
                
            } else {
                for continuation in continuations {
                    continuation.yield(.partialText(transcription, timestamp: currentTime))
                }
            }
        }
    }
    
    private func restartRecognitionSession() async {
        guard isRecognizing else { return }
        
        for continuation in continuations {
            continuation.yield(.recognitionRestarted)
        }
        
        // Ensure complete cleanup before restart
        await stopCurrentRecognitionSession()
        
        // Progressive retry with increasing delays for better recovery
        let retryDelays: [UInt64] = [500_000_000, 1_000_000_000, 2_000_000_000] // 500ms, 1s, 2s
        
        for (attempt, delay) in retryDelays.enumerated() {
            try? await Task.sleep(nanoseconds: delay)
            
            // Re-configure audio session if needed
            if attempt > 0 {
                do {
                    try await configureAudioSession()
                } catch {
                    // Audio session reconfiguration failed
                }
            }
            
            do {
                try await startRecognitionSession()
                
                // Notify that restart was successful
                for continuation in continuations {
                    continuation.yield(.recognitionRestarted)
                }
                return
            } catch {
                if attempt == retryDelays.count - 1 {
                    // Final attempt failed
                    for continuation in continuations {
                        continuation.yield(.error("Audio system unavailable - attempting full reset..."))
                    }
                    
                    // Try one more time after longer delay
                    Task {
                        try? await Task.sleep(nanoseconds: 5_000_000_000) // 5 seconds
                        if self.isRecognizing {
                            await self.performFullAudioSystemReset()
                        }
                    }
                }
            }
        }
    }
    
    private func performFullAudioSystemReset() async {
        // Step 1: Complete cleanup
        await stopCurrentRecognitionSession()
        
        // Step 2: Deactivate audio session
        await deactivateAudioSession()
        
        // Step 3: Wait for system to stabilize
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        // Step 4: Try to reinitialize everything
        do {
            try await configureAudioSession()
            try await startRecognitionSession()
            
            for continuation in continuations {
                continuation.yield(.recognitionRestarted)
            }
        } catch {
            for continuation in continuations {
                continuation.yield(.error("Unable to restart audio - please close and reopen the camera"))
            }
        }
    }
    
    private func createTranscriptionSegment(text: String, endTime: TimeInterval) {
        let segment = TranscriptionSegment(
            startTime: currentSegmentStartTime,
            endTime: endTime,
            text: text
        )
        transcriptionSegments.append(segment)
        currentSegmentStartTime = endTime
    }
}

// MARK: - SFSpeechRecognizerDelegate

extension LiveSpeechRecognitionManager: SFSpeechRecognizerDelegate {
    nonisolated func speechRecognizer(_ speechRecognizer: SFSpeechRecognizer, availabilityDidChange available: Bool) {
        Task { @MainActor in
            for continuation in continuations {
                continuation.yield(.availabilityChanged(available))
            }
            
            if !available && isRecognizing {
                try? await Task.sleep(nanoseconds: 1_000_000_000)
                if available {
                    await restartRecognitionSession()
                }
            }
        }
    }
}

// MARK: - Supporting Types

@objc public class TranscriptionSegment: NSObject {
    @objc public let startTime: TimeInterval
    @objc public let endTime: TimeInterval
    @objc public let text: String
    
    init(startTime: TimeInterval, endTime: TimeInterval, text: String) {
        self.startTime = startTime
        self.endTime = endTime
        self.text = text
        super.init()
    }
}

// MARK: - Errors

public enum LiveSpeechRecognitionError: LocalizedError {
    case recognizerNotAvailable
    case requestCreationFailed
    case audioEngineCreationFailed
    case audioInputNotAvailable
    case microphonePermissionDenied
    
    public var errorDescription: String? {
        switch self {
        case .recognizerNotAvailable:
            return "Speech recognizer is not available"
        case .requestCreationFailed:
            return "Failed to create speech recognition request"
        case .audioEngineCreationFailed:
            return "Failed to create audio engine"
        case .audioInputNotAvailable:
            return "No audio input device available"
        case .microphonePermissionDenied:
            return "Microphone permission not granted"
        }
    }
}
