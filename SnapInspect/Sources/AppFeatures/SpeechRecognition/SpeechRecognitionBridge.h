//
//  SpeechRecognitionBridge.h
//  SnapInspect3
//
//  Bridge header for exposing Speech Recognition Swift classes to Objective-C
//

#ifndef SpeechRecognitionBridge_h
#define SpeechRecognitionBridge_h

@import Foundation;
@import UIKit;

@class SpeechRecognitionManager;
@class SubtitleOverlayView;
@class SRTFileWriter;
@class TranscriptionSegment;

@protocol SpeechRecognitionDelegate <NSObject>
@optional
- (void)speechRecognitionDidStart;
- (void)speechRecognitionDidStop;
- (void)speechRecognitionDidReceivePartialResult:(NSString *)text;
- (void)speechRecognitionDidReceiveFinalResult:(NSString *)text timestamp:(NSTimeInterval)timestamp;
- (void)speechRecognitionDidFailWithError:(NSError *)error;
- (void)speechRecognitionAvailabilityChanged:(BOOL)isAvailable;
@end

#endif /* SpeechRecognitionBridge_h */
