import Foundation

@objc public class SRTFileWriter: NSObject {
    // MARK: - Properties
    
    private let fileManager = FileManager.default
    
    // MARK: - Public Methods
    
    @objc public func writeSRTFile(segments: [TranscriptionSegment], to filePath: String) throws {
        let srtContent = generateSRTContent(from: segments)
        
        // Ensure directory exists
        let directory = (filePath as NSString).deletingLastPathComponent
        if !fileManager.fileExists(atPath: directory) {
            try fileManager.createDirectory(atPath: directory, withIntermediateDirectories: true, attributes: nil)
        }
        
        // Write content to file
        try srtContent.write(toFile: filePath, atomically: true, encoding: .utf8)
    }
    
    @objc public func writeSRTFile(content: String, to filePath: String) throws {
        // Ensure directory exists
        let directory = (filePath as NSString).deletingLastPathComponent
        if !fileManager.fileExists(atPath: directory) {
            try fileManager.createDirectory(atPath: directory, withIntermediateDirectories: true, attributes: nil)
        }
        
        // Write content to file
        try content.write(toFile: filePath, atomically: true, encoding: .utf8)
    }
    
    @objc public func generateSRTFilePath(for videoPath: String) -> String {
        let videoURL = URL(fileURLWithPath: videoPath)
        let directory = videoURL.deletingLastPathComponent().path
        let fileName = videoURL.deletingPathExtension().lastPathComponent
        let srtFileName = "\(fileName).srt"
        
        return (directory as NSString).appendingPathComponent(srtFileName)
    }
    
    @objc public func srtFileExists(at path: String) -> Bool {
        return fileManager.fileExists(atPath: path)
    }
    
    @objc public func deleteSRTFile(at path: String) throws {
        if fileManager.fileExists(atPath: path) {
            try fileManager.removeItem(atPath: path)
        }
    }
    
    // MARK: - Private Methods
    
    private func generateSRTContent(from segments: [TranscriptionSegment]) -> String {
        var srtContent = ""
        
        for (index, segment) in segments.enumerated() {
            let sequenceNumber = index + 1
            let startTime = formatSRTTime(segment.startTime)
            let endTime = formatSRTTime(segment.endTime)
            
            srtContent += "\(sequenceNumber)\n"
            srtContent += "\(startTime) --> \(endTime)\n"
            srtContent += "\(segment.text)\n\n"
        }
        
        return srtContent
    }
    
    @objc public static func formatSRTTime(_ timeInterval: TimeInterval) -> String {
        let hours = Int(timeInterval) / 3600
        let minutes = (Int(timeInterval) % 3600) / 60
        let seconds = Int(timeInterval) % 60
        let milliseconds = Int((timeInterval.truncatingRemainder(dividingBy: 1)) * 1000)
        
        return String(format: "%02d:%02d:%02d,%03d", hours, minutes, seconds, milliseconds)
    }
    
    private func formatSRTTime(_ timeInterval: TimeInterval) -> String {
        return SRTFileWriter.formatSRTTime(timeInterval)
    }
}

// MARK: - SRT Parsing (for reading existing SRT files)

@objc public extension SRTFileWriter {
    func readSRTFile(from filePath: String) throws -> [TranscriptionSegment] {
        let content = try String(contentsOfFile: filePath, encoding: .utf8)
        return parseSRTContent(content)
    }
    
    private func parseSRTContent(_ content: String) -> [TranscriptionSegment] {
        var segments: [TranscriptionSegment] = []
        let blocks = content.components(separatedBy: "\n\n").filter { !$0.isEmpty }
        
        for block in blocks {
            let lines = block.components(separatedBy: "\n").filter { !$0.isEmpty }
            
            guard lines.count >= 3 else { continue }
            
            // Parse timestamp line (format: "00:00:00,000 --> 00:00:05,000")
            let timestampLine = lines[1]
            let timestamps = timestampLine.components(separatedBy: " --> ")
            
            guard timestamps.count == 2 else { continue }
            
            if let startTime = parseTime(timestamps[0]),
               let endTime = parseTime(timestamps[1])
            {
                // Combine remaining lines as text
                let text = lines[2...].joined(separator: " ")
                
                let segment = TranscriptionSegment(
                    startTime: startTime,
                    endTime: endTime,
                    text: text
                )
                segments.append(segment)
            }
        }
        
        return segments
    }
    
    @nonobjc private func parseTime(_ timeString: String) -> TimeInterval? {
        // Format: "00:00:00,000"
        let components = timeString.replacingOccurrences(of: ",", with: ".").components(separatedBy: ":")
        
        guard components.count == 3,
              let hours = Double(components[0]),
              let minutes = Double(components[1]),
              let seconds = Double(components[2])
        else {
            return nil
        }
        
        return hours * 3600 + minutes * 60 + seconds
    }
}
