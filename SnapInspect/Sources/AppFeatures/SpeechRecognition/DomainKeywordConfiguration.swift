import Foundation

/// Configuration for domain-specific keyword matching and compound word handling in speech recognition.
///
/// This structure provides centralized configuration for:
/// - Domain-specific keywords to improve context-aware matching
/// - Compound word definitions to handle words that can be written as one word or two
///
/// Example usage:
/// ```swift
/// // Using the default inspection configuration
/// let config = DomainKeywordConfiguration.inspectionDefaults
/// if config.isCompound("bath", "room") {
///     print("bathroom is a known compound")
/// }
///
/// // Creating a custom configuration
/// let customConfig = DomainKeywordConfiguration(
///     keywords: ["kitchen": ["kitchen", "cooking", "stove", "oven"]],
///     compoundWords: [("dish", "washer"), ("counter", "top")]
/// )
///
/// // Using with SpeechNormalizer
/// let result = SpeechNormalizer.findBestMatch(
///     spokenText: "check the bedroom smoke detector",
///     in: items,
///     domainConfiguration: config
/// )
/// ```
public struct DomainKeywordConfiguration {
    public let keywords: [String: [String]]
    public let compoundWords: [(String, String)]
    
    public init(
        keywords: [String: [String]] = [:],
        compoundWords: [(String, String)] = []
    ) {
        self.keywords = keywords
        self.compoundWords = compoundWords
    }
    
    /// Default configuration for inspection-related domains
    public static let inspectionDefaults = DomainKeywordConfiguration(
        keywords: [
            "fire": ["fire", "smoke", "alarm", "suppression", "sprinkler", "extinguisher"],
            "electrical": ["electrical", "power", "outlet", "panel", "circuit", "breaker"],
            "hvac": ["hvac", "air", "ventilation", "heating", "cooling", "filter"],
            "security": ["security", "door", "lock", "access", "entry", "exit", "secure", "secured"],
            "plumbing": ["plumbing", "pipe", "faucet", "drain", "toilet", "toilets", "restroom", "restrooms", "bathroom", "bathrooms"],
            "structural": ["structural", "wall", "foundation", "support"],
            "safety": ["safety", "emergency"],
            "lighting": ["lighting", "lights", "light", "fixture", "fixtures", "illumination", "bulb", "bulbs", "lamp", "lamps"],
            "windows": ["window", "windows", "glass", "pane", "panes", "broken", "intact"],
            "status": ["working", "functional", "operational", "functioning", "order", "properly", "correctly"],
            "moisture": ["moisture", "water", "dampness", "intrusion", "infiltration", "leak", "leaking", "damage", "damages", "visible"],
            "signage": ["signage", "signs", "sign", "posted", "notices", "display", "boards"],
            "roof": ["roof", "roofing", "gutter", "gutters", "overflow"],
            "pool": ["pool", "swimming", "area", "secured", "clear", "contaminants", "contaminated"],
            "apartment": ["apartment", "apt", "apt."],
            "dining": ["dining", "dinning"],
        ],
        compoundWords: [
            // Room types
            ("bath", "room"), ("bed", "room"), ("living", "room"), ("dining", "room"),
            ("laundry", "room"), ("store", "room"), ("class", "room"), ("break", "room"),
            ("wash", "room"), ("rest", "room"), ("board", "room"), ("show", "room"),
            ("mail", "room"), ("coat", "room"), ("mud", "room"), ("sun", "room"),
            // Architectural features
            ("fire", "place"), ("stair", "case"), ("hall", "way"), ("drive", "way"),
            ("walk", "way"), ("door", "way"), ("path", "way"), ("side", "walk"),
            ("back", "yard"), ("front", "yard"), ("court", "yard"),
        ]
    )
}

// MARK: - Utility Methods

public extension DomainKeywordConfiguration {
    /// Checks if two words form a known compound
    func isCompound(_ word1: String, _ word2: String) -> Bool {
        let lowered1 = word1.lowercased()
        let lowered2 = word2.lowercased()
        return compoundWords.contains { $0.0 == lowered1 && $0.1 == lowered2 }
    }
    
    /// Creates a merged compound from two words if they form a known compound
    func createCompound(_ word1: String, _ word2: String) -> String? {
        guard isCompound(word1, word2) else { return nil }
        return word1 + word2
    }
    
    /// Splits a word into compound parts if it's a known compound
    func splitCompound(_ word: String) -> (String, String)? {
        let lowered = word.lowercased()
        for (part1, part2) in compoundWords {
            if lowered == part1 + part2 {
                return (part1, part2)
            }
        }
        return nil
    }
}
