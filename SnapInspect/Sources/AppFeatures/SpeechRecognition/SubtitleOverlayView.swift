import Foundation
#if canImport(UIKit)
import UIKit

@objc public class SubtitleOverlayView: UIView {
    // MARK: - Constants
    
    private enum SubtitleConstants {
        static let defaultFontSize: CGFloat = 16
        static let shadowOffset: CGFloat = 1
        static let backgroundAlpha: CGFloat = 0.7
        static let cornerRadius: CGFloat = 8
        static let horizontalPadding: CGFloat = 16
        static let verticalPadding: CGFloat = 12
        static let fadeDelay: TimeInterval = 3.0
        static let animationDuration: TimeInterval = 0.3
        static let minimumHeight: CGFloat = 50.0
    }
    
    // MARK: - Public Properties

    public var orientation: UIDeviceOrientation = .portrait
        
    // MARK: - Properties
    
    private let textLabel: UILabel = {
        let label = UILabel()
        label.numberOfLines = 0
        label.textAlignment = .center
        label.font = .systemFont(ofSize: SubtitleConstants.defaultFontSize, weight: .medium)
        label.textColor = .white
        label.shadowColor = .black
        label.shadowOffset = CGSize(width: SubtitleConstants.shadowOffset, height: SubtitleConstants.shadowOffset)
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    private let backgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(SubtitleConstants.backgroundAlpha)
        view.layer.cornerRadius = SubtitleConstants.cornerRadius
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    private var fadeTimer: Timer?
    private let fadeDelay: TimeInterval = SubtitleConstants.fadeDelay
    
    // Constraint properties for orientation handling
    private weak var leadingConstraint: NSLayoutConstraint?
    private weak var trailingConstraint: NSLayoutConstraint?
    private weak var bottomConstraint: NSLayoutConstraint?
    
    @objc public var isVisible: Bool = true {
        didSet {
            isHidden = !isVisible
        }
    }
    
    // MARK: - Initialization
    
    @objc override public init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        // Configure self
        translatesAutoresizingMaskIntoConstraints = false
        isUserInteractionEnabled = false
        
        // Add subviews
        addSubview(backgroundView)
        addSubview(textLabel)
        
        // Setup constraints
        NSLayoutConstraint.activate([
            // Background view constraints
            backgroundView.leadingAnchor.constraint(equalTo: leadingAnchor),
            backgroundView.trailingAnchor.constraint(equalTo: trailingAnchor),
            backgroundView.topAnchor.constraint(equalTo: topAnchor),
            backgroundView.bottomAnchor.constraint(equalTo: bottomAnchor),
            
            // Text label constraints
            textLabel.leadingAnchor.constraint(equalTo: leadingAnchor, constant: SubtitleConstants.horizontalPadding),
            textLabel.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -SubtitleConstants.horizontalPadding),
            textLabel.topAnchor.constraint(equalTo: topAnchor, constant: SubtitleConstants.verticalPadding),
            textLabel.bottomAnchor.constraint(equalTo: bottomAnchor, constant: -SubtitleConstants.verticalPadding)
        ])
        
        // Initially hidden
        alpha = 0
    }
    
    // MARK: - Public Methods
    
    @objc public func updateSubtitle(_ text: String) {
        textLabel.text = text
        
        // Update height based on text content
        updateHeightForCurrentText()
        
        // Show the view
        showAnimated()
        
        // Reset fade timer
        resetFadeTimer()
    }
    
    @objc public func clearSubtitle() {
        hideAnimated()
    }
    
    @objc public func setTextColor(_ color: UIColor) {
        textLabel.textColor = color
    }
    
    @objc public func setBackgroundColor(_ color: UIColor, alpha: CGFloat) {
        backgroundView.backgroundColor = color.withAlphaComponent(alpha)
    }
    
    @objc public func setFontSize(_ size: CGFloat) {
        textLabel.font = .systemFont(ofSize: size, weight: .medium)
    }
    
    // MARK: - Private Methods
    
    private func showAnimated() {
        guard isVisible else { return }
        
        UIView.animate(withDuration: SubtitleConstants.animationDuration) {
            self.alpha = 1
        }
    }
    
    private func hideAnimated() {
        UIView.animate(withDuration: SubtitleConstants.animationDuration) {
            self.alpha = 0
        }
    }
    
    private func resetFadeTimer() {
        fadeTimer?.invalidate()
        fadeTimer = Timer.scheduledTimer(withTimeInterval: fadeDelay, repeats: false) { [weak self] _ in
            self?.hideAnimated()
        }
    }
    
    // MARK: - Height Management
    
    private func updateHeightForCurrentText() {
        var newFrame = frame
        if orientation.isLandscape {
            let widthDiff = totalHeight - frame.width
            newFrame.size.width = totalHeight
            newFrame.origin.x -= widthDiff
        } else {
            let heightDiff = totalHeight - frame.height
            newFrame.size.height = totalHeight
            // Adjust y position to keep bottom edge anchored
            newFrame.origin.y -= heightDiff
        }
        frame = newFrame
    }
    
    public var totalHeight: CGFloat {
        guard let text = textLabel.text, !text.isEmpty else {
            return SubtitleConstants.minimumHeight
        }
        
        let maxWidth = textLabel.bounds.width
        let font = textLabel.font ?? .systemFont(ofSize: SubtitleConstants.defaultFontSize, weight: .medium)
        
        let textHeight = text.boundingRect(
            with: CGSize(width: maxWidth, height: CGFloat.greatestFiniteMagnitude),
            options: [.usesLineFragmentOrigin, .usesFontLeading],
            attributes: [.font: font],
            context: nil
        ).height
        
        // Add padding (vertical padding * 2) and ensure minimum height
        return max(ceil(textHeight) + (SubtitleConstants.verticalPadding * 2), SubtitleConstants.minimumHeight)
    }
    
    deinit {
        fadeTimer?.invalidate()
        NotificationCenter.default.removeObserver(self)
    }
}

#endif
