Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild test -workspace SnapInspect3.xcworkspace -scheme AppFeaturesTests -destination "platform=iOS Simulator,name=iPhone 16 Pro" "-only-testing:AppFeaturesTests/SpeechNormalizerFindBestMatchTests/testFindBestMatchWithPhrases_ComplexScoring"

Resolve Package Graph


Resolved source packages:
  Intercom: https://github.com/intercom/intercom-ios-sp @ 18.6.4
  swift-perception: https://github.com/pointfreeco/swift-perception @ 1.4.1
  opentelemetry-swift: https://github.com/DataDog/opentelemetry-swift-packages.git @ 1.13.1
  Firebase: https://github.com/firebase/firebase-ios-sdk.git @ 10.29.0
  GoogleAppMeasurement: https://github.com/google/GoogleAppMeasurement.git @ 10.28.0
  gRPC: https://github.com/google/grpc-binary.git @ 1.62.2
  GoogleUtilities: https://github.com/google/GoogleUtilities.git @ 7.13.3
  xctest-dynamic-overlay: https://github.com/pointfreeco/xctest-dynamic-overlay @ 1.4.3
  swift-navigation: https://github.com/pointfreeco/swift-navigation @ 2.3.1
  AppAuth: https://github.com/openid/AppAuth-iOS.git @ 1.7.5
  swift-custom-dump: https://github.com/pointfreeco/swift-custom-dump @ 1.3.3
  TOCropViewController: https://github.com/TimOliver/TOCropViewController.git @ 2.7.4
  SwiftProtobuf: https://github.com/apple/swift-protobuf.git @ 1.28.1
  MBProgressHUD: https://github.com/jdg/MBProgressHUD @ master (4a7c5f3)
  abseil: https://github.com/google/abseil-cpp-binary.git @ 1.2024011602.0
  FMDB: https://github.com/ccgus/fmdb @ 2.7.12
  MarqueeLabel: https://github.com/cbpowell/MarqueeLabel @ 4.5.0
  GoogleDataTransport: https://github.com/google/GoogleDataTransport.git @ 9.4.0
  nanopb: https://github.com/firebase/nanopb.git @ 2.30910.0
  SwiftUIPullToRefresh: https://github.com/AppPear/SwiftUI-PullToRefresh @ 1.0.0
  SnapKit: https://github.com/SnapKit/SnapKit @ 5.7.1
  GoogleSignIn: https://github.com/google/GoogleSignIn-iOS @ 7.1.0
  UIScrollView_InfiniteScroll: https://github.com/pronebird/UIScrollView-InfiniteScroll @ 1.3.0
  AppCheck: https://github.com/google/app-check.git @ 10.19.2
  SDWebImage: https://github.com/SDWebImage/SDWebImage.git @ 5.20.0
  SwiftDate: https://github.com/malcommac/SwiftDate @ 6.3.1
  combine-schedulers: https://github.com/pointfreeco/combine-schedulers @ 1.0.2
  GTMAppAuth: https://github.com/google/GTMAppAuth.git @ 4.1.1
  SnapInspect: /Users/<USER>/Documents/Drive/Workspaces/Upworks/snapinspect/SnapInspect3-iOS-New/SnapInspect
  FSCalendar: https://github.com/WenchaoD/FSCalendar @ 2.8.4
  MSAL: https://github.com/AzureAD/microsoft-authentication-library-for-objc @ 1.5.0
  Promises: https://github.com/google/promises.git @ 2.4.0
  swift-sharing: https://github.com/pointfreeco/swift-sharing @ 2.5.2
  WeScan: https://github.com/shanegao/WeScan @ 3.0.2
  FontAwesome: https://github.com/thii/FontAwesome.swift @ 1.9.1
  YPImagePicker: https://github.com/Yummypets/YPImagePicker @ 5.2.2
  PryntTrimmerView: https://github.com/HHK1/PryntTrimmerView @ 4.0.2
  InteropForGoogle: https://github.com/google/interop-ios-for-google-sdks.git @ 100.0.0
  Datadog: https://github.com/DataDog/dd-sdk-ios @ 2.29.0
  leveldb: https://github.com/firebase/leveldb.git @ 1.22.5
  swift-dependencies: https://github.com/pointfreeco/swift-dependencies @ 1.6.1
  swift-syntax: https://github.com/swiftlang/swift-syntax @ 600.0.1
  SwiftUIFlow: https://github.com/ciaranrobrien/SwiftUIFlow @ main (b300d3c)
  SDWebImageSwiftUI: https://github.com/SDWebImage/SDWebImageSwiftUI @ 3.1.3
  swift-identified-collections: https://github.com/pointfreeco/swift-identified-collections @ 1.1.0
  PLCrashReporter: https://github.com/microsoft/plcrashreporter.git @ 1.12.0
  NotificationBannerSwift: https://github.com/Daltron/NotificationBanner @ 4.0.0
  Stevia: https://github.com/freshOS/Stevia @ 5.1.2
  swift-clocks: https://github.com/pointfreeco/swift-clocks @ 1.0.5
  swift-case-paths: https://github.com/pointfreeco/swift-case-paths @ 1.5.6
  CHTCollectionViewWaterfallLayout: https://github.com/chiahsien/CHTCollectionViewWaterfallLayout @ 0.9.10
  swift-collections: https://github.com/apple/swift-collections @ 1.1.4
  swift-concurrency-extras: https://github.com/pointfreeco/swift-concurrency-extras @ 1.3.0
  GTMSessionFetcher: https://github.com/google/gtm-session-fetcher.git @ 3.5.0
  swift-composable-architecture: https://github.com/pointfreeco/swift-composable-architecture @ 1.20.2

ComputePackagePrebuildTargetDependencyGraph

Prepare packages

CreateBuildRequest

SendProjectDescription

CreateBuildOperation

ComputeTargetDependencyGraph
note: Building targets in dependency order
note: Target dependency graph (193 targets)
    Target 'AppFeaturesTests' in project 'SnapInspect'
        ➜ Explicit dependency on target 'Extensions' in project 'SnapInspect'
        ➜ Explicit dependency on target 'AppFeatures' in project 'SnapInspect'
        ➜ Explicit dependency on target 'DatadogCore' in project 'Datadog'
        ➜ Explicit dependency on target 'DatadogLogs' in project 'Datadog'
        ➜ Explicit dependency on target 'MSAL' in project 'MSAL'
        ➜ Explicit dependency on target 'NotificationBannerSwift' in project 'NotificationBannerSwift'
        ➜ Explicit dependency on target 'SDWebImageSwiftUI' in project 'SDWebImageSwiftUI'
        ➜ Explicit dependency on target 'SwiftUIPullToRefresh' in project 'SwiftUIPullToRefresh'
        ➜ Explicit dependency on target 'SwiftDate' in project 'SwiftDate'
        ➜ Explicit dependency on target 'TOCropViewController' in project 'TOCropViewController'
        ➜ Explicit dependency on target 'FSCalendar' in project 'FSCalendar'
        ➜ Explicit dependency on target 'UIScrollView_InfiniteScroll' in project 'UIScrollView_InfiniteScroll'
        ➜ Explicit dependency on target 'CHTCollectionViewWaterfallLayout' in project 'CHTCollectionViewWaterfallLayout'
        ➜ Explicit dependency on target 'FontAwesome' in project 'FontAwesome'
        ➜ Explicit dependency on target 'GoogleSignIn' in project 'GoogleSignIn'
        ➜ Explicit dependency on target 'FirebaseAnalytics' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCrashlytics' in project 'Firebase'
        ➜ Explicit dependency on target 'FMDB' in project 'FMDB'
        ➜ Explicit dependency on target 'WeScan' in project 'WeScan'
        ➜ Explicit dependency on target 'Intercom' in project 'Intercom'
        ➜ Explicit dependency on target 'YPImagePicker' in project 'YPImagePicker'
        ➜ Explicit dependency on target 'MBProgressHUD' in project 'MBProgressHUD'
        ➜ Explicit dependency on target 'ComposableArchitecture' in project 'swift-composable-architecture'
        ➜ Explicit dependency on target 'SwiftUIFlow' in project 'SwiftUIFlow'
    Target 'AppFeatures' in project 'SnapInspect'
        ➜ Explicit dependency on target 'Extensions' in project 'SnapInspect'
        ➜ Explicit dependency on target 'DatadogCore' in project 'Datadog'
        ➜ Explicit dependency on target 'DatadogLogs' in project 'Datadog'
        ➜ Explicit dependency on target 'MSAL' in project 'MSAL'
        ➜ Explicit dependency on target 'NotificationBannerSwift' in project 'NotificationBannerSwift'
        ➜ Explicit dependency on target 'SDWebImageSwiftUI' in project 'SDWebImageSwiftUI'
        ➜ Explicit dependency on target 'SwiftUIPullToRefresh' in project 'SwiftUIPullToRefresh'
        ➜ Explicit dependency on target 'SwiftDate' in project 'SwiftDate'
        ➜ Explicit dependency on target 'TOCropViewController' in project 'TOCropViewController'
        ➜ Explicit dependency on target 'FSCalendar' in project 'FSCalendar'
        ➜ Explicit dependency on target 'UIScrollView_InfiniteScroll' in project 'UIScrollView_InfiniteScroll'
        ➜ Explicit dependency on target 'CHTCollectionViewWaterfallLayout' in project 'CHTCollectionViewWaterfallLayout'
        ➜ Explicit dependency on target 'FontAwesome' in project 'FontAwesome'
        ➜ Explicit dependency on target 'GoogleSignIn' in project 'GoogleSignIn'
        ➜ Explicit dependency on target 'FirebaseAnalytics' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCrashlytics' in project 'Firebase'
        ➜ Explicit dependency on target 'FMDB' in project 'FMDB'
        ➜ Explicit dependency on target 'WeScan' in project 'WeScan'
        ➜ Explicit dependency on target 'Intercom' in project 'Intercom'
        ➜ Explicit dependency on target 'YPImagePicker' in project 'YPImagePicker'
        ➜ Explicit dependency on target 'MBProgressHUD' in project 'MBProgressHUD'
        ➜ Explicit dependency on target 'ComposableArchitecture' in project 'swift-composable-architecture'
        ➜ Explicit dependency on target 'SwiftUIFlow' in project 'SwiftUIFlow'
    Target 'SwiftUIFlow' in project 'SwiftUIFlow'
        ➜ Explicit dependency on target 'SwiftUIFlow' in project 'SwiftUIFlow'
    Target 'SwiftUIFlow' in project 'SwiftUIFlow' (no dependencies)
    Target 'ComposableArchitecture' in project 'swift-composable-architecture'
        ➜ Explicit dependency on target 'ComposableArchitecture' in project 'swift-composable-architecture'
        ➜ Explicit dependency on target 'swift-composable-architecture_ComposableArchitecture' in project 'swift-composable-architecture'
        ➜ Explicit dependency on target 'ComposableArchitectureMacros' in project 'swift-composable-architecture'
        ➜ Explicit dependency on target 'CasePaths' in project 'swift-case-paths'
        ➜ Explicit dependency on target 'CombineSchedulers' in project 'combine-schedulers'
        ➜ Explicit dependency on target 'ConcurrencyExtras' in project 'swift-concurrency-extras'
        ➜ Explicit dependency on target 'CustomDump' in project 'swift-custom-dump'
        ➜ Explicit dependency on target 'Dependencies' in project 'swift-dependencies'
        ➜ Explicit dependency on target 'DependenciesMacros' in project 'swift-dependencies'
        ➜ Explicit dependency on target 'IdentifiedCollections' in project 'swift-identified-collections'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
        ➜ Explicit dependency on target 'OrderedCollections' in project 'swift-collections'
        ➜ Explicit dependency on target 'Perception' in project 'swift-perception'
        ➜ Explicit dependency on target 'Sharing' in project 'swift-sharing'
        ➜ Explicit dependency on target 'SwiftUINavigation' in project 'swift-navigation'
        ➜ Explicit dependency on target 'UIKitNavigation' in project 'swift-navigation'
    Target 'ComposableArchitecture' in project 'swift-composable-architecture'
        ➜ Explicit dependency on target 'swift-composable-architecture_ComposableArchitecture' in project 'swift-composable-architecture'
        ➜ Explicit dependency on target 'ComposableArchitectureMacros' in project 'swift-composable-architecture'
        ➜ Explicit dependency on target 'CasePaths' in project 'swift-case-paths'
        ➜ Explicit dependency on target 'CombineSchedulers' in project 'combine-schedulers'
        ➜ Explicit dependency on target 'ConcurrencyExtras' in project 'swift-concurrency-extras'
        ➜ Explicit dependency on target 'CustomDump' in project 'swift-custom-dump'
        ➜ Explicit dependency on target 'Dependencies' in project 'swift-dependencies'
        ➜ Explicit dependency on target 'DependenciesMacros' in project 'swift-dependencies'
        ➜ Explicit dependency on target 'IdentifiedCollections' in project 'swift-identified-collections'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
        ➜ Explicit dependency on target 'OrderedCollections' in project 'swift-collections'
        ➜ Explicit dependency on target 'Perception' in project 'swift-perception'
        ➜ Explicit dependency on target 'Sharing' in project 'swift-sharing'
        ➜ Explicit dependency on target 'SwiftUINavigation' in project 'swift-navigation'
        ➜ Explicit dependency on target 'UIKitNavigation' in project 'swift-navigation'
    Target 'UIKitNavigation' in project 'swift-navigation'
        ➜ Explicit dependency on target 'UIKitNavigation' in project 'swift-navigation'
        ➜ Explicit dependency on target 'SwiftNavigation' in project 'swift-navigation'
        ➜ Explicit dependency on target 'UIKitNavigationShim' in project 'swift-navigation'
        ➜ Explicit dependency on target 'CasePaths' in project 'swift-case-paths'
        ➜ Explicit dependency on target 'CustomDump' in project 'swift-custom-dump'
        ➜ Explicit dependency on target 'ConcurrencyExtras' in project 'swift-concurrency-extras'
        ➜ Explicit dependency on target 'OrderedCollections' in project 'swift-collections'
        ➜ Explicit dependency on target 'Perception' in project 'swift-perception'
    Target 'SwiftUINavigation' in project 'swift-navigation'
        ➜ Explicit dependency on target 'SwiftUINavigation' in project 'swift-navigation'
        ➜ Explicit dependency on target 'SwiftNavigation' in project 'swift-navigation'
        ➜ Explicit dependency on target 'UIKitNavigationShim' in project 'swift-navigation'
        ➜ Explicit dependency on target 'UIKitNavigation' in project 'swift-navigation'
        ➜ Explicit dependency on target 'CasePaths' in project 'swift-case-paths'
        ➜ Explicit dependency on target 'CustomDump' in project 'swift-custom-dump'
        ➜ Explicit dependency on target 'ConcurrencyExtras' in project 'swift-concurrency-extras'
        ➜ Explicit dependency on target 'OrderedCollections' in project 'swift-collections'
        ➜ Explicit dependency on target 'Perception' in project 'swift-perception'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
    Target 'SwiftUINavigation' in project 'swift-navigation'
        ➜ Explicit dependency on target 'SwiftNavigation' in project 'swift-navigation'
        ➜ Explicit dependency on target 'UIKitNavigationShim' in project 'swift-navigation'
        ➜ Explicit dependency on target 'UIKitNavigation' in project 'swift-navigation'
        ➜ Explicit dependency on target 'CasePaths' in project 'swift-case-paths'
        ➜ Explicit dependency on target 'CustomDump' in project 'swift-custom-dump'
        ➜ Explicit dependency on target 'ConcurrencyExtras' in project 'swift-concurrency-extras'
        ➜ Explicit dependency on target 'OrderedCollections' in project 'swift-collections'
        ➜ Explicit dependency on target 'Perception' in project 'swift-perception'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
    Target 'UIKitNavigation' in project 'swift-navigation'
        ➜ Explicit dependency on target 'SwiftNavigation' in project 'swift-navigation'
        ➜ Explicit dependency on target 'UIKitNavigationShim' in project 'swift-navigation'
        ➜ Explicit dependency on target 'CasePaths' in project 'swift-case-paths'
        ➜ Explicit dependency on target 'CustomDump' in project 'swift-custom-dump'
        ➜ Explicit dependency on target 'ConcurrencyExtras' in project 'swift-concurrency-extras'
        ➜ Explicit dependency on target 'OrderedCollections' in project 'swift-collections'
        ➜ Explicit dependency on target 'Perception' in project 'swift-perception'
    Target 'UIKitNavigationShim' in project 'swift-navigation' (no dependencies)
    Target 'SwiftNavigation' in project 'swift-navigation'
        ➜ Explicit dependency on target 'CasePaths' in project 'swift-case-paths'
        ➜ Explicit dependency on target 'CustomDump' in project 'swift-custom-dump'
        ➜ Explicit dependency on target 'ConcurrencyExtras' in project 'swift-concurrency-extras'
        ➜ Explicit dependency on target 'OrderedCollections' in project 'swift-collections'
        ➜ Explicit dependency on target 'Perception' in project 'swift-perception'
    Target 'Sharing' in project 'swift-sharing'
        ➜ Explicit dependency on target 'Sharing' in project 'swift-sharing'
        ➜ Explicit dependency on target 'swift-sharing_Sharing' in project 'swift-sharing'
        ➜ Explicit dependency on target 'Sharing1' in project 'swift-sharing'
        ➜ Explicit dependency on target 'Sharing2' in project 'swift-sharing'
        ➜ Explicit dependency on target 'CombineSchedulers' in project 'combine-schedulers'
        ➜ Explicit dependency on target 'ConcurrencyExtras' in project 'swift-concurrency-extras'
        ➜ Explicit dependency on target 'CustomDump' in project 'swift-custom-dump'
        ➜ Explicit dependency on target 'Dependencies' in project 'swift-dependencies'
        ➜ Explicit dependency on target 'IdentifiedCollections' in project 'swift-identified-collections'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
        ➜ Explicit dependency on target 'PerceptionCore' in project 'swift-perception'
    Target 'Sharing' in project 'swift-sharing'
        ➜ Explicit dependency on target 'swift-sharing_Sharing' in project 'swift-sharing'
        ➜ Explicit dependency on target 'Sharing1' in project 'swift-sharing'
        ➜ Explicit dependency on target 'Sharing2' in project 'swift-sharing'
        ➜ Explicit dependency on target 'CombineSchedulers' in project 'combine-schedulers'
        ➜ Explicit dependency on target 'ConcurrencyExtras' in project 'swift-concurrency-extras'
        ➜ Explicit dependency on target 'CustomDump' in project 'swift-custom-dump'
        ➜ Explicit dependency on target 'Dependencies' in project 'swift-dependencies'
        ➜ Explicit dependency on target 'IdentifiedCollections' in project 'swift-identified-collections'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
        ➜ Explicit dependency on target 'PerceptionCore' in project 'swift-perception'
    Target 'PerceptionCore' in project 'swift-perception'
        ➜ Explicit dependency on target 'PerceptionCore' in project 'swift-perception'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
    Target 'Sharing2' in project 'swift-sharing' (no dependencies)
    Target 'Sharing1' in project 'swift-sharing' (no dependencies)
    Target 'swift-sharing_Sharing' in project 'swift-sharing' (no dependencies)
    Target 'Perception' in project 'swift-perception'
        ➜ Explicit dependency on target 'Perception' in project 'swift-perception'
        ➜ Explicit dependency on target 'PerceptionCore' in project 'swift-perception'
        ➜ Explicit dependency on target 'PerceptionMacros' in project 'swift-perception'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
    Target 'Perception' in project 'swift-perception'
        ➜ Explicit dependency on target 'PerceptionCore' in project 'swift-perception'
        ➜ Explicit dependency on target 'PerceptionMacros' in project 'swift-perception'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
    Target 'PerceptionMacros' in project 'swift-perception'
        ➜ Explicit dependency on target 'SwiftSyntaxMacros' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftCompilerPlugin' in project 'swift-syntax'
    Target 'PerceptionCore' in project 'swift-perception'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
    Target 'IdentifiedCollections' in project 'swift-identified-collections'
        ➜ Explicit dependency on target 'IdentifiedCollections' in project 'swift-identified-collections'
        ➜ Explicit dependency on target 'OrderedCollections' in project 'swift-collections'
    Target 'IdentifiedCollections' in project 'swift-identified-collections'
        ➜ Explicit dependency on target 'OrderedCollections' in project 'swift-collections'
    Target 'OrderedCollections' in project 'swift-collections'
        ➜ Explicit dependency on target 'OrderedCollections' in project 'swift-collections'
        ➜ Explicit dependency on target 'InternalCollectionsUtilities' in project 'swift-collections'
    Target 'OrderedCollections' in project 'swift-collections'
        ➜ Explicit dependency on target 'InternalCollectionsUtilities' in project 'swift-collections'
    Target 'InternalCollectionsUtilities' in project 'swift-collections' (no dependencies)
    Target 'DependenciesMacros' in project 'swift-dependencies'
        ➜ Explicit dependency on target 'DependenciesMacros' in project 'swift-dependencies'
        ➜ Explicit dependency on target 'DependenciesMacrosPlugin' in project 'swift-dependencies'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
        ➜ Explicit dependency on target 'XCTestDynamicOverlay' in project 'xctest-dynamic-overlay'
    Target 'DependenciesMacros' in project 'swift-dependencies'
        ➜ Explicit dependency on target 'DependenciesMacrosPlugin' in project 'swift-dependencies'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
        ➜ Explicit dependency on target 'XCTestDynamicOverlay' in project 'xctest-dynamic-overlay'
    Target 'DependenciesMacrosPlugin' in project 'swift-dependencies'
        ➜ Explicit dependency on target 'SwiftSyntaxMacros' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftCompilerPlugin' in project 'swift-syntax'
    Target 'Dependencies' in project 'swift-dependencies'
        ➜ Explicit dependency on target 'Dependencies' in project 'swift-dependencies'
        ➜ Explicit dependency on target 'Clocks' in project 'swift-clocks'
        ➜ Explicit dependency on target 'CombineSchedulers' in project 'combine-schedulers'
        ➜ Explicit dependency on target 'ConcurrencyExtras' in project 'swift-concurrency-extras'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
        ➜ Explicit dependency on target 'XCTestDynamicOverlay' in project 'xctest-dynamic-overlay'
    Target 'Dependencies' in project 'swift-dependencies'
        ➜ Explicit dependency on target 'Clocks' in project 'swift-clocks'
        ➜ Explicit dependency on target 'CombineSchedulers' in project 'combine-schedulers'
        ➜ Explicit dependency on target 'ConcurrencyExtras' in project 'swift-concurrency-extras'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
        ➜ Explicit dependency on target 'XCTestDynamicOverlay' in project 'xctest-dynamic-overlay'
    Target 'Clocks' in project 'swift-clocks'
        ➜ Explicit dependency on target 'Clocks' in project 'swift-clocks'
        ➜ Explicit dependency on target 'ConcurrencyExtras' in project 'swift-concurrency-extras'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
    Target 'Clocks' in project 'swift-clocks'
        ➜ Explicit dependency on target 'ConcurrencyExtras' in project 'swift-concurrency-extras'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
    Target 'CustomDump' in project 'swift-custom-dump'
        ➜ Explicit dependency on target 'CustomDump' in project 'swift-custom-dump'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
        ➜ Explicit dependency on target 'XCTestDynamicOverlay' in project 'xctest-dynamic-overlay'
    Target 'CustomDump' in project 'swift-custom-dump'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
        ➜ Explicit dependency on target 'XCTestDynamicOverlay' in project 'xctest-dynamic-overlay'
    Target 'CombineSchedulers' in project 'combine-schedulers'
        ➜ Explicit dependency on target 'CombineSchedulers' in project 'combine-schedulers'
        ➜ Explicit dependency on target 'ConcurrencyExtras' in project 'swift-concurrency-extras'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
    Target 'CombineSchedulers' in project 'combine-schedulers'
        ➜ Explicit dependency on target 'ConcurrencyExtras' in project 'swift-concurrency-extras'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
    Target 'ConcurrencyExtras' in project 'swift-concurrency-extras'
        ➜ Explicit dependency on target 'ConcurrencyExtras' in project 'swift-concurrency-extras'
    Target 'ConcurrencyExtras' in project 'swift-concurrency-extras' (no dependencies)
    Target 'CasePaths' in project 'swift-case-paths'
        ➜ Explicit dependency on target 'CasePaths' in project 'swift-case-paths'
        ➜ Explicit dependency on target 'CasePathsMacros' in project 'swift-case-paths'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
        ➜ Explicit dependency on target 'XCTestDynamicOverlay' in project 'xctest-dynamic-overlay'
    Target 'CasePaths' in project 'swift-case-paths'
        ➜ Explicit dependency on target 'CasePathsMacros' in project 'swift-case-paths'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
        ➜ Explicit dependency on target 'XCTestDynamicOverlay' in project 'xctest-dynamic-overlay'
    Target 'XCTestDynamicOverlay' in project 'xctest-dynamic-overlay'
        ➜ Explicit dependency on target 'XCTestDynamicOverlay' in project 'xctest-dynamic-overlay'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
    Target 'XCTestDynamicOverlay' in project 'xctest-dynamic-overlay'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
    Target 'IssueReporting' in project 'xctest-dynamic-overlay'
        ➜ Explicit dependency on target 'IssueReporting' in project 'xctest-dynamic-overlay'
    Target 'IssueReporting' in project 'xctest-dynamic-overlay' (no dependencies)
    Target 'CasePathsMacros' in project 'swift-case-paths'
        ➜ Explicit dependency on target 'SwiftSyntaxMacros' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftCompilerPlugin' in project 'swift-syntax'
    Target 'ComposableArchitectureMacros' in project 'swift-composable-architecture'
        ➜ Explicit dependency on target 'SwiftSyntaxMacros' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftCompilerPlugin' in project 'swift-syntax'
    Target 'SwiftCompilerPlugin' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftCompilerPlugin' in project 'swift-syntax'
        ➜ Explicit dependency on target '_SwiftSyntaxCShims' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax509' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax510' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax600' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftDiagnostics' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftParser' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftOperators' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftBasicFormat' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftParserDiagnostics' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntaxBuilder' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntaxMacros' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntaxMacroExpansion' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftCompilerPluginMessageHandling' in project 'swift-syntax'
    Target 'SwiftCompilerPlugin' in project 'swift-syntax'
        ➜ Explicit dependency on target '_SwiftSyntaxCShims' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax509' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax510' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax600' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftDiagnostics' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftParser' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftOperators' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftBasicFormat' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftParserDiagnostics' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntaxBuilder' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntaxMacros' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntaxMacroExpansion' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftCompilerPluginMessageHandling' in project 'swift-syntax'
    Target 'SwiftCompilerPluginMessageHandling' in project 'swift-syntax'
        ➜ Explicit dependency on target '_SwiftSyntaxCShims' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax509' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax510' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax600' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftDiagnostics' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftParser' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftOperators' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftBasicFormat' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftParserDiagnostics' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntaxBuilder' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntaxMacros' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntaxMacroExpansion' in project 'swift-syntax'
    Target 'SwiftSyntaxMacroExpansion' in project 'swift-syntax'
        ➜ Explicit dependency on target '_SwiftSyntaxCShims' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax509' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax510' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax600' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftBasicFormat' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftParser' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftDiagnostics' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftParserDiagnostics' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntaxBuilder' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntaxMacros' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftOperators' in project 'swift-syntax'
    Target 'SwiftOperators' in project 'swift-syntax'
        ➜ Explicit dependency on target '_SwiftSyntaxCShims' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax509' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax510' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax600' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftDiagnostics' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftParser' in project 'swift-syntax'
    Target 'SwiftSyntaxMacros' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntaxMacros' in project 'swift-syntax'
        ➜ Explicit dependency on target '_SwiftSyntaxCShims' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax509' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax510' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax600' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftDiagnostics' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftParser' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftBasicFormat' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftParserDiagnostics' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntaxBuilder' in project 'swift-syntax'
    Target 'SwiftSyntaxMacros' in project 'swift-syntax'
        ➜ Explicit dependency on target '_SwiftSyntaxCShims' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax509' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax510' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax600' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftDiagnostics' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftParser' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftBasicFormat' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftParserDiagnostics' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntaxBuilder' in project 'swift-syntax'
    Target 'SwiftSyntaxBuilder' in project 'swift-syntax'
        ➜ Explicit dependency on target '_SwiftSyntaxCShims' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax509' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax510' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax600' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftBasicFormat' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftParser' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftDiagnostics' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftParserDiagnostics' in project 'swift-syntax'
    Target 'SwiftParserDiagnostics' in project 'swift-syntax'
        ➜ Explicit dependency on target '_SwiftSyntaxCShims' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax509' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax510' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax600' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftBasicFormat' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftDiagnostics' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftParser' in project 'swift-syntax'
    Target 'SwiftBasicFormat' in project 'swift-syntax'
        ➜ Explicit dependency on target '_SwiftSyntaxCShims' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax509' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax510' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax600' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax' in project 'swift-syntax'
    Target 'SwiftParser' in project 'swift-syntax'
        ➜ Explicit dependency on target '_SwiftSyntaxCShims' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax509' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax510' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax600' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax' in project 'swift-syntax'
    Target 'SwiftDiagnostics' in project 'swift-syntax'
        ➜ Explicit dependency on target '_SwiftSyntaxCShims' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax509' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax510' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax600' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax' in project 'swift-syntax'
    Target 'SwiftSyntax' in project 'swift-syntax'
        ➜ Explicit dependency on target '_SwiftSyntaxCShims' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax509' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax510' in project 'swift-syntax'
        ➜ Explicit dependency on target 'SwiftSyntax600' in project 'swift-syntax'
    Target 'SwiftSyntax600' in project 'swift-syntax' (no dependencies)
    Target 'SwiftSyntax510' in project 'swift-syntax' (no dependencies)
    Target 'SwiftSyntax509' in project 'swift-syntax' (no dependencies)
    Target '_SwiftSyntaxCShims' in project 'swift-syntax' (no dependencies)
    Target 'swift-composable-architecture_ComposableArchitecture' in project 'swift-composable-architecture' (no dependencies)
    Target 'MBProgressHUD' in project 'MBProgressHUD'
        ➜ Explicit dependency on target 'MBProgressHUD' in project 'MBProgressHUD'
        ➜ Explicit dependency on target 'MBProgressHUD_MBProgressHUD' in project 'MBProgressHUD'
    Target 'MBProgressHUD' in project 'MBProgressHUD'
        ➜ Explicit dependency on target 'MBProgressHUD_MBProgressHUD' in project 'MBProgressHUD'
    Target 'MBProgressHUD_MBProgressHUD' in project 'MBProgressHUD' (no dependencies)
    Target 'YPImagePicker' in project 'YPImagePicker'
        ➜ Explicit dependency on target 'YPImagePicker' in project 'YPImagePicker'
        ➜ Explicit dependency on target 'YPImagePicker_YPImagePicker' in project 'YPImagePicker'
        ➜ Explicit dependency on target 'Stevia' in project 'Stevia'
        ➜ Explicit dependency on target 'PryntTrimmerView' in project 'PryntTrimmerView'
    Target 'YPImagePicker' in project 'YPImagePicker'
        ➜ Explicit dependency on target 'YPImagePicker_YPImagePicker' in project 'YPImagePicker'
        ➜ Explicit dependency on target 'Stevia' in project 'Stevia'
        ➜ Explicit dependency on target 'PryntTrimmerView' in project 'PryntTrimmerView'
    Target 'PryntTrimmerView' in project 'PryntTrimmerView'
        ➜ Explicit dependency on target 'PryntTrimmerView' in project 'PryntTrimmerView'
    Target 'PryntTrimmerView' in project 'PryntTrimmerView' (no dependencies)
    Target 'Stevia' in project 'Stevia'
        ➜ Explicit dependency on target 'Stevia' in project 'Stevia'
    Target 'Stevia' in project 'Stevia' (no dependencies)
    Target 'YPImagePicker_YPImagePicker' in project 'YPImagePicker' (no dependencies)
    Target 'Intercom' in project 'Intercom' (no dependencies)
    Target 'WeScan' in project 'WeScan'
        ➜ Explicit dependency on target 'WeScan' in project 'WeScan'
        ➜ Explicit dependency on target 'WeScan_WeScan' in project 'WeScan'
    Target 'WeScan' in project 'WeScan'
        ➜ Explicit dependency on target 'WeScan_WeScan' in project 'WeScan'
    Target 'WeScan_WeScan' in project 'WeScan' (no dependencies)
    Target 'FMDB' in project 'FMDB'
        ➜ Explicit dependency on target 'FMDB' in project 'FMDB'
    Target 'FMDB' in project 'FMDB' (no dependencies)
    Target 'FirebaseCrashlytics' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCrashlytics' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase_FirebaseCrashlytics' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseInstallations' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreExtension' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseSessionsObjC' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseSessions' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseRemoteConfigInterop' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCrashlyticsSwift' in project 'Firebase'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
        ➜ Explicit dependency on target 'Promises' in project 'Promises'
        ➜ Explicit dependency on target 'GoogleDataTransport' in project 'GoogleDataTransport'
    Target 'FirebaseCrashlytics' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase_FirebaseCrashlytics' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseInstallations' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreExtension' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseSessionsObjC' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseSessions' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseRemoteConfigInterop' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCrashlyticsSwift' in project 'Firebase'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
        ➜ Explicit dependency on target 'Promises' in project 'Promises'
        ➜ Explicit dependency on target 'GoogleDataTransport' in project 'GoogleDataTransport'
    Target 'FirebaseCrashlyticsSwift' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseRemoteConfigInterop' in project 'Firebase'
    Target 'FirebaseRemoteConfigInterop' in project 'Firebase' (no dependencies)
    Target 'FirebaseSessions' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseInstallations' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreExtension' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseSessionsObjC' in project 'Firebase'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
        ➜ Explicit dependency on target 'Promises' in project 'Promises'
        ➜ Explicit dependency on target 'GoogleDataTransport' in project 'GoogleDataTransport'
    Target 'GoogleDataTransport' in project 'GoogleDataTransport'
        ➜ Explicit dependency on target 'GoogleDataTransport' in project 'GoogleDataTransport'
        ➜ Explicit dependency on target 'GoogleDataTransport_GoogleDataTransport' in project 'GoogleDataTransport'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
    Target 'GoogleDataTransport' in project 'GoogleDataTransport'
        ➜ Explicit dependency on target 'GoogleDataTransport_GoogleDataTransport' in project 'GoogleDataTransport'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
    Target 'GoogleDataTransport_GoogleDataTransport' in project 'GoogleDataTransport' (no dependencies)
    Target 'Promises' in project 'Promises'
        ➜ Explicit dependency on target 'Promises' in project 'Promises'
        ➜ Explicit dependency on target 'Promises_Promises' in project 'Promises'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
    Target 'Promises' in project 'Promises'
        ➜ Explicit dependency on target 'Promises_Promises' in project 'Promises'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
    Target 'Promises_Promises' in project 'Promises' (no dependencies)
    Target 'FirebaseSessionsObjC' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreExtension' in project 'Firebase'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
    Target 'FirebaseCoreExtension' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase_FirebaseCoreExtension' in project 'Firebase'
    Target 'Firebase_FirebaseCoreExtension' in project 'Firebase' (no dependencies)
    Target 'Firebase_FirebaseCrashlytics' in project 'Firebase' (no dependencies)
    Target 'FirebaseAnalytics' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseAnalyticsTarget' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseInstallations' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseAnalyticsWrapper' in project 'Firebase'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleAppMeasurement' in project 'GoogleAppMeasurement'
        ➜ Explicit dependency on target 'GULAppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULMethodSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULNetwork' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
    Target 'FirebaseAnalyticsTarget' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseInstallations' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseAnalyticsWrapper' in project 'Firebase'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleAppMeasurement' in project 'GoogleAppMeasurement'
        ➜ Explicit dependency on target 'GULAppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULMethodSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULNetwork' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
    Target 'FirebaseAnalyticsWrapper' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseInstallations' in project 'Firebase'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleAppMeasurement' in project 'GoogleAppMeasurement'
        ➜ Explicit dependency on target 'GULAppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULMethodSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULNetwork' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
    Target 'GoogleAppMeasurement' in project 'GoogleAppMeasurement'
        ➜ Explicit dependency on target 'GoogleAppMeasurementTarget' in project 'GoogleAppMeasurement'
        ➜ Explicit dependency on target 'GULAppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULMethodSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULNetwork' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
    Target 'GoogleAppMeasurementTarget' in project 'GoogleAppMeasurement'
        ➜ Explicit dependency on target 'GULAppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULMethodSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULNetwork' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
    Target 'nanopb' in project 'nanopb'
        ➜ Explicit dependency on target 'nanopb' in project 'nanopb'
        ➜ Explicit dependency on target 'nanopb_nanopb' in project 'nanopb'
    Target 'nanopb' in project 'nanopb'
        ➜ Explicit dependency on target 'nanopb_nanopb' in project 'nanopb'
    Target 'nanopb_nanopb' in project 'nanopb' (no dependencies)
    Target 'GULNetwork' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Network' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Network' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-NSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Reachability' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
    Target 'GULMethodSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-MethodSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-MethodSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
    Target 'GoogleUtilities-MethodSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-MethodSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
    Target 'GoogleUtilities_GoogleUtilities-MethodSwizzler' in project 'GoogleUtilities' (no dependencies)
    Target 'GULAppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-AppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-AppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-NSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Reachability' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Network' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
    Target 'GoogleUtilities-AppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-AppDelegateSwizzler' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-NSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Reachability' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Network' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
    Target 'GoogleUtilities-Network' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Network' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-NSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Reachability' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
    Target 'GoogleUtilities_GoogleUtilities-Network' in project 'GoogleUtilities' (no dependencies)
    Target 'GoogleUtilities-Reachability' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Reachability' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
    Target 'GoogleUtilities_GoogleUtilities-Reachability' in project 'GoogleUtilities' (no dependencies)
    Target 'GoogleUtilities_GoogleUtilities-AppDelegateSwizzler' in project 'GoogleUtilities' (no dependencies)
    Target 'FirebaseInstallations' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase_FirebaseInstallations' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'GULUserDefaults' in project 'GoogleUtilities'
    Target 'GULUserDefaults' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-UserDefaults' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-UserDefaults' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
    Target 'GoogleUtilities-UserDefaults' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-UserDefaults' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
    Target 'GoogleUtilities_GoogleUtilities-UserDefaults' in project 'GoogleUtilities' (no dependencies)
    Target 'Firebase_FirebaseInstallations' in project 'Firebase' (no dependencies)
    Target 'FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase_FirebaseCore' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase' in project 'Firebase'
        ➜ Explicit dependency on target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GULLogger' in project 'GoogleUtilities'
    Target 'GULLogger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
    Target 'GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Logger' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
    Target 'GoogleUtilities_GoogleUtilities-Logger' in project 'GoogleUtilities' (no dependencies)
    Target 'GULEnvironment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
    Target 'GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-Environment' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'third-party-IsAppEncrypted' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
    Target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'Promises_FBLPromises' in project 'Promises'
    Target 'FBLPromises' in project 'Promises'
        ➜ Explicit dependency on target 'Promises_FBLPromises' in project 'Promises'
    Target 'Promises_FBLPromises' in project 'Promises' (no dependencies)
    Target 'third-party-IsAppEncrypted' in project 'GoogleUtilities' (no dependencies)
    Target 'GoogleUtilities_GoogleUtilities-Environment' in project 'GoogleUtilities' (no dependencies)
    Target 'Firebase_FirebaseCore' in project 'Firebase' (no dependencies)
    Target 'FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'Firebase_FirebaseCoreInternal' in project 'Firebase'
        ➜ Explicit dependency on target 'GULNSData' in project 'GoogleUtilities'
    Target 'GULNSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities-NSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-NSData' in project 'GoogleUtilities'
    Target 'GoogleUtilities-NSData' in project 'GoogleUtilities'
        ➜ Explicit dependency on target 'GoogleUtilities_GoogleUtilities-NSData' in project 'GoogleUtilities'
    Target 'GoogleUtilities_GoogleUtilities-NSData' in project 'GoogleUtilities' (no dependencies)
    Target 'Firebase_FirebaseCoreInternal' in project 'Firebase' (no dependencies)
    Target 'Firebase' in project 'Firebase' (no dependencies)
    Target 'GoogleSignIn' in project 'GoogleSignIn'
        ➜ Explicit dependency on target 'GoogleSignIn' in project 'GoogleSignIn'
        ➜ Explicit dependency on target 'GoogleSignIn_GoogleSignIn' in project 'GoogleSignIn'
        ➜ Explicit dependency on target 'AppAuth' in project 'AppAuth'
        ➜ Explicit dependency on target 'GTMAppAuth' in project 'GTMAppAuth'
        ➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
    Target 'GoogleSignIn' in project 'GoogleSignIn'
        ➜ Explicit dependency on target 'GoogleSignIn_GoogleSignIn' in project 'GoogleSignIn'
        ➜ Explicit dependency on target 'AppAuth' in project 'AppAuth'
        ➜ Explicit dependency on target 'GTMAppAuth' in project 'GTMAppAuth'
        ➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
    Target 'GTMAppAuth' in project 'GTMAppAuth'
        ➜ Explicit dependency on target 'GTMAppAuth' in project 'GTMAppAuth'
        ➜ Explicit dependency on target 'GTMAppAuth_GTMAppAuth' in project 'GTMAppAuth'
        ➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
        ➜ Explicit dependency on target 'AppAuthCore' in project 'AppAuth'
    Target 'GTMAppAuth' in project 'GTMAppAuth'
        ➜ Explicit dependency on target 'GTMAppAuth_GTMAppAuth' in project 'GTMAppAuth'
        ➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
        ➜ Explicit dependency on target 'AppAuthCore' in project 'AppAuth'
    Target 'AppAuthCore' in project 'AppAuth'
        ➜ Explicit dependency on target 'AppAuthCore' in project 'AppAuth'
        ➜ Explicit dependency on target 'AppAuth_AppAuthCore' in project 'AppAuth'
    Target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
        ➜ Explicit dependency on target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
        ➜ Explicit dependency on target 'GTMSessionFetcher_GTMSessionFetcherCore' in project 'GTMSessionFetcher'
    Target 'GTMSessionFetcherCore' in project 'GTMSessionFetcher'
        ➜ Explicit dependency on target 'GTMSessionFetcher_GTMSessionFetcherCore' in project 'GTMSessionFetcher'
    Target 'GTMSessionFetcher_GTMSessionFetcherCore' in project 'GTMSessionFetcher' (no dependencies)
    Target 'GTMAppAuth_GTMAppAuth' in project 'GTMAppAuth' (no dependencies)
    Target 'AppAuth' in project 'AppAuth'
        ➜ Explicit dependency on target 'AppAuth' in project 'AppAuth'
        ➜ Explicit dependency on target 'AppAuth_AppAuth' in project 'AppAuth'
        ➜ Explicit dependency on target 'AppAuthCore' in project 'AppAuth'
    Target 'AppAuth' in project 'AppAuth'
        ➜ Explicit dependency on target 'AppAuth_AppAuth' in project 'AppAuth'
        ➜ Explicit dependency on target 'AppAuthCore' in project 'AppAuth'
    Target 'AppAuthCore' in project 'AppAuth'
        ➜ Explicit dependency on target 'AppAuth_AppAuthCore' in project 'AppAuth'
    Target 'AppAuth_AppAuthCore' in project 'AppAuth' (no dependencies)
    Target 'AppAuth_AppAuth' in project 'AppAuth' (no dependencies)
    Target 'GoogleSignIn_GoogleSignIn' in project 'GoogleSignIn' (no dependencies)
    Target 'FontAwesome' in project 'FontAwesome'
        ➜ Explicit dependency on target 'FontAwesome' in project 'FontAwesome'
    Target 'FontAwesome' in project 'FontAwesome' (no dependencies)
    Target 'CHTCollectionViewWaterfallLayout' in project 'CHTCollectionViewWaterfallLayout'
        ➜ Explicit dependency on target 'CHTCollectionViewWaterfallLayout' in project 'CHTCollectionViewWaterfallLayout'
    Target 'CHTCollectionViewWaterfallLayout' in project 'CHTCollectionViewWaterfallLayout' (no dependencies)
    Target 'UIScrollView_InfiniteScroll' in project 'UIScrollView_InfiniteScroll'
        ➜ Explicit dependency on target 'UIScrollView_InfiniteScroll' in project 'UIScrollView_InfiniteScroll'
    Target 'UIScrollView_InfiniteScroll' in project 'UIScrollView_InfiniteScroll' (no dependencies)
    Target 'FSCalendar' in project 'FSCalendar'
        ➜ Explicit dependency on target 'FSCalendar' in project 'FSCalendar'
    Target 'FSCalendar' in project 'FSCalendar' (no dependencies)
    Target 'TOCropViewController' in project 'TOCropViewController'
        ➜ Explicit dependency on target 'TOCropViewController' in project 'TOCropViewController'
        ➜ Explicit dependency on target 'TOCropViewController_TOCropViewController' in project 'TOCropViewController'
    Target 'TOCropViewController' in project 'TOCropViewController'
        ➜ Explicit dependency on target 'TOCropViewController_TOCropViewController' in project 'TOCropViewController'
    Target 'TOCropViewController_TOCropViewController' in project 'TOCropViewController' (no dependencies)
    Target 'Extensions' in project 'SnapInspect'
        ➜ Explicit dependency on target 'DatadogCore' in project 'Datadog'
        ➜ Explicit dependency on target 'DatadogLogs' in project 'Datadog'
        ➜ Explicit dependency on target 'MSAL' in project 'MSAL'
        ➜ Explicit dependency on target 'NotificationBannerSwift' in project 'NotificationBannerSwift'
        ➜ Explicit dependency on target 'SDWebImageSwiftUI' in project 'SDWebImageSwiftUI'
        ➜ Explicit dependency on target 'SwiftUIPullToRefresh' in project 'SwiftUIPullToRefresh'
        ➜ Explicit dependency on target 'SwiftDate' in project 'SwiftDate'
    Target 'SwiftDate' in project 'SwiftDate'
        ➜ Explicit dependency on target 'SwiftDate' in project 'SwiftDate'
        ➜ Explicit dependency on target 'SwiftDate_SwiftDate' in project 'SwiftDate'
    Target 'SwiftDate' in project 'SwiftDate'
        ➜ Explicit dependency on target 'SwiftDate_SwiftDate' in project 'SwiftDate'
    Target 'SwiftDate_SwiftDate' in project 'SwiftDate' (no dependencies)
    Target 'SwiftUIPullToRefresh' in project 'SwiftUIPullToRefresh'
        ➜ Explicit dependency on target 'SwiftUIPullToRefresh' in project 'SwiftUIPullToRefresh'
    Target 'SwiftUIPullToRefresh' in project 'SwiftUIPullToRefresh' (no dependencies)
    Target 'SDWebImageSwiftUI' in project 'SDWebImageSwiftUI'
        ➜ Explicit dependency on target 'SDWebImageSwiftUI' in project 'SDWebImageSwiftUI'
        ➜ Explicit dependency on target 'SDWebImageSwiftUI_SDWebImageSwiftUI' in project 'SDWebImageSwiftUI'
        ➜ Explicit dependency on target 'SDWebImage' in project 'SDWebImage'
    Target 'SDWebImageSwiftUI' in project 'SDWebImageSwiftUI'
        ➜ Explicit dependency on target 'SDWebImageSwiftUI_SDWebImageSwiftUI' in project 'SDWebImageSwiftUI'
        ➜ Explicit dependency on target 'SDWebImage' in project 'SDWebImage'
    Target 'SDWebImage' in project 'SDWebImage'
        ➜ Explicit dependency on target 'SDWebImage' in project 'SDWebImage'
        ➜ Explicit dependency on target 'SDWebImage_SDWebImage' in project 'SDWebImage'
    Target 'SDWebImage' in project 'SDWebImage'
        ➜ Explicit dependency on target 'SDWebImage_SDWebImage' in project 'SDWebImage'
    Target 'SDWebImage_SDWebImage' in project 'SDWebImage' (no dependencies)
    Target 'SDWebImageSwiftUI_SDWebImageSwiftUI' in project 'SDWebImageSwiftUI' (no dependencies)
    Target 'NotificationBannerSwift' in project 'NotificationBannerSwift'
        ➜ Explicit dependency on target 'NotificationBannerSwift' in project 'NotificationBannerSwift'
        ➜ Explicit dependency on target 'MarqueeLabel' in project 'MarqueeLabel'
        ➜ Explicit dependency on target 'SnapKit' in project 'SnapKit'
    Target 'NotificationBannerSwift' in project 'NotificationBannerSwift'
        ➜ Explicit dependency on target 'MarqueeLabel' in project 'MarqueeLabel'
        ➜ Explicit dependency on target 'SnapKit' in project 'SnapKit'
    Target 'SnapKit' in project 'SnapKit'
        ➜ Explicit dependency on target 'SnapKit' in project 'SnapKit'
        ➜ Explicit dependency on target 'SnapKit_SnapKit' in project 'SnapKit'
    Target 'SnapKit' in project 'SnapKit'
        ➜ Explicit dependency on target 'SnapKit_SnapKit' in project 'SnapKit'
    Target 'SnapKit_SnapKit' in project 'SnapKit' (no dependencies)
    Target 'MarqueeLabel' in project 'MarqueeLabel'
        ➜ Explicit dependency on target 'MarqueeLabel' in project 'MarqueeLabel'
        ➜ Explicit dependency on target 'MarqueeLabel_MarqueeLabel' in project 'MarqueeLabel'
    Target 'MarqueeLabel' in project 'MarqueeLabel'
        ➜ Explicit dependency on target 'MarqueeLabel_MarqueeLabel' in project 'MarqueeLabel'
    Target 'MarqueeLabel_MarqueeLabel' in project 'MarqueeLabel' (no dependencies)
    Target 'MSAL' in project 'MSAL' (no dependencies)
    Target 'DatadogLogs' in project 'Datadog'
        ➜ Explicit dependency on target 'DatadogLogs' in project 'Datadog'
        ➜ Explicit dependency on target 'DatadogInternal' in project 'Datadog'
    Target 'DatadogLogs' in project 'Datadog'
        ➜ Explicit dependency on target 'DatadogInternal' in project 'Datadog'
    Target 'DatadogCore' in project 'Datadog'
        ➜ Explicit dependency on target 'DatadogCore' in project 'Datadog'
        ➜ Explicit dependency on target 'Datadog_DatadogCore' in project 'Datadog'
        ➜ Explicit dependency on target 'DatadogInternal' in project 'Datadog'
        ➜ Explicit dependency on target 'DatadogPrivate' in project 'Datadog'
    Target 'DatadogCore' in project 'Datadog'
        ➜ Explicit dependency on target 'Datadog_DatadogCore' in project 'Datadog'
        ➜ Explicit dependency on target 'DatadogInternal' in project 'Datadog'
        ➜ Explicit dependency on target 'DatadogPrivate' in project 'Datadog'
    Target 'DatadogPrivate' in project 'Datadog' (no dependencies)
    Target 'DatadogInternal' in project 'Datadog' (no dependencies)
    Target 'Datadog_DatadogCore' in project 'Datadog' (no dependencies)

GatherProvisioningInputs

CreateBuildDescription

ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache
    cd /Users/<USER>/Documents/Drive/Workspaces/Upworks/snapinspect/SnapInspect3-iOS-New
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.5.sdk -o /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.5-22F76-d5fc8ad4295d2ef488fb7d0f804ce0c4.sdkstatcache

ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache
    cd /Users/<USER>/Documents/Drive/Workspaces/Upworks/snapinspect/SnapInspect3-iOS-New
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -o /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache

Test Suite 'Selected tests' started at 2025-07-20 18:38:27.206.
Test Suite 'AppFeaturesTests.xctest' started at 2025-07-20 18:38:27.206.
Test Suite 'SpeechNormalizerFindBestMatchTests' started at 2025-07-20 18:38:27.207.
Test Case '-[AppFeaturesTests.SpeechNormalizerFindBestMatchTests testFindBestMatchWithPhrases_ComplexScoring]' started.
🔍 DEBUG: findBestMatchWithPhrases
   Spoken text: 'emergency exit sign illuminated and functioning'
   Items count: 4
   Threshold: 0.7
   Spoken variations: ["emergency exit sign illuminated and functioning", "emergency exitsign illuminated and functioning", "emergency exit sign illuminated andfunctioning", "emergency exit signilluminated and functioning", "emergencyexit sign illuminated and functioning", "emergency exit sign illuminatedand functioning"]

   🎯 Evaluating item [0]: 'Exit'

      🔬 DEBUG: matchScore
         Spoken: 'emergency exit sign illuminated and functioning'
         Item: 'Exit'
         Spoken phrases: ["function", "exit", "sign", "illuminate", "emergency"]
         Item phrases: ["exit"]
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'exit' → 'E230', 'function' → 'F523'
            📊 Similarity - Text: 0.125, Phonetic: 0.25, Combined: 0.16249999999999998
            vs spoken phrase: 'exit' → lemma: 'exit'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'exit' → 'E230', 'sign' → 'S250'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'exit' → 'E230', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'exit' → 'E230', 'emergency' → 'E562'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
         Best match for 'exit': 1.0
            ✅ EXACT SUBSTRING MATCH FOUND!
         📊 Final score calculation:
            - Phrase score: 1.0 → boosted: 1.2
            - Substring bonus: 0.5
            - Compound bonus: 0.01702127659574468
            - Partial match bonus: 0.0
            - Specificity bonus: 0.0
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 0.9720212765957448
         ✅ Final matchScore: 0.9720212765957448
      New best variation: 'emergency exit sign illuminated and functioning' with score: 0.9720212765957448
      Is ordinal match: false, matched number: nil

      🔬 DEBUG: matchScore
         Spoken: 'emergency exitsign illuminated and functioning'
         Item: 'Exit'
         Spoken phrases: ["emergency", "exitsign", "function", "illuminate"]
         Item phrases: ["exit"]
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'exit' → 'E230', 'emergency' → 'E562'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'exitsign' → lemma: 'exitsign'
               🔊 Phonetic: 'exit' → 'E230', 'exitsign' → 'E232'
            📊 Similarity - Text: 0.5, Phonetic: 0.75, Combined: 0.575
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'exit' → 'E230', 'function' → 'F523'
            📊 Similarity - Text: 0.125, Phonetic: 0.25, Combined: 0.16249999999999998
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'exit' → 'E230', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
         Best match for 'exit': 0.575
            ✅ EXACT SUBSTRING MATCH FOUND!
         📊 Final score calculation:
            - Phrase score: 0.575 → boosted: 0.575
            - Substring bonus: 0.95
            - Compound bonus: 0.017391304347826087
            - Partial match bonus: 0.0
            - Specificity bonus: 0.0
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 0.723641304347826
            - SUBSTRING PRIORITY OVERRIDE: Boosted score to 0.85
         ✅ Final matchScore: 0.85

      🔬 DEBUG: matchScore
         Spoken: 'emergency exit sign illuminated andfunctioning'
         Item: 'Exit'
         Spoken phrases: ["andfunction", "sign", "emergency", "exit", "illuminate"]
         Item phrases: ["exit"]
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'andfunction' → lemma: 'andfunction'
               🔊 Phonetic: 'exit' → 'E230', 'andfunction' → 'A531'
            📊 Similarity - Text: 0.09090909090909094, Phonetic: 0.25, Combined: 0.13863636363636367
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'exit' → 'E230', 'sign' → 'S250'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'exit' → 'E230', 'emergency' → 'E562'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'exit' → lemma: 'exit'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'exit' → 'E230', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
         Best match for 'exit': 1.0
            ✅ EXACT SUBSTRING MATCH FOUND!
         📊 Final score calculation:
            - Phrase score: 1.0 → boosted: 1.2
            - Substring bonus: 0.95
            - Compound bonus: 0.017391304347826087
            - Partial match bonus: 0.0
            - Specificity bonus: 0.0
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 1.129891304347826
         ✅ Final matchScore: 1.129891304347826
      New best variation: 'emergency exit sign illuminated andfunctioning' with score: 1.129891304347826
      Is ordinal match: false, matched number: nil

      🔬 DEBUG: matchScore
         Spoken: 'emergency exit signilluminated and functioning'
         Item: 'Exit'
         Spoken phrases: ["function", "exit", "emergency", "signilluminat"]
         Item phrases: ["exit"]
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'exit' → 'E230', 'function' → 'F523'
            📊 Similarity - Text: 0.125, Phonetic: 0.25, Combined: 0.16249999999999998
            vs spoken phrase: 'exit' → lemma: 'exit'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'exit' → 'E230', 'emergency' → 'E562'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'signilluminat' → lemma: 'signilluminat'
               🔊 Phonetic: 'exit' → 'E230', 'signilluminat' → 'S254'
            📊 Similarity - Text: 0.15384615384615385, Phonetic: 0.25, Combined: 0.1826923076923077
         Best match for 'exit': 1.0
            ✅ EXACT SUBSTRING MATCH FOUND!
         📊 Final score calculation:
            - Phrase score: 1.0 → boosted: 1.2
            - Substring bonus: 0.95
            - Compound bonus: 0.017391304347826087
            - Partial match bonus: 0.0
            - Specificity bonus: 0.0
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 1.129891304347826
         ✅ Final matchScore: 1.129891304347826

      🔬 DEBUG: matchScore
         Spoken: 'emergencyexit sign illuminated and functioning'
         Item: 'Exit'
         Spoken phrases: ["emergencyexit", "illuminate", "sign", "function"]
         Item phrases: ["exit"]
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'emergencyexit' → lemma: 'emergencyexit'
               🔊 Phonetic: 'exit' → 'E230', 'emergencyexit' → 'E562'
            📊 Similarity - Text: 0.3076923076923077, Phonetic: 0.25, Combined: 0.2903846153846154
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'exit' → 'E230', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'exit' → 'E230', 'sign' → 'S250'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'exit' → 'E230', 'function' → 'F523'
            📊 Similarity - Text: 0.125, Phonetic: 0.25, Combined: 0.16249999999999998
         Best match for 'exit': 0.2903846153846154
            ✅ EXACT SUBSTRING MATCH FOUND!
         📊 Final score calculation:
            - Phrase score: 0.2903846153846154 → boosted: 0.2903846153846154
            - Substring bonus: 0.95
            - Compound bonus: 0.017391304347826087
            - Partial match bonus: 0.0
            - Specificity bonus: 0.0
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 0.5386413043478261
            - SUBSTRING PRIORITY OVERRIDE: Boosted score to 0.85
         ✅ Final matchScore: 0.85

      🔬 DEBUG: matchScore
         Spoken: 'emergency exit sign illuminatedand functioning'
         Item: 'Exit'
         Spoken phrases: ["sign", "exit", "function", "emergency"]
         Item phrases: ["exit"]
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'exit' → 'E230', 'sign' → 'S250'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'exit' → lemma: 'exit'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'exit' → 'E230', 'function' → 'F523'
            📊 Similarity - Text: 0.125, Phonetic: 0.25, Combined: 0.16249999999999998
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'exit' → 'E230', 'emergency' → 'E562'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
         Best match for 'exit': 1.0
            ✅ EXACT SUBSTRING MATCH FOUND!
         📊 Final score calculation:
            - Phrase score: 1.0 → boosted: 1.2
            - Substring bonus: 0.95
            - Compound bonus: 0.017391304347826087
            - Partial match bonus: 0.0
            - Specificity bonus: 0.0
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 1.129891304347826
         ✅ Final matchScore: 1.129891304347826
      Item number: nil
      Contains ordinal indicator: false
      Has number match in list: false
      ⚠️ Single word item penalty when spoken has 6 words
            - Debug FINAL guarantee check:
              - itemCoverageRatio: 1.0
              - hasSignificantPhraseMatch: true
              - commonKeyPhrases.count: 1
              - itemKeyPhrases.count: 1
              - current score: 0.6553843478260869
              - phraseCoverageFromScore: false
            - Applied FINAL minimum score guarantee (50%+ meaningful coverage → score >= 0.7)
      📈 Final score for this item: 0.7
      🏆 NEW BEST! Previous best: 0.0 → New best: 0.7

   🎯 Evaluating item [1]: 'Emergency Exit'

      🔬 DEBUG: matchScore
         Spoken: 'emergency exit sign illuminated and functioning'
         Item: 'Emergency Exit'
         Spoken phrases: ["function", "exit", "sign", "illuminate", "emergency"]
         Item phrases: ["emergency", "exit"]
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'emergency' → 'E562', 'function' → 'F523'
            📊 Similarity - Text: 0.0, Phonetic: 0.25, Combined: 0.075
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'emergency' → 'E562', 'exit' → 'E230'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'emergency' → 'E562', 'sign' → 'S250'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'emergency' → 'E562', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
            vs spoken phrase: 'emergency' → lemma: 'emergency'
            ✅ Exact lemma match! Score: 1.0
         Best match for 'emergency': 1.0
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'exit' → 'E230', 'function' → 'F523'
            📊 Similarity - Text: 0.125, Phonetic: 0.25, Combined: 0.16249999999999998
            vs spoken phrase: 'exit' → lemma: 'exit'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'exit' → 'E230', 'sign' → 'S250'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'exit' → 'E230', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'exit' → 'E230', 'emergency' → 'E562'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
         Best match for 'exit': 1.0
            ✅ EXACT SUBSTRING MATCH FOUND!
         📊 Final score calculation:
            - Phrase score: 1.0 → boosted: 1.2
            - Substring bonus: 0.5
            - Compound bonus: 0.059574468085106386
            - Partial match bonus: 0.15
            - Specificity bonus: 0.375
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 1.5395744680851065
         ✅ Final matchScore: 1.5395744680851065
      New best variation: 'emergency exit sign illuminated and functioning' with score: 1.5395744680851065
      Is ordinal match: false, matched number: nil

      🔬 DEBUG: matchScore
         Spoken: 'emergency exitsign illuminated and functioning'
         Item: 'Emergency Exit'
         Spoken phrases: ["emergency", "exitsign", "function", "illuminate"]
         Item phrases: ["emergency", "exit"]
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'emergency' → lemma: 'emergency'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'exitsign' → lemma: 'exitsign'
               🔊 Phonetic: 'emergency' → 'E562', 'exitsign' → 'E232'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.5, Combined: 0.2277777777777778
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'emergency' → 'E562', 'function' → 'F523'
            📊 Similarity - Text: 0.0, Phonetic: 0.25, Combined: 0.075
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'emergency' → 'E562', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
         Best match for 'emergency': 1.0
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'exit' → 'E230', 'emergency' → 'E562'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'exitsign' → lemma: 'exitsign'
               🔊 Phonetic: 'exit' → 'E230', 'exitsign' → 'E232'
            📊 Similarity - Text: 0.5, Phonetic: 0.75, Combined: 0.575
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'exit' → 'E230', 'function' → 'F523'
            📊 Similarity - Text: 0.125, Phonetic: 0.25, Combined: 0.16249999999999998
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'exit' → 'E230', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
         Best match for 'exit': 0.575
            ✅ EXACT SUBSTRING MATCH FOUND!
         📊 Final score calculation:
            - Phrase score: 0.7875 → boosted: 0.7875
            - Substring bonus: 0.95
            - Compound bonus: 0.060869565217391314
            - Partial match bonus: 0.15
            - Specificity bonus: 0.0
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 1.0552445652173912
         ✅ Final matchScore: 1.0552445652173912

      🔬 DEBUG: matchScore
         Spoken: 'emergency exit sign illuminated andfunctioning'
         Item: 'Emergency Exit'
         Spoken phrases: ["andfunction", "sign", "emergency", "exit", "illuminate"]
         Item phrases: ["emergency", "exit"]
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'andfunction' → lemma: 'andfunction'
               🔊 Phonetic: 'emergency' → 'E562', 'andfunction' → 'A531'
            📊 Similarity - Text: 0.09090909090909094, Phonetic: 0.25, Combined: 0.13863636363636367
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'emergency' → 'E562', 'sign' → 'S250'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
            vs spoken phrase: 'emergency' → lemma: 'emergency'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'emergency' → 'E562', 'exit' → 'E230'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'emergency' → 'E562', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
         Best match for 'emergency': 1.0
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'andfunction' → lemma: 'andfunction'
               🔊 Phonetic: 'exit' → 'E230', 'andfunction' → 'A531'
            📊 Similarity - Text: 0.09090909090909094, Phonetic: 0.25, Combined: 0.13863636363636367
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'exit' → 'E230', 'sign' → 'S250'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'exit' → 'E230', 'emergency' → 'E562'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'exit' → lemma: 'exit'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'exit' → 'E230', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
         Best match for 'exit': 1.0
            ✅ EXACT SUBSTRING MATCH FOUND!
         📊 Final score calculation:
            - Phrase score: 1.0 → boosted: 1.2
            - Substring bonus: 0.95
            - Compound bonus: 0.060869565217391314
            - Partial match bonus: 0.15
            - Specificity bonus: 0.375
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 1.6983695652173914
         ✅ Final matchScore: 1.6983695652173914
      New best variation: 'emergency exit sign illuminated andfunctioning' with score: 1.6983695652173914
      Is ordinal match: false, matched number: nil

      🔬 DEBUG: matchScore
         Spoken: 'emergency exit signilluminated and functioning'
         Item: 'Emergency Exit'
         Spoken phrases: ["function", "exit", "emergency", "signilluminat"]
         Item phrases: ["emergency", "exit"]
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'emergency' → 'E562', 'function' → 'F523'
            📊 Similarity - Text: 0.0, Phonetic: 0.25, Combined: 0.075
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'emergency' → 'E562', 'exit' → 'E230'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'emergency' → lemma: 'emergency'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'signilluminat' → lemma: 'signilluminat'
               🔊 Phonetic: 'emergency' → 'E562', 'signilluminat' → 'S254'
            📊 Similarity - Text: 0.07692307692307687, Phonetic: 0.0, Combined: 0.05384615384615381
         Best match for 'emergency': 1.0
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'exit' → 'E230', 'function' → 'F523'
            📊 Similarity - Text: 0.125, Phonetic: 0.25, Combined: 0.16249999999999998
            vs spoken phrase: 'exit' → lemma: 'exit'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'exit' → 'E230', 'emergency' → 'E562'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'signilluminat' → lemma: 'signilluminat'
               🔊 Phonetic: 'exit' → 'E230', 'signilluminat' → 'S254'
            📊 Similarity - Text: 0.15384615384615385, Phonetic: 0.25, Combined: 0.1826923076923077
         Best match for 'exit': 1.0
            ✅ EXACT SUBSTRING MATCH FOUND!
         📊 Final score calculation:
            - Phrase score: 1.0 → boosted: 1.2
            - Substring bonus: 0.95
            - Compound bonus: 0.060869565217391314
            - Partial match bonus: 0.15
            - Specificity bonus: 0.375
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 1.6983695652173914
         ✅ Final matchScore: 1.6983695652173914

      🔬 DEBUG: matchScore
         Spoken: 'emergencyexit sign illuminated and functioning'
         Item: 'Emergency Exit'
         Spoken phrases: ["emergencyexit", "illuminate", "sign", "function"]
         Item phrases: ["emergency", "exit"]
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'emergencyexit' → lemma: 'emergencyexit'
               🔊 Phonetic: 'emergency' → 'E562', 'emergencyexit' → 'E562'
            📊 Similarity - Text: 0.6923076923076923, Phonetic: 1.0, Combined: 0.7846153846153845
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'emergency' → 'E562', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'emergency' → 'E562', 'sign' → 'S250'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'emergency' → 'E562', 'function' → 'F523'
            📊 Similarity - Text: 0.0, Phonetic: 0.25, Combined: 0.075
         Best match for 'emergency': 0.7846153846153845
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'emergencyexit' → lemma: 'emergencyexit'
               🔊 Phonetic: 'exit' → 'E230', 'emergencyexit' → 'E562'
            📊 Similarity - Text: 0.3076923076923077, Phonetic: 0.25, Combined: 0.2903846153846154
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'exit' → 'E230', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'exit' → 'E230', 'sign' → 'S250'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'exit' → 'E230', 'function' → 'F523'
            📊 Similarity - Text: 0.125, Phonetic: 0.25, Combined: 0.16249999999999998
         Best match for 'exit': 0.2903846153846154
         📊 Final score calculation:
            - Phrase score: 0.5375 → boosted: 0.5375
            - Substring bonus: 0.0
            - Compound bonus: 0.0
            - Partial match bonus: 0.075
            - Specificity bonus: 0.0
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 0.424375
         ✅ Final matchScore: 0.424375

      🔬 DEBUG: matchScore
         Spoken: 'emergency exit sign illuminatedand functioning'
         Item: 'Emergency Exit'
         Spoken phrases: ["sign", "exit", "function", "emergency"]
         Item phrases: ["emergency", "exit"]
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'emergency' → 'E562', 'sign' → 'S250'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'emergency' → 'E562', 'exit' → 'E230'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'emergency' → 'E562', 'function' → 'F523'
            📊 Similarity - Text: 0.0, Phonetic: 0.25, Combined: 0.075
            vs spoken phrase: 'emergency' → lemma: 'emergency'
            ✅ Exact lemma match! Score: 1.0
         Best match for 'emergency': 1.0
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'exit' → 'E230', 'sign' → 'S250'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'exit' → lemma: 'exit'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'exit' → 'E230', 'function' → 'F523'
            📊 Similarity - Text: 0.125, Phonetic: 0.25, Combined: 0.16249999999999998
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'exit' → 'E230', 'emergency' → 'E562'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
         Best match for 'exit': 1.0
            ✅ EXACT SUBSTRING MATCH FOUND!
         📊 Final score calculation:
            - Phrase score: 1.0 → boosted: 1.2
            - Substring bonus: 0.95
            - Compound bonus: 0.060869565217391314
            - Partial match bonus: 0.15
            - Specificity bonus: 0.375
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 1.6983695652173914
         ✅ Final matchScore: 1.6983695652173914
      Item number: nil
      Contains ordinal indicator: false
      Has number match in list: false
      ✅ Complete match bonus with 2 words
      📊 Matching words: 2/2, word count boost applied
            - Debug FINAL guarantee check:
              - itemCoverageRatio: 1.0
              - hasSignificantPhraseMatch: true
              - commonKeyPhrases.count: 2
              - itemKeyPhrases.count: 2
              - current score: 1.106693747826087
              - phraseCoverageFromScore: false
      📈 Final score for this item: 1.106693747826087
      🏆 NEW BEST! Previous best: 0.7 → New best: 1.106693747826087

   🎯 Evaluating item [2]: 'Emergency Exit Sign'

      🔬 DEBUG: matchScore
         Spoken: 'emergency exit sign illuminated and functioning'
         Item: 'Emergency Exit Sign'
         Spoken phrases: ["function", "exit", "sign", "illuminate", "emergency"]
         Item phrases: ["exit", "sign", "emergency"]
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'exit' → 'E230', 'function' → 'F523'
            📊 Similarity - Text: 0.125, Phonetic: 0.25, Combined: 0.16249999999999998
            vs spoken phrase: 'exit' → lemma: 'exit'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'exit' → 'E230', 'sign' → 'S250'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'exit' → 'E230', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'exit' → 'E230', 'emergency' → 'E562'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
         Best match for 'exit': 1.0
         📝 Checking item phrase: 'sign' → lemma: 'sign'
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'sign' → 'S250', 'function' → 'F523'
            📊 Similarity - Text: 0.25, Phonetic: 0.0, Combined: 0.175
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'sign' → 'S250', 'exit' → 'E230'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'sign' → lemma: 'sign'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'sign' → 'S250', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.25, Combined: 0.14499999999999996
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'sign' → 'S250', 'emergency' → 'E562'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
         Best match for 'sign': 1.0
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'emergency' → 'E562', 'function' → 'F523'
            📊 Similarity - Text: 0.0, Phonetic: 0.25, Combined: 0.075
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'emergency' → 'E562', 'exit' → 'E230'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'emergency' → 'E562', 'sign' → 'S250'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'emergency' → 'E562', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
            vs spoken phrase: 'emergency' → lemma: 'emergency'
            ✅ Exact lemma match! Score: 1.0
         Best match for 'emergency': 1.0
            ✅ EXACT SUBSTRING MATCH FOUND!
         📊 Final score calculation:
            - Phrase score: 1.0 → boosted: 1.2
            - Substring bonus: 0.95
            - Compound bonus: 0.08085106382978724
            - Partial match bonus: 0.15
            - Specificity bonus: 0.9
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 2.2433510638297873
         ✅ Final matchScore: 2.2433510638297873
      New best variation: 'emergency exit sign illuminated and functioning' with score: 2.2433510638297873
      Is ordinal match: false, matched number: nil

      🔬 DEBUG: matchScore
         Spoken: 'emergency exitsign illuminated and functioning'
         Item: 'Emergency Exit Sign'
         Spoken phrases: ["emergency", "exitsign", "function", "illuminate"]
         Item phrases: ["exit", "sign", "emergency"]
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'exit' → 'E230', 'emergency' → 'E562'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'exitsign' → lemma: 'exitsign'
               🔊 Phonetic: 'exit' → 'E230', 'exitsign' → 'E232'
            📊 Similarity - Text: 0.5, Phonetic: 0.75, Combined: 0.575
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'exit' → 'E230', 'function' → 'F523'
            📊 Similarity - Text: 0.125, Phonetic: 0.25, Combined: 0.16249999999999998
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'exit' → 'E230', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
         Best match for 'exit': 0.575
         📝 Checking item phrase: 'sign' → lemma: 'sign'
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'sign' → 'S250', 'emergency' → 'E562'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
            vs spoken phrase: 'exitsign' → lemma: 'exitsign'
               🔊 Phonetic: 'sign' → 'S250', 'exitsign' → 'E232'
            📊 Similarity - Text: 0.5, Phonetic: 0.25, Combined: 0.425
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'sign' → 'S250', 'function' → 'F523'
            📊 Similarity - Text: 0.25, Phonetic: 0.0, Combined: 0.175
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'sign' → 'S250', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.25, Combined: 0.14499999999999996
         Best match for 'sign': 0.425
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'emergency' → lemma: 'emergency'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'exitsign' → lemma: 'exitsign'
               🔊 Phonetic: 'emergency' → 'E562', 'exitsign' → 'E232'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.5, Combined: 0.2277777777777778
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'emergency' → 'E562', 'function' → 'F523'
            📊 Similarity - Text: 0.0, Phonetic: 0.25, Combined: 0.075
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'emergency' → 'E562', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
         Best match for 'emergency': 1.0
         📊 Final score calculation:
            - Phrase score: 0.6666666666666666 → boosted: 0.6666666666666666
            - Substring bonus: 0.05777777777777778
            - Compound bonus: 0.0
            - Partial match bonus: 0.12
            - Specificity bonus: 0.0
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 0.5735555555555556
         ✅ Final matchScore: 0.5735555555555556

      🔬 DEBUG: matchScore
         Spoken: 'emergency exit sign illuminated andfunctioning'
         Item: 'Emergency Exit Sign'
         Spoken phrases: ["andfunction", "sign", "emergency", "exit", "illuminate"]
         Item phrases: ["exit", "sign", "emergency"]
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'andfunction' → lemma: 'andfunction'
               🔊 Phonetic: 'exit' → 'E230', 'andfunction' → 'A531'
            📊 Similarity - Text: 0.09090909090909094, Phonetic: 0.25, Combined: 0.13863636363636367
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'exit' → 'E230', 'sign' → 'S250'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'exit' → 'E230', 'emergency' → 'E562'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'exit' → lemma: 'exit'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'exit' → 'E230', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
         Best match for 'exit': 1.0
         📝 Checking item phrase: 'sign' → lemma: 'sign'
            vs spoken phrase: 'andfunction' → lemma: 'andfunction'
               🔊 Phonetic: 'sign' → 'S250', 'andfunction' → 'A531'
            📊 Similarity - Text: 0.18181818181818177, Phonetic: 0.0, Combined: 0.12727272727272723
            vs spoken phrase: 'sign' → lemma: 'sign'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'sign' → 'S250', 'emergency' → 'E562'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'sign' → 'S250', 'exit' → 'E230'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'sign' → 'S250', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.25, Combined: 0.14499999999999996
         Best match for 'sign': 1.0
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'andfunction' → lemma: 'andfunction'
               🔊 Phonetic: 'emergency' → 'E562', 'andfunction' → 'A531'
            📊 Similarity - Text: 0.09090909090909094, Phonetic: 0.25, Combined: 0.13863636363636367
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'emergency' → 'E562', 'sign' → 'S250'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
            vs spoken phrase: 'emergency' → lemma: 'emergency'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'emergency' → 'E562', 'exit' → 'E230'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'emergency' → 'E562', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
         Best match for 'emergency': 1.0
            ✅ EXACT SUBSTRING MATCH FOUND!
         📊 Final score calculation:
            - Phrase score: 1.0 → boosted: 1.2
            - Substring bonus: 0.95
            - Compound bonus: 0.08260869565217392
            - Partial match bonus: 0.15
            - Specificity bonus: 0.9
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 2.2451086956521737
         ✅ Final matchScore: 2.2451086956521737
      New best variation: 'emergency exit sign illuminated andfunctioning' with score: 2.2451086956521737
      Is ordinal match: false, matched number: nil

      🔬 DEBUG: matchScore
         Spoken: 'emergency exit signilluminated and functioning'
         Item: 'Emergency Exit Sign'
         Spoken phrases: ["function", "exit", "emergency", "signilluminat"]
         Item phrases: ["exit", "sign", "emergency"]
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'exit' → 'E230', 'function' → 'F523'
            📊 Similarity - Text: 0.125, Phonetic: 0.25, Combined: 0.16249999999999998
            vs spoken phrase: 'exit' → lemma: 'exit'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'exit' → 'E230', 'emergency' → 'E562'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'signilluminat' → lemma: 'signilluminat'
               🔊 Phonetic: 'exit' → 'E230', 'signilluminat' → 'S254'
            📊 Similarity - Text: 0.15384615384615385, Phonetic: 0.25, Combined: 0.1826923076923077
         Best match for 'exit': 1.0
         📝 Checking item phrase: 'sign' → lemma: 'sign'
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'sign' → 'S250', 'function' → 'F523'
            📊 Similarity - Text: 0.25, Phonetic: 0.0, Combined: 0.175
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'sign' → 'S250', 'exit' → 'E230'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'sign' → 'S250', 'emergency' → 'E562'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
            vs spoken phrase: 'signilluminat' → lemma: 'signilluminat'
               🔊 Phonetic: 'sign' → 'S250', 'signilluminat' → 'S254'
            📊 Similarity - Text: 0.3076923076923077, Phonetic: 0.75, Combined: 0.4403846153846154
         Best match for 'sign': 0.4403846153846154
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'emergency' → 'E562', 'function' → 'F523'
            📊 Similarity - Text: 0.0, Phonetic: 0.25, Combined: 0.075
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'emergency' → 'E562', 'exit' → 'E230'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'emergency' → lemma: 'emergency'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'signilluminat' → lemma: 'signilluminat'
               🔊 Phonetic: 'emergency' → 'E562', 'signilluminat' → 'S254'
            📊 Similarity - Text: 0.07692307692307687, Phonetic: 0.0, Combined: 0.05384615384615381
         Best match for 'emergency': 1.0
            ✅ EXACT SUBSTRING MATCH FOUND!
         📊 Final score calculation:
            - Phrase score: 0.8134615384615383 → boosted: 0.8948076923076923
            - Substring bonus: 0.95
            - Compound bonus: 0.08260869565217392
            - Partial match bonus: 0.15
            - Specificity bonus: 0.24999999999999997
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 1.3967336956521739
         ✅ Final matchScore: 1.3967336956521739

      🔬 DEBUG: matchScore
         Spoken: 'emergencyexit sign illuminated and functioning'
         Item: 'Emergency Exit Sign'
         Spoken phrases: ["emergencyexit", "illuminate", "sign", "function"]
         Item phrases: ["exit", "sign", "emergency"]
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'emergencyexit' → lemma: 'emergencyexit'
               🔊 Phonetic: 'exit' → 'E230', 'emergencyexit' → 'E562'
            📊 Similarity - Text: 0.3076923076923077, Phonetic: 0.25, Combined: 0.2903846153846154
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'exit' → 'E230', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'exit' → 'E230', 'sign' → 'S250'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'exit' → 'E230', 'function' → 'F523'
            📊 Similarity - Text: 0.125, Phonetic: 0.25, Combined: 0.16249999999999998
         Best match for 'exit': 0.2903846153846154
         📝 Checking item phrase: 'sign' → lemma: 'sign'
            vs spoken phrase: 'emergencyexit' → lemma: 'emergencyexit'
               🔊 Phonetic: 'sign' → 'S250', 'emergencyexit' → 'E562'
            📊 Similarity - Text: 0.15384615384615385, Phonetic: 0.0, Combined: 0.1076923076923077
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'sign' → 'S250', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.25, Combined: 0.14499999999999996
            vs spoken phrase: 'sign' → lemma: 'sign'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'sign' → 'S250', 'function' → 'F523'
            📊 Similarity - Text: 0.25, Phonetic: 0.0, Combined: 0.175
         Best match for 'sign': 1.0
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'emergencyexit' → lemma: 'emergencyexit'
               🔊 Phonetic: 'emergency' → 'E562', 'emergencyexit' → 'E562'
            📊 Similarity - Text: 0.6923076923076923, Phonetic: 1.0, Combined: 0.7846153846153845
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'emergency' → 'E562', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'emergency' → 'E562', 'sign' → 'S250'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'emergency' → 'E562', 'function' → 'F523'
            📊 Similarity - Text: 0.0, Phonetic: 0.25, Combined: 0.075
         Best match for 'emergency': 0.7846153846153845
         📊 Final score calculation:
            - Phrase score: 0.6916666666666668 → boosted: 0.6916666666666668
            - Substring bonus: 0.05777777777777778
            - Compound bonus: 0.0
            - Partial match bonus: 0.075
            - Specificity bonus: 0.0
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 0.5448055555555555
         ✅ Final matchScore: 0.5448055555555555

      🔬 DEBUG: matchScore
         Spoken: 'emergency exit sign illuminatedand functioning'
         Item: 'Emergency Exit Sign'
         Spoken phrases: ["sign", "exit", "function", "emergency"]
         Item phrases: ["exit", "sign", "emergency"]
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'exit' → 'E230', 'sign' → 'S250'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'exit' → lemma: 'exit'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'exit' → 'E230', 'function' → 'F523'
            📊 Similarity - Text: 0.125, Phonetic: 0.25, Combined: 0.16249999999999998
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'exit' → 'E230', 'emergency' → 'E562'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
         Best match for 'exit': 1.0
         📝 Checking item phrase: 'sign' → lemma: 'sign'
            vs spoken phrase: 'sign' → lemma: 'sign'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'sign' → 'S250', 'exit' → 'E230'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'sign' → 'S250', 'function' → 'F523'
            📊 Similarity - Text: 0.25, Phonetic: 0.0, Combined: 0.175
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'sign' → 'S250', 'emergency' → 'E562'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
         Best match for 'sign': 1.0
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'emergency' → 'E562', 'sign' → 'S250'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'emergency' → 'E562', 'exit' → 'E230'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'emergency' → 'E562', 'function' → 'F523'
            📊 Similarity - Text: 0.0, Phonetic: 0.25, Combined: 0.075
            vs spoken phrase: 'emergency' → lemma: 'emergency'
            ✅ Exact lemma match! Score: 1.0
         Best match for 'emergency': 1.0
            ✅ EXACT SUBSTRING MATCH FOUND!
         📊 Final score calculation:
            - Phrase score: 1.0 → boosted: 1.2
            - Substring bonus: 0.95
            - Compound bonus: 0.08260869565217392
            - Partial match bonus: 0.15
            - Specificity bonus: 0.9
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 2.2451086956521737
         ✅ Final matchScore: 2.2451086956521737
      Item number: nil
      Contains ordinal indicator: false
      Has number match in list: false
      ✅ Complete match bonus with 3 words
      📊 Matching words: 3/3, word count boost applied
            - Debug FINAL guarantee check:
              - itemCoverageRatio: 1.0
              - hasSignificantPhraseMatch: true
              - commonKeyPhrases.count: 3
              - itemKeyPhrases.count: 3
              - current score: 1.0117424083913042
              - phraseCoverageFromScore: false
      📈 Final score for this item: 1.0117424083913042

   🎯 Evaluating item [3]: 'Emergency Exit Sign - Illuminated'

      🔬 DEBUG: matchScore
         Spoken: 'emergency exit sign illuminated and functioning'
         Item: 'Emergency Exit Sign - Illuminated'
         Spoken phrases: ["function", "exit", "sign", "illuminate", "emergency"]
         Item phrases: ["illuminate", "emergency", "sign", "exit"]
         📝 Checking item phrase: 'illuminate' → lemma: 'illuminate'
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'illuminate' → 'I455', 'function' → 'F523'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'illuminate' → 'I455', 'exit' → 'E230'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'illuminate' → 'I455', 'sign' → 'S250'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.25, Combined: 0.14499999999999996
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'illuminate' → 'I455', 'emergency' → 'E562'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
         Best match for 'illuminate': 1.0
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'emergency' → 'E562', 'function' → 'F523'
            📊 Similarity - Text: 0.0, Phonetic: 0.25, Combined: 0.075
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'emergency' → 'E562', 'exit' → 'E230'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'emergency' → 'E562', 'sign' → 'S250'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'emergency' → 'E562', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
            vs spoken phrase: 'emergency' → lemma: 'emergency'
            ✅ Exact lemma match! Score: 1.0
         Best match for 'emergency': 1.0
         📝 Checking item phrase: 'sign' → lemma: 'sign'
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'sign' → 'S250', 'function' → 'F523'
            📊 Similarity - Text: 0.25, Phonetic: 0.0, Combined: 0.175
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'sign' → 'S250', 'exit' → 'E230'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'sign' → lemma: 'sign'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'sign' → 'S250', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.25, Combined: 0.14499999999999996
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'sign' → 'S250', 'emergency' → 'E562'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
         Best match for 'sign': 1.0
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'exit' → 'E230', 'function' → 'F523'
            📊 Similarity - Text: 0.125, Phonetic: 0.25, Combined: 0.16249999999999998
            vs spoken phrase: 'exit' → lemma: 'exit'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'exit' → 'E230', 'sign' → 'S250'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'exit' → 'E230', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'exit' → 'E230', 'emergency' → 'E562'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
         Best match for 'exit': 1.0
         📊 Final score calculation:
            - Phrase score: 1.0 → boosted: 1.2
            - Substring bonus: 0.35
            - Compound bonus: 0.0
            - Partial match bonus: 0.12
            - Specificity bonus: 2.3
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 3.3225
         ✅ Final matchScore: 3.3225
      New best variation: 'emergency exit sign illuminated and functioning' with score: 3.3225
      Is ordinal match: false, matched number: nil

      🔬 DEBUG: matchScore
         Spoken: 'emergency exitsign illuminated and functioning'
         Item: 'Emergency Exit Sign - Illuminated'
         🔊 Word phonetic match: 'emergency' [E562] vs 'emergency' [E562]
         Spoken phrases: ["emergency", "exitsign", "function", "illuminate"]
         Item phrases: ["illuminate", "emergency", "sign", "exit"]
         📝 Checking item phrase: 'illuminate' → lemma: 'illuminate'
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'illuminate' → 'I455', 'emergency' → 'E562'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
            vs spoken phrase: 'exitsign' → lemma: 'exitsign'
               🔊 Phonetic: 'illuminate' → 'I455', 'exitsign' → 'E232'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'illuminate' → 'I455', 'function' → 'F523'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
            ✅ Exact lemma match! Score: 1.0
         Best match for 'illuminate': 1.0
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'emergency' → lemma: 'emergency'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'exitsign' → lemma: 'exitsign'
               🔊 Phonetic: 'emergency' → 'E562', 'exitsign' → 'E232'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.5, Combined: 0.2277777777777778
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'emergency' → 'E562', 'function' → 'F523'
            📊 Similarity - Text: 0.0, Phonetic: 0.25, Combined: 0.075
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'emergency' → 'E562', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
         Best match for 'emergency': 1.0
         📝 Checking item phrase: 'sign' → lemma: 'sign'
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'sign' → 'S250', 'emergency' → 'E562'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
            vs spoken phrase: 'exitsign' → lemma: 'exitsign'
               🔊 Phonetic: 'sign' → 'S250', 'exitsign' → 'E232'
            📊 Similarity - Text: 0.5, Phonetic: 0.25, Combined: 0.425
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'sign' → 'S250', 'function' → 'F523'
            📊 Similarity - Text: 0.25, Phonetic: 0.0, Combined: 0.175
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'sign' → 'S250', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.25, Combined: 0.14499999999999996
         Best match for 'sign': 0.425
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'exit' → 'E230', 'emergency' → 'E562'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'exitsign' → lemma: 'exitsign'
               🔊 Phonetic: 'exit' → 'E230', 'exitsign' → 'E232'
            📊 Similarity - Text: 0.5, Phonetic: 0.75, Combined: 0.575
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'exit' → 'E230', 'function' → 'F523'
            📊 Similarity - Text: 0.125, Phonetic: 0.25, Combined: 0.16249999999999998
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'exit' → 'E230', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
         Best match for 'exit': 0.575
         📊 Final score calculation:
            - Phrase score: 0.75 → boosted: 0.75
            - Substring bonus: 0.075
            - Compound bonus: 0.0
            - Partial match bonus: 0.12
            - Specificity bonus: 0.0
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 0.63375
            - Applied minimum score guarantee (50%+ meaningful coverage → score >= 0.7)
         ✅ Final matchScore: 0.7

      🔬 DEBUG: matchScore
         Spoken: 'emergency exit sign illuminated andfunctioning'
         Item: 'Emergency Exit Sign - Illuminated'
         🔊 Word phonetic match: 'emergency' [E562] vs 'emergency' [E562]
         🔊 Word phonetic match: 'exit' [E230] vs 'exit' [E230]
         🔊 Word phonetic match: 'sign' [S250] vs 'sign' [S250]
         Spoken phrases: ["andfunction", "sign", "emergency", "exit", "illuminate"]
         Item phrases: ["illuminate", "emergency", "sign", "exit"]
         📝 Checking item phrase: 'illuminate' → lemma: 'illuminate'
            vs spoken phrase: 'andfunction' → lemma: 'andfunction'
               🔊 Phonetic: 'illuminate' → 'I455', 'andfunction' → 'A531'
            📊 Similarity - Text: 0.09090909090909094, Phonetic: 0.0, Combined: 0.06363636363636366
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'illuminate' → 'I455', 'sign' → 'S250'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.25, Combined: 0.14499999999999996
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'illuminate' → 'I455', 'emergency' → 'E562'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'illuminate' → 'I455', 'exit' → 'E230'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
            ✅ Exact lemma match! Score: 1.0
         Best match for 'illuminate': 1.0
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'andfunction' → lemma: 'andfunction'
               🔊 Phonetic: 'emergency' → 'E562', 'andfunction' → 'A531'
            📊 Similarity - Text: 0.09090909090909094, Phonetic: 0.25, Combined: 0.13863636363636367
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'emergency' → 'E562', 'sign' → 'S250'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
            vs spoken phrase: 'emergency' → lemma: 'emergency'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'emergency' → 'E562', 'exit' → 'E230'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'emergency' → 'E562', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
         Best match for 'emergency': 1.0
         📝 Checking item phrase: 'sign' → lemma: 'sign'
            vs spoken phrase: 'andfunction' → lemma: 'andfunction'
               🔊 Phonetic: 'sign' → 'S250', 'andfunction' → 'A531'
            📊 Similarity - Text: 0.18181818181818177, Phonetic: 0.0, Combined: 0.12727272727272723
            vs spoken phrase: 'sign' → lemma: 'sign'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'sign' → 'S250', 'emergency' → 'E562'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'sign' → 'S250', 'exit' → 'E230'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'sign' → 'S250', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.25, Combined: 0.14499999999999996
         Best match for 'sign': 1.0
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'andfunction' → lemma: 'andfunction'
               🔊 Phonetic: 'exit' → 'E230', 'andfunction' → 'A531'
            📊 Similarity - Text: 0.09090909090909094, Phonetic: 0.25, Combined: 0.13863636363636367
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'exit' → 'E230', 'sign' → 'S250'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'exit' → 'E230', 'emergency' → 'E562'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'exit' → lemma: 'exit'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'exit' → 'E230', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
         Best match for 'exit': 1.0
         📊 Final score calculation:
            - Phrase score: 1.0 → boosted: 1.2
            - Substring bonus: 0.35
            - Compound bonus: 0.0
            - Partial match bonus: 0.12
            - Specificity bonus: 2.3
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 3.3225
         ✅ Final matchScore: 3.3225

      🔬 DEBUG: matchScore
         Spoken: 'emergency exit signilluminated and functioning'
         Item: 'Emergency Exit Sign - Illuminated'
         🔊 Word phonetic match: 'emergency' [E562] vs 'emergency' [E562]
         🔊 Word phonetic match: 'exit' [E230] vs 'exit' [E230]
         Spoken phrases: ["function", "exit", "emergency", "signilluminat"]
         Item phrases: ["illuminate", "emergency", "sign", "exit"]
         📝 Checking item phrase: 'illuminate' → lemma: 'illuminate'
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'illuminate' → 'I455', 'function' → 'F523'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'illuminate' → 'I455', 'exit' → 'E230'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'illuminate' → 'I455', 'emergency' → 'E562'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
            vs spoken phrase: 'signilluminat' → lemma: 'signilluminat'
               🔊 Phonetic: 'illuminate' → 'I455', 'signilluminat' → 'S254'
            📊 Similarity - Text: 0.6153846153846154, Phonetic: 0.25, Combined: 0.5057692307692307
         Best match for 'illuminate': 0.5057692307692307
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'emergency' → 'E562', 'function' → 'F523'
            📊 Similarity - Text: 0.0, Phonetic: 0.25, Combined: 0.075
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'emergency' → 'E562', 'exit' → 'E230'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'emergency' → lemma: 'emergency'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'signilluminat' → lemma: 'signilluminat'
               🔊 Phonetic: 'emergency' → 'E562', 'signilluminat' → 'S254'
            📊 Similarity - Text: 0.07692307692307687, Phonetic: 0.0, Combined: 0.05384615384615381
         Best match for 'emergency': 1.0
         📝 Checking item phrase: 'sign' → lemma: 'sign'
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'sign' → 'S250', 'function' → 'F523'
            📊 Similarity - Text: 0.25, Phonetic: 0.0, Combined: 0.175
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'sign' → 'S250', 'exit' → 'E230'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'sign' → 'S250', 'emergency' → 'E562'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
            vs spoken phrase: 'signilluminat' → lemma: 'signilluminat'
               🔊 Phonetic: 'sign' → 'S250', 'signilluminat' → 'S254'
            📊 Similarity - Text: 0.3076923076923077, Phonetic: 0.75, Combined: 0.4403846153846154
         Best match for 'sign': 0.4403846153846154
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'exit' → 'E230', 'function' → 'F523'
            📊 Similarity - Text: 0.125, Phonetic: 0.25, Combined: 0.16249999999999998
            vs spoken phrase: 'exit' → lemma: 'exit'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'exit' → 'E230', 'emergency' → 'E562'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'signilluminat' → lemma: 'signilluminat'
               🔊 Phonetic: 'exit' → 'E230', 'signilluminat' → 'S254'
            📊 Similarity - Text: 0.15384615384615385, Phonetic: 0.25, Combined: 0.1826923076923077
         Best match for 'exit': 1.0
         📊 Final score calculation:
            - Phrase score: 0.7365384615384616 → boosted: 0.7365384615384616
            - Substring bonus: 0.075
            - Compound bonus: 0.0
            - Partial match bonus: 0.12
            - Specificity bonus: 0.0
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 0.6250000000000001
            - Applied minimum score guarantee (50%+ meaningful coverage → score >= 0.7)
         ✅ Final matchScore: 0.7

      🔬 DEBUG: matchScore
         Spoken: 'emergencyexit sign illuminated and functioning'
         Item: 'Emergency Exit Sign - Illuminated'
         🔊 Word phonetic match: 'emergencyexit' [E562] vs 'emergency' [E562]
         Spoken phrases: ["emergencyexit", "illuminate", "sign", "function"]
         Item phrases: ["illuminate", "emergency", "sign", "exit"]
         📝 Checking item phrase: 'illuminate' → lemma: 'illuminate'
            vs spoken phrase: 'emergencyexit' → lemma: 'emergencyexit'
               🔊 Phonetic: 'illuminate' → 'I455', 'emergencyexit' → 'E562'
            📊 Similarity - Text: 0.15384615384615385, Phonetic: 0.0, Combined: 0.1076923076923077
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'illuminate' → 'I455', 'sign' → 'S250'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.25, Combined: 0.14499999999999996
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'illuminate' → 'I455', 'function' → 'F523'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
         Best match for 'illuminate': 1.0
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'emergencyexit' → lemma: 'emergencyexit'
               🔊 Phonetic: 'emergency' → 'E562', 'emergencyexit' → 'E562'
            📊 Similarity - Text: 0.6923076923076923, Phonetic: 1.0, Combined: 0.7846153846153845
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'emergency' → 'E562', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'emergency' → 'E562', 'sign' → 'S250'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'emergency' → 'E562', 'function' → 'F523'
            📊 Similarity - Text: 0.0, Phonetic: 0.25, Combined: 0.075
         Best match for 'emergency': 0.7846153846153845
         📝 Checking item phrase: 'sign' → lemma: 'sign'
            vs spoken phrase: 'emergencyexit' → lemma: 'emergencyexit'
               🔊 Phonetic: 'sign' → 'S250', 'emergencyexit' → 'E562'
            📊 Similarity - Text: 0.15384615384615385, Phonetic: 0.0, Combined: 0.1076923076923077
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'sign' → 'S250', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.25, Combined: 0.14499999999999996
            vs spoken phrase: 'sign' → lemma: 'sign'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'sign' → 'S250', 'function' → 'F523'
            📊 Similarity - Text: 0.25, Phonetic: 0.0, Combined: 0.175
         Best match for 'sign': 1.0
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'emergencyexit' → lemma: 'emergencyexit'
               🔊 Phonetic: 'exit' → 'E230', 'emergencyexit' → 'E562'
            📊 Similarity - Text: 0.3076923076923077, Phonetic: 0.25, Combined: 0.2903846153846154
            vs spoken phrase: 'illuminate' → lemma: 'illuminate'
               🔊 Phonetic: 'exit' → 'E230', 'illuminate' → 'I455'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'exit' → 'E230', 'sign' → 'S250'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'exit' → 'E230', 'function' → 'F523'
            📊 Similarity - Text: 0.125, Phonetic: 0.25, Combined: 0.16249999999999998
         Best match for 'exit': 0.2903846153846154
         📊 Final score calculation:
            - Phrase score: 0.7687499999999999 → boosted: 0.7687499999999999
            - Substring bonus: 0.075
            - Compound bonus: 0.0
            - Partial match bonus: 0.075
            - Specificity bonus: 0.0
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 0.6009374999999999
            - Applied minimum score guarantee (50%+ meaningful coverage → score >= 0.7)
         ✅ Final matchScore: 0.7

      🔬 DEBUG: matchScore
         Spoken: 'emergency exit sign illuminatedand functioning'
         Item: 'Emergency Exit Sign - Illuminated'
         🔊 Word phonetic match: 'emergency' [E562] vs 'emergency' [E562]
         🔊 Word phonetic match: 'exit' [E230] vs 'exit' [E230]
         🔊 Word phonetic match: 'sign' [S250] vs 'sign' [S250]
         Spoken phrases: ["sign", "exit", "function", "emergency"]
         Item phrases: ["illuminate", "emergency", "sign", "exit"]
         📝 Checking item phrase: 'illuminate' → lemma: 'illuminate'
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'illuminate' → 'I455', 'sign' → 'S250'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.25, Combined: 0.14499999999999996
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'illuminate' → 'I455', 'exit' → 'E230'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'illuminate' → 'I455', 'function' → 'F523'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'illuminate' → 'I455', 'emergency' → 'E562'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
         Best match for 'illuminate': 0.14499999999999996
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'emergency' → 'E562', 'sign' → 'S250'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'emergency' → 'E562', 'exit' → 'E230'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'emergency' → 'E562', 'function' → 'F523'
            📊 Similarity - Text: 0.0, Phonetic: 0.25, Combined: 0.075
            vs spoken phrase: 'emergency' → lemma: 'emergency'
            ✅ Exact lemma match! Score: 1.0
         Best match for 'emergency': 1.0
         📝 Checking item phrase: 'sign' → lemma: 'sign'
            vs spoken phrase: 'sign' → lemma: 'sign'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'sign' → 'S250', 'exit' → 'E230'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'sign' → 'S250', 'function' → 'F523'
            📊 Similarity - Text: 0.25, Phonetic: 0.0, Combined: 0.175
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'sign' → 'S250', 'emergency' → 'E562'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
         Best match for 'sign': 1.0
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'exit' → 'E230', 'sign' → 'S250'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'exit' → lemma: 'exit'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'function' → lemma: 'function'
               🔊 Phonetic: 'exit' → 'E230', 'function' → 'F523'
            📊 Similarity - Text: 0.125, Phonetic: 0.25, Combined: 0.16249999999999998
            vs spoken phrase: 'emergency' → lemma: 'emergency'
               🔊 Phonetic: 'exit' → 'E230', 'emergency' → 'E562'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
         Best match for 'exit': 1.0
         📊 Final score calculation:
            - Phrase score: 0.78625 → boosted: 0.78625
            - Substring bonus: 0.11249999999999999
            - Compound bonus: 0.0
            - Partial match bonus: 0.12
            - Specificity bonus: 1.6
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 2.2704375
         ✅ Final matchScore: 2.2704375
      Item number: nil
      Contains ordinal indicator: false
      Has number match in list: false
      ✅ Complete match bonus with 4 words
      📊 Matching words: 4/4, word count boost applied
            - Debug FINAL guarantee check:
              - itemCoverageRatio: 0.8
              - hasSignificantPhraseMatch: true
              - commonKeyPhrases.count: 4
              - itemKeyPhrases.count: 4
              - current score: 0.9892835959599999
              - phraseCoverageFromScore: true
      📈 Final score for this item: 0.9892835959599999

📊 FINAL RESULTS:
   Best index: 1
   Best score: 1.106693747826087
   Threshold: 0.7
/Users/<USER>/Documents/Drive/Workspaces/Upworks/snapinspect/SnapInspect3-iOS-New/SnapInspect/Tests/AppFeaturesTests/SpeechNormalizerFindBestMatchTests.swift:98: error: -[AppFeaturesTests.SpeechNormalizerFindBestMatchTests testFindBestMatchWithPhrases_ComplexScoring] : XCTAssertEqual failed: ("1") is not equal to ("3") - Should prefer most specific match
🔍 DEBUG: findBestMatchWithPhrases
   Spoken text: 'exit sign'
   Items count: 4
   Threshold: 0.7
   Spoken variations: ["exit sign", "exitsign"]

   🎯 Evaluating item [0]: 'Exit'

      🔬 DEBUG: matchScore
         Spoken: 'exit sign'
         Item: 'Exit'
         Spoken phrases: ["exit", "sign"]
         Item phrases: ["exit"]
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'exit' → lemma: 'exit'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'exit' → 'E230', 'sign' → 'S250'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
         Best match for 'exit': 1.0
            ✅ EXACT SUBSTRING MATCH FOUND!
         📊 Final score calculation:
            - Phrase score: 1.0 → boosted: 1.2
            - Substring bonus: 0.95
            - Compound bonus: 0.08888888888888889
            - Partial match bonus: 0.15
            - Specificity bonus: 0.0
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 1.3513888888888888
         ✅ Final matchScore: 1.3513888888888888
      New best variation: 'exit sign' with score: 1.3513888888888888
      Is ordinal match: false, matched number: nil

      🔬 DEBUG: matchScore
         Spoken: 'exitsign'
         Item: 'Exit'
         Spoken phrases: ["exitsign"]
         Item phrases: ["exit"]
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'exitsign' → lemma: 'exitsign'
               🔊 Phonetic: 'exit' → 'E230', 'exitsign' → 'E232'
            📊 Similarity - Text: 0.5, Phonetic: 0.75, Combined: 0.575
         Best match for 'exit': 0.575
            ✅ EXACT SUBSTRING MATCH FOUND!
         📊 Final score calculation:
            - Phrase score: 0.575 → boosted: 0.575
            - Substring bonus: 0.95
            - Compound bonus: 0.1
            - Partial match bonus: 0.15
            - Specificity bonus: 0.0
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 0.9562499999999999
         ✅ Final matchScore: 0.9562499999999999
      Item number: nil
      Contains ordinal indicator: false
      Has number match in list: false
      ⚠️ Single word item penalty when spoken has 2 words
            - Debug FINAL guarantee check:
              - itemCoverageRatio: 1.0
              - hasSignificantPhraseMatch: true
              - commonKeyPhrases.count: 1
              - itemKeyPhrases.count: 1
              - current score: 0.8233666666666667
              - phraseCoverageFromScore: true
      📈 Final score for this item: 0.8233666666666667
      🏆 NEW BEST! Previous best: 0.0 → New best: 0.8233666666666667

   🎯 Evaluating item [1]: 'Emergency Exit'

      🔬 DEBUG: matchScore
         Spoken: 'exit sign'
         Item: 'Emergency Exit'
         Spoken phrases: ["exit", "sign"]
         Item phrases: ["emergency", "exit"]
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'emergency' → 'E562', 'exit' → 'E230'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'emergency' → 'E562', 'sign' → 'S250'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
         Best match for 'emergency': 0.15555555555555553
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'exit' → lemma: 'exit'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'exit' → 'E230', 'sign' → 'S250'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
         Best match for 'exit': 1.0
         📊 Final score calculation:
            - Phrase score: 0.5777777777777777 → boosted: 0.5777777777777777
            - Substring bonus: 0.075
            - Compound bonus: 0.0
            - Partial match bonus: 0.12
            - Specificity bonus: 0.0
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 0.5218055555555555
            - Applied minimum score guarantee (50%+ meaningful coverage → score >= 0.7)
         ✅ Final matchScore: 0.7
      New best variation: 'exit sign' with score: 0.7
      Is ordinal match: false, matched number: nil

      🔬 DEBUG: matchScore
         Spoken: 'exitsign'
         Item: 'Emergency Exit'
         Spoken phrases: ["exitsign"]
         Item phrases: ["emergency", "exit"]
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'exitsign' → lemma: 'exitsign'
               🔊 Phonetic: 'emergency' → 'E562', 'exitsign' → 'E232'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.5, Combined: 0.2277777777777778
         Best match for 'emergency': 0.2277777777777778
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'exitsign' → lemma: 'exitsign'
               🔊 Phonetic: 'exit' → 'E230', 'exitsign' → 'E232'
            📊 Similarity - Text: 0.5, Phonetic: 0.75, Combined: 0.575
         Best match for 'exit': 0.575
         📊 Final score calculation:
            - Phrase score: 0.4013888888888889 → boosted: 0.4013888888888889
            - Substring bonus: 0.0
            - Compound bonus: 0.0
            - Partial match bonus: 0.075
            - Specificity bonus: 0.0
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 0.33590277777777783
         ✅ Final matchScore: 0.33590277777777783
      Item number: nil
      Contains ordinal indicator: false
      Has number match in list: false
            - Debug FINAL guarantee check:
              - itemCoverageRatio: 0.5
              - hasSignificantPhraseMatch: true
              - commonKeyPhrases.count: 1
              - itemKeyPhrases.count: 2
              - current score: 0.68458
              - phraseCoverageFromScore: true
            - Applied FINAL minimum score guarantee (50%+ meaningful coverage → score >= 0.7)
      📈 Final score for this item: 0.7

   🎯 Evaluating item [2]: 'Emergency Exit Sign'

      🔬 DEBUG: matchScore
         Spoken: 'exit sign'
         Item: 'Emergency Exit Sign'
         Spoken phrases: ["exit", "sign"]
         Item phrases: ["exit", "sign", "emergency"]
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'exit' → lemma: 'exit'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'exit' → 'E230', 'sign' → 'S250'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
         Best match for 'exit': 1.0
         📝 Checking item phrase: 'sign' → lemma: 'sign'
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'sign' → 'S250', 'exit' → 'E230'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'sign' → lemma: 'sign'
            ✅ Exact lemma match! Score: 1.0
         Best match for 'sign': 1.0
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'emergency' → 'E562', 'exit' → 'E230'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'emergency' → 'E562', 'sign' → 'S250'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
         Best match for 'emergency': 0.15555555555555553
         📊 Final score calculation:
            - Phrase score: 0.7185185185185184 → boosted: 0.7185185185185184
            - Substring bonus: 0.09999999999999999
            - Compound bonus: 0.0
            - Partial match bonus: 0.12
            - Specificity bonus: 0.24999999999999997
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 0.872037037037037
         ✅ Final matchScore: 0.872037037037037
      New best variation: 'exit sign' with score: 0.872037037037037
      Is ordinal match: false, matched number: nil

      🔬 DEBUG: matchScore
         Spoken: 'exitsign'
         Item: 'Emergency Exit Sign'
         Spoken phrases: ["exitsign"]
         Item phrases: ["exit", "sign", "emergency"]
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'exitsign' → lemma: 'exitsign'
               🔊 Phonetic: 'exit' → 'E230', 'exitsign' → 'E232'
            📊 Similarity - Text: 0.5, Phonetic: 0.75, Combined: 0.575
         Best match for 'exit': 0.575
         📝 Checking item phrase: 'sign' → lemma: 'sign'
            vs spoken phrase: 'exitsign' → lemma: 'exitsign'
               🔊 Phonetic: 'sign' → 'S250', 'exitsign' → 'E232'
            📊 Similarity - Text: 0.5, Phonetic: 0.25, Combined: 0.425
         Best match for 'sign': 0.425
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'exitsign' → lemma: 'exitsign'
               🔊 Phonetic: 'emergency' → 'E562', 'exitsign' → 'E232'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.5, Combined: 0.2277777777777778
         Best match for 'emergency': 0.2277777777777778
         📊 Final score calculation:
            - Phrase score: 0.40925925925925927 → boosted: 0.40925925925925927
            - Substring bonus: 0.0
            - Compound bonus: 0.0
            - Partial match bonus: 0.075
            - Specificity bonus: 0.0
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 0.34101851851851855
         ✅ Final matchScore: 0.34101851851851855
      Item number: nil
      Contains ordinal indicator: false
      Has number match in list: false
      📊 Matching words: 2/3, word count boost applied
            - Debug FINAL guarantee check:
              - itemCoverageRatio: 0.6666666666666666
              - hasSignificantPhraseMatch: true
              - commonKeyPhrases.count: 2
              - itemKeyPhrases.count: 3
              - current score: 0.9926014814814815
              - phraseCoverageFromScore: true
      📈 Final score for this item: 0.9926014814814815
      🏆 NEW BEST! Previous best: 0.8233666666666667 → New best: 0.9926014814814815

   🎯 Evaluating item [3]: 'Emergency Exit Sign - Illuminated'

      🔬 DEBUG: matchScore
         Spoken: 'exit sign'
         Item: 'Emergency Exit Sign - Illuminated'
         Spoken phrases: ["exit", "sign"]
         Item phrases: ["illuminate", "emergency", "sign", "exit"]
         📝 Checking item phrase: 'illuminate' → lemma: 'illuminate'
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'illuminate' → 'I455', 'exit' → 'E230'
            📊 Similarity - Text: 0.19999999999999996, Phonetic: 0.0, Combined: 0.13999999999999996
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'illuminate' → 'I455', 'sign' → 'S250'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.25, Combined: 0.14499999999999996
         Best match for 'illuminate': 0.14499999999999996
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'emergency' → 'E562', 'exit' → 'E230'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.25, Combined: 0.1527777777777778
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'emergency' → 'E562', 'sign' → 'S250'
            📊 Similarity - Text: 0.2222222222222222, Phonetic: 0.0, Combined: 0.15555555555555553
         Best match for 'emergency': 0.15555555555555553
         📝 Checking item phrase: 'sign' → lemma: 'sign'
            vs spoken phrase: 'exit' → lemma: 'exit'
               🔊 Phonetic: 'sign' → 'S250', 'exit' → 'E230'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
            vs spoken phrase: 'sign' → lemma: 'sign'
            ✅ Exact lemma match! Score: 1.0
         Best match for 'sign': 1.0
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'exit' → lemma: 'exit'
            ✅ Exact lemma match! Score: 1.0
            vs spoken phrase: 'sign' → lemma: 'sign'
               🔊 Phonetic: 'exit' → 'E230', 'sign' → 'S250'
            📊 Similarity - Text: 0.0, Phonetic: 0.5, Combined: 0.15
         Best match for 'exit': 1.0
         📊 Final score calculation:
            - Phrase score: 0.5751388888888889 → boosted: 0.5751388888888889
            - Substring bonus: 0.075
            - Compound bonus: 0.0
            - Partial match bonus: 0.12
            - Specificity bonus: 0.0
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 0.5200902777777778
            - Applied minimum score guarantee (50%+ meaningful coverage → score >= 0.7)
         ✅ Final matchScore: 0.7
      New best variation: 'exit sign' with score: 0.7
      Is ordinal match: false, matched number: nil

      🔬 DEBUG: matchScore
         Spoken: 'exitsign'
         Item: 'Emergency Exit Sign - Illuminated'
         Spoken phrases: ["exitsign"]
         Item phrases: ["illuminate", "emergency", "sign", "exit"]
         📝 Checking item phrase: 'illuminate' → lemma: 'illuminate'
            vs spoken phrase: 'exitsign' → lemma: 'exitsign'
               🔊 Phonetic: 'illuminate' → 'I455', 'exitsign' → 'E232'
            📊 Similarity - Text: 0.09999999999999998, Phonetic: 0.0, Combined: 0.06999999999999998
         Best match for 'illuminate': 0.06999999999999998
         📝 Checking item phrase: 'emergency' → lemma: 'emergency'
            vs spoken phrase: 'exitsign' → lemma: 'exitsign'
               🔊 Phonetic: 'emergency' → 'E562', 'exitsign' → 'E232'
            📊 Similarity - Text: 0.11111111111111116, Phonetic: 0.5, Combined: 0.2277777777777778
         Best match for 'emergency': 0.2277777777777778
         📝 Checking item phrase: 'sign' → lemma: 'sign'
            vs spoken phrase: 'exitsign' → lemma: 'exitsign'
               🔊 Phonetic: 'sign' → 'S250', 'exitsign' → 'E232'
            📊 Similarity - Text: 0.5, Phonetic: 0.25, Combined: 0.425
         Best match for 'sign': 0.425
         📝 Checking item phrase: 'exit' → lemma: 'exit'
            vs spoken phrase: 'exitsign' → lemma: 'exitsign'
               🔊 Phonetic: 'exit' → 'E230', 'exitsign' → 'E232'
            📊 Similarity - Text: 0.5, Phonetic: 0.75, Combined: 0.575
         Best match for 'exit': 0.575
         📊 Final score calculation:
            - Phrase score: 0.3244444444444444 → boosted: 0.3244444444444444
            - Substring bonus: 0.0
            - Compound bonus: 0.0
            - Partial match bonus: 0.075
            - Specificity bonus: 0.0
            - Number penalty: 0.0
            - Number mismatch penalty: 1.0
            - Raw final score: 0.28588888888888886
         ✅ Final matchScore: 0.28588888888888886
      Item number: nil
      Contains ordinal indicator: false
      Has number match in list: false
      📊 Matching words: 2/4, word count boost applied
            - Debug FINAL guarantee check:
              - itemCoverageRatio: 0.4
              - hasSignificantPhraseMatch: true
              - commonKeyPhrases.count: 2
              - itemKeyPhrases.count: 4
              - current score: 0.99304
              - phraseCoverageFromScore: true
      📈 Final score for this item: 0.99304
      🏆 NEW BEST! Previous best: 0.9926014814814815 → New best: 0.99304

📊 FINAL RESULTS:
   Best index: 3
   Best score: 0.99304
   Threshold: 0.7
/Users/<USER>/Documents/Drive/Workspaces/Upworks/snapinspect/SnapInspect3-iOS-New/SnapInspect/Tests/AppFeaturesTests/SpeechNormalizerFindBestMatchTests.swift:108: error: -[AppFeaturesTests.SpeechNormalizerFindBestMatchTests testFindBestMatchWithPhrases_ComplexScoring] : XCTAssertEqual failed: ("3") is not equal to ("2") - Should match 'Emergency Exit Sign'
Test Case '-[AppFeaturesTests.SpeechNormalizerFindBestMatchTests testFindBestMatchWithPhrases_ComplexScoring]' failed (0.514 seconds).
Test Suite 'SpeechNormalizerFindBestMatchTests' failed at 2025-07-20 18:38:27.721.
	 Executed 1 test, with 2 failures (0 unexpected) in 0.514 (0.515) seconds
Test Suite 'AppFeaturesTests.xctest' failed at 2025-07-20 18:38:27.722.
	 Executed 1 test, with 2 failures (0 unexpected) in 0.514 (0.515) seconds
Test Suite 'Selected tests' failed at 2025-07-20 18:38:27.722.
	 Executed 1 test, with 2 failures (0 unexpected) in 0.514 (0.516) seconds

Test session results, code coverage, and logs:
	/Users/<USER>/Library/Developer/Xcode/DerivedData/SnapInspect3-djojsytidbeyuvdhmajocgrfbtuh/Logs/Test/Test-AppFeaturesTests-2025.07.20_18-38-20-+0800.xcresult

Testing started
