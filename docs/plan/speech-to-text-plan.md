# Live Speech-to-Text Feature Plan

## Overview
Implementation of real-time speech recognition with subtitle overlay for the SnapInspect3 iOS application.

## Current Problem Analysis
The application needs live speech-to-text capability for inspection documentation and accessibility features.

## Strategy and Approach
Building a comprehensive speech recognition service using Apple's Speech framework with:
- Continuous speech recognition
- Real-time subtitle display
- SRT file generation for export
- Robust error handling and recovery

## Implementation Steps

### ✅ Phase 1: Core Speech Recognition Service
- ✅ Create LiveSpeechRecognitionService with async/await patterns
- ✅ Implement continuous recognition with automatic restarts
- ✅ Add proper error handling for common speech recognition issues
- ✅ **FIXED: Audio engine crash issue** - Resolved AVAudioEngine format validation crash

### ⏳ Phase 2: UI Integration (In Progress)
- ✅ Create SubtitleOverlayView for real-time display
- ⏳ Integrate with inspection workflow
- ⏳ Add start/stop controls
- ⏳ Implement language selection

### ⏳ Phase 3: Export Features
- ⏳ SRT file generation and export
- ⏳ Integration with file management system
- ⏳ Share functionality

### ⏳ Phase 4: Testing & Polish
- ⏳ Device testing on various iOS versions
- ⏳ Performance optimization
- ⏳ Accessibility improvements
- ⏳ Edge case handling

## Recent Critical Fix - Audio Engine Crash ✅

### Problem
The app was crashing with error:
```
URemoteIO.cpp:1091 failed: -10851 (enable 1, outf< 2 ch, 0 Hz, Float32, deinterleaved> inf< 2 ch, 0 Hz, Float32, deinterleaved>)
AVAEInternal.h:71 required condition is false: [AVAEGraphNode.mm:834:CreateRecordingTap: (IsFormatSampleRateAndChannelCountValid(format))]
```

### Root Cause
Invalid audio format (0 Hz sample rate) being returned from `inputNode.outputFormat(forBus: 0)` when audio engine wasn't fully initialized.

### Solution Implemented
1. **Proper Audio Engine Initialization Sequence**: Start audio engine BEFORE accessing input node format
2. **Robust Format Validation**: Check both sample rate AND channel count
3. **Fallback Format Creation**: Use audio session settings as fallback when format is invalid
4. **Better Audio Session Configuration**: Use `.measurement` mode with preferred sample rate
5. **Enhanced Error Handling**: Properly stop audio engine on tap installation failure

### Key Changes Made

#### 1. Audio Session Configuration (lines ~160-175)
```swift
private func configureAudioSession() async throws {
    let audioSession = AVAudioSession.sharedInstance()
    
    do {
        // Configure audio session for speech recognition
        try audioSession.setCategory(.playAndRecord, mode: .measurement, options: [.defaultToSpeaker, .mixWithOthers, .allowBluetooth])
        
        // Set preferred sample rate and buffer duration for optimal speech recognition
        try audioSession.setPreferredSampleRate(44100.0)
        try audioSession.setPreferredIOBufferDuration(0.023) // ~1024 samples at 44.1kHz
        
        // Activate the session
        try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
        
        print("Audio session configured: sample rate \(audioSession.sampleRate), input channels \(audioSession.inputNumberOfChannels)")
        
    } catch {
        print("Failed to configure audio session: \(error.localizedDescription)")
        throw error
    }
}
```

#### 2. Fixed Audio Engine Initialization (lines ~214-287)
```swift
// Prepare and start audio engine FIRST to ensure proper initialization
audioEngine.prepare()
try audioEngine.start()

// Wait for audio engine to fully initialize
try await Task.sleep(nanoseconds: 50_000_000) // 50ms

// Configure audio input with robust format validation
do {
    var recordingFormat = inputNode.outputFormat(forBus: 0)
    
    // CRITICAL: Validate and fix the format to prevent the crash
    if recordingFormat.sampleRate <= 0 || recordingFormat.channelCount == 0 {
        print("Warning: Invalid audio format detected (sample rate: \(recordingFormat.sampleRate), channels: \(recordingFormat.channelCount))")
        
        // Create a proper format as fallback
        let audioSession = AVAudioSession.sharedInstance()
        let sessionSampleRate = audioSession.sampleRate > 0 ? audioSession.sampleRate : 44100.0
        let sessionChannels = audioSession.inputNumberOfChannels > 0 ? audioSession.inputNumberOfChannels : 1
        
        recordingFormat = AVAudioFormat(
            commonFormat: .pcmFormatFloat32,
            sampleRate: sessionSampleRate,
            channels: AVAudioChannelCount(sessionChannels),
            interleaved: false
        ) ?? AVAudioFormat(
            commonFormat: .pcmFormatFloat32,
            sampleRate: 44100.0,
            channels: 1,
            interleaved: false
        )!
        
        print("Using fallback audio format: sample rate \(recordingFormat.sampleRate), channels: \(recordingFormat.channelCount)")
    }
    
    // Double-check the format before installing tap
    guard recordingFormat.sampleRate > 0 && recordingFormat.channelCount > 0 else {
        print("Error: Still invalid audio format after fallback attempt")
        throw LiveSpeechRecognitionError.audioEngineCreationFailed
    }
    
    inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { buffer, _ in
        recognitionRequest.append(buffer)
    }
    
    print("Audio tap installed successfully with format: \(recordingFormat.sampleRate) Hz, \(recordingFormat.channelCount) channels")
    
} catch {
    print("Error installing audio tap: \(error.localizedDescription)")
    // Stop the audio engine if tap installation failed
    if audioEngine.isRunning {
        audioEngine.stop()
    }
    throw LiveSpeechRecognitionError.audioEngineCreationFailed
}
```

## Timeline
- ✅ Week 1-2: Core service implementation and crash fix
- ⏳ Week 3: UI integration and testing
- ⏳ Week 4: Export features and polish

## Risk Assessment
- ✅ **RESOLVED: Audio engine crashes** - Fixed with robust format validation
- ⏳ Device compatibility across iOS versions
- ⏳ Performance impact during long recognition sessions
- ⏳ Speech recognition accuracy in noisy environments

## Success Criteria
- ✅ Stable continuous speech recognition without crashes
- ⏳ Real-time subtitle display with < 1 second latency
- ⏳ Accurate SRT file generation
- ⏳ Seamless integration with existing inspection workflow

## Progress Tracking
- ✅ **Core Speech Service**: Complete with crash fix
- ⏳ **UI Integration**: In progress
- ⏳ **Export Features**: Pending
- ⏳ **Testing & Polish**: Pending

## Related Files
- ✅ `SnapInspect/Sources/AppFeatures/SpeechRecognition/LiveSpeechRecognitionService.swift` - Fixed audio engine crash
- ⏳ `SnapInspect/Sources/AppFeatures/SpeechRecognition/SubtitleOverlayView.swift` - In progress
- ⏳ Integration points in main app controllers - Pending

## Notes
The critical audio engine crash has been resolved with proper initialization sequence and robust format validation. The service now handles edge cases where the audio system returns invalid formats and provides appropriate fallbacks.