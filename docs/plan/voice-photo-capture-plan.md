# Voice-Enabled Photo Capture Plan

## Overview
Implement a live speech-to-text feature for the inspection photo capture workflow that allows users to navigate areas/items via voice commands and automatically attach photos to the correct inspection items.

## Current Problem Analysis
- Manual navigation through inspection areas and items is time-consuming
- Users need to manually select items before taking photos
- No voice control for hands-free operation during inspections
- Need to streamline the photo attachment process

## Strategy and Approach
Leverage the existing `LiveSpeechRecognitionService` infrastructure and extend it to the photo capture module. Create a voice-controlled interface that displays all areas/items and responds to voice commands for navigation and photo capture.

## Implementation Steps

### Stage 1: Voice-Enabled Photo Capture UI (Estimated: 3-4 days)

#### 1.1 UI Implementation (Day 1)
- [ ] Add microphone icon (FontAwesome microchip-ai) to bottom-left of camera interface
- [ ] Create overlay view to display area/item names at the top
- [ ] Implement scrollable list view for areas and items
- [ ] Add highlighting mechanism for voice-matched items
- [ ] Integrate with existing `SCCaptureCameraController`

#### 1.2 Speech Recognition Integration (Day 1-2)
- [ ] Extend `LiveSpeechRecognitionService` for photo capture context
- [ ] Implement real-time transcription display at bottom
- [ ] Create speech-to-text matching logic for area/item names
- [ ] Handle number conversion (1,2,3 → one,two,three)
- [ ] Add fuzzy matching for voice recognition accuracy

#### 1.3 Data Loading & Navigation (Day 2)
- [ ] Load all area names for current inspection
- [ ] Implement dynamic item loading when area changes
- [ ] Create voice command parser for area/item selection
- [ ] Highlight matched areas/items based on speech
- [ ] Maintain manual swipe/selection capability

#### 1.4 Photo Attachment Logic (Day 3)
- [ ] Auto-attach photos to first photo control of selected item
- [ ] Implement photo control detection (oControl1-6)
- [ ] Handle bulk photo scenarios
- [ ] Update UI to show attachment confirmation

#### 1.5 Testing & Polish (Day 3-4)
- [ ] Test voice recognition accuracy
- [ ] Optimize UI performance with large item lists
- [ ] Handle edge cases (no matches, multiple matches)
- [ ] Ensure backward compatibility with manual workflow

### Stage 2: Database & SRT Storage (Future - 2-3 days)
- [ ] Create SRT sentence tracking table
- [ ] Add bProcessed flag for server sync
- [ ] Implement SRT file generation and upload
- [ ] Add reference to inspection ID and item ID

### Stage 3: AI Integration (Future - 3-4 days)
- [ ] Add bAIApproval column to items
- [ ] Implement AI icon display for unverified items
- [ ] Create background listener for server responses
- [ ] Handle AI action triggers and data updates
- [ ] Implement scroll-down approval mechanism

## Timeline
- **Stage 1**: 3-4 days (immediate implementation)
- **Stage 2**: 2-3 days (after Stage 1 completion)
- **Stage 3**: 3-4 days (requires server API development)

Total estimated time: 8-11 days for full implementation

## Risk Assessment
1. **Voice Recognition Accuracy**: 
   - Risk: Poor matching in noisy environments
   - Mitigation: Implement fuzzy matching and manual override

2. **Performance with Large Item Lists**:
   - Risk: UI lag with hundreds of items
   - Mitigation: Implement pagination or virtualized lists

3. **Integration Complexity**:
   - Risk: Conflicts with existing photo workflow
   - Mitigation: Maintain backward compatibility, phase rollout

## Success Criteria
- [ ] Voice commands successfully navigate areas/items
- [ ] Photos automatically attach to correct items
- [ ] Real-time speech display with < 1s latency
- [ ] 90%+ voice matching accuracy in quiet environments
- [ ] No disruption to existing manual workflow

## Related Files
### Core Components to Modify:
- `SCCaptureCameraController.{h,m}` - Main camera interface
- `LiveSpeechRecognitionService.swift` - Speech recognition
- `if_Inspection_3rd_Items.{h,m}` - Item display logic
- `if_Inspection_3rd_Items_ViewModel.{h,m}` - Item data management

### Data Models:
- `O_Area.{h,m}` - Area entity
- `O_Item.{h,m}` - Item entity  
- `O_InsItem.{h,m}` - Inspection item entity
- `O_Photo.{h,m}` - Photo entity

### New Components to Create:
- `VoicePhotoCaptureOverlay` - UI overlay for areas/items
- `VoiceCommandMatcher` - Speech to area/item matching
- `PhotoAutoAttachment` - Auto-attachment logic

## Progress Tracking
### Stage 1 Progress: ⏳ In Progress
- [x] UI Implementation - Added floating microphone button (extracted to CommonUI)
- [ ] Speech Recognition Integration  
- [ ] Data Loading & Navigation
- [ ] Photo Attachment Logic
- [ ] Testing & Polish

### Technical Notes
- Floating action button implemented in CommonUI for reusability
- Using "iconAssistant" image asset
- Button positioned at bottom-right (16px padding) with 56x56 size
- Applied to both if_Inspection_3rd.m and if_Inspection_3rd_Items.m

Last Updated: 2025-01-07