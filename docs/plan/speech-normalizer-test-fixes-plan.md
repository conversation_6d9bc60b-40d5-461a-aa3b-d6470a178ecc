# SpeechNormalizer Test Fixes Plan

## Overview
Fix failing tests in SpeechNormalizerFindBestMatchTests to ensure proper matching behavior for key phrase extraction, complex scoring, ordinal matching, and homophone handling.

## Current Problem Analysis

### Key Phrase Extraction Issue
- **Test**: `testFindBestMatchWithPhrases_KeyPhraseExtraction`
- **Problem**: "hvac system in the mechanical room needs service" should match "HVAC System - Mechanical Room" (index 1) but matches "HVAC System" (index 0)
- **Root Cause**: The scoring logic doesn't properly prioritize items that contain more of the spoken key phrases when the spoken text has additional context

### Complex Scoring Issue  
- **Test**: `testFindBestMatchWithPhrases_ComplexScoring`
- **Problem**: "emergency exit sign illuminated and functioning" should match "Emergency Exit Sign - Illuminated" (index 3) but matches "Emergency Exit Sign" (index 2)
- **Root Cause**: The hierarchical item scoring logic doesn't properly handle items with additional descriptive terms

### Ordinal Matching Issues
- **Test**: `testFindBestMatchWithPhrases_OrdinalMatchingPatternsExtended`
- **Problem**: Multiple compound patterns and time vs item number confusion
- **Root Cause**: The ordinal matching logic doesn't properly handle complex patterns and context

### Homophone Confusion Issue
- **Test**: `testFindBestMatchWithPhrases_HomophoneConfusion`
- **Problem**: "went to bedroom too but meant bedroom 3" should match "Bedroom 3" but matches "Bedroom 2"
- **Root Cause**: The homophone handling logic doesn't properly prioritize later clear numbers

### Embedding Tests Issue
- **Test**: `testFindBestMatchWithEmbedding_SemanticMatching`
- **Problem**: Tests fail due to iOS version availability
- **Root Cause**: Tests don't properly handle iOS version requirements

## Strategy and Approach

### Phase 1: Fix Key Phrase Extraction and Complex Scoring
1. Enhance key phrase matching logic to prefer items with more matching phrases
2. Improve hierarchical item scoring for items with additional descriptive terms
3. Add better phrase coverage analysis

### Phase 2: Fix Ordinal Matching Logic
1. Enhance compound pattern recognition
2. Improve context-aware number extraction
3. Fix time vs item number confusion

### Phase 3: Fix Homophone Handling
1. Improve homophone conversion logic
2. Add better context analysis for number precedence
3. Enhance phonetic confusion handling

### Phase 4: Fix Embedding Tests
1. Add proper iOS version checks
2. Handle unsupported language cases
3. Improve fallback behavior

## Implementation Steps

### Phase 1: Key Phrase and Complex Scoring Fixes ⏳
- [ ] Enhance key phrase extraction logic in `findBestMatchWithPhrases`
- [ ] Improve hierarchical item scoring for specificity
- [ ] Add phrase coverage recovery logic
- [ ] Test key phrase extraction fixes

### Phase 2: Ordinal Matching Fixes ⏳
- [ ] Enhance compound noun extraction in `OrdinalMatcher`
- [ ] Improve pattern recognition for complex cases
- [ ] Fix time vs item number context analysis
- [ ] Test ordinal matching fixes

### Phase 3: Homophone Handling Fixes ⏳
- [ ] Improve homophone conversion logic
- [ ] Add better context analysis for number precedence
- [ ] Enhance phonetic confusion patterns
- [ ] Test homophone handling fixes

### Phase 4: Embedding Test Fixes ⏳
- [ ] Add proper iOS version availability checks
- [ ] Handle unsupported language cases
- [ ] Improve fallback behavior for embedding tests
- [ ] Test embedding functionality

### Phase 5: Integration and Testing ⏳
- [ ] Run all tests to ensure fixes work together
- [ ] Verify no regressions in existing functionality
- [ ] Test edge cases and performance
- [ ] Final validation

## Timeline
- **Phase 1**: 1-2 hours
- **Phase 2**: 1-2 hours  
- **Phase 3**: 1 hour
- **Phase 4**: 30 minutes
- **Phase 5**: 30 minutes
- **Total**: 4-6 hours

## Risk Assessment
- **Low Risk**: Most fixes involve enhancing existing logic rather than major refactoring
- **Medium Risk**: Ordinal matching changes could affect existing functionality
- **Mitigation**: Comprehensive testing after each phase

## Success Criteria
- All failing tests pass
- No regressions in existing functionality
- Improved matching accuracy for edge cases
- Better handling of complex speech patterns

## Progress Tracking
- ✅ Plan created
- ⏳ Phase 1: Key Phrase and Complex Scoring Fixes
- ⏳ Phase 2: Ordinal Matching Fixes  
- ⏳ Phase 3: Homophone Handling Fixes
- ⏳ Phase 4: Embedding Test Fixes
- ⏳ Phase 5: Integration and Testing

## Related Files
- `SnapInspect/Sources/AppFeatures/SpeechRecognition/SpeechNormalizer.swift`
- `SnapInspect/Sources/AppFeatures/SpeechRecognition/OrdinalMatcher.swift`
- `SnapInspect/Tests/AppFeaturesTests/SpeechNormalizerFindBestMatchTests.swift`
- `SnapInspect/Tests/AppFeaturesTests/OrdinalMatcherTests.swift` 